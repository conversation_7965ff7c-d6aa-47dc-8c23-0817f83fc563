package com.kdsjkj.controller;

import com.kdsjkj.entity.AlipayConfigBackend;
import com.kdsjkj.service.IAlipayConfigBackendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝配置诊断控制器
 * 用于诊断和验证支付宝配置
 */
@Slf4j
@RestController
@RequestMapping("/api/alipay/config")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class AlipayConfigController {

    @Autowired
    private IAlipayConfigBackendService alipayConfigBackendService;

    /**
     * 检查支付宝配置
     * @return 配置检查结果
     */
    @GetMapping("/check")
    public Map<String, Object> checkConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始检查支付宝配置...");
            
            // 获取数据库配置
            AlipayConfigBackend config = alipayConfigBackendService.getLatestConfig();
            
            if (config == null) {
                result.put("success", false);
                result.put("message", "数据库中未找到支付宝配置");
                return result;
            }
            
            Map<String, Object> configInfo = new HashMap<>();
            configInfo.put("id", config.getId());
            configInfo.put("serverUrl", config.getServerUrl());
            configInfo.put("appId", config.getAppId());
            
            // 检查私钥
            Map<String, Object> privateKeyCheck = checkPrivateKey(config.getPrivateKey());
            configInfo.put("privateKey", privateKeyCheck);
            
            // 检查公钥
            Map<String, Object> publicKeyCheck = checkPublicKey(config.getAlipayPublicKey());
            configInfo.put("publicKey", publicKeyCheck);
            
            result.put("success", true);
            result.put("message", "配置检查完成");
            result.put("config", configInfo);
            
        } catch (Exception e) {
            log.error("配置检查异常", e);
            result.put("success", false);
            result.put("message", "配置检查异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查应用私钥
     */
    private Map<String, Object> checkPrivateKey(String privateKey) {
        Map<String, Object> check = new HashMap<>();
        
        if (privateKey == null || privateKey.trim().isEmpty()) {
            check.put("valid", false);
            check.put("message", "私钥为空");
            return check;
        }
        
        try {
            // 检查私钥长度
            check.put("length", privateKey.length());
            check.put("isEmpty", privateKey.trim().isEmpty());
            
            // 检查是否包含BEGIN标记
            boolean hasBegin = privateKey.contains("-----BEGIN");
            check.put("hasBegin", hasBegin);
            
            // 检查是否包含END标记
            boolean hasEnd = privateKey.contains("-----END");
            check.put("hasEnd", hasEnd);
            
            // 检查是否包含RSA标记
            boolean hasRSA = privateKey.contains("RSA");
            check.put("hasRSA", hasRSA);
            
            // 检查是否包含PRIVATE KEY标记
            boolean hasPrivateKey = privateKey.contains("PRIVATE KEY");
            check.put("hasPrivateKey", hasPrivateKey);
            
            // 显示私钥前100个字符（用于调试）
            String preview = privateKey.length() > 100 ? 
                privateKey.substring(0, 100) + "..." : privateKey;
            check.put("preview", preview);
            
            // 检查是否包含特殊字符
            boolean hasSpecialChars = privateKey.contains("\\n") || privateKey.contains("\\r");
            check.put("hasSpecialChars", hasSpecialChars);
            
            // 格式化建议
            String suggestion = "";
            if (!hasBegin || !hasEnd) {
                suggestion = "建议添加BEGIN和END标记";
            } else if (!hasRSA || !hasPrivateKey) {
                suggestion = "建议使用RSA PRIVATE KEY格式";
            } else if (hasSpecialChars) {
                suggestion = "建议清理特殊字符";
            } else {
                suggestion = "格式看起来正确";
            }
            check.put("suggestion", suggestion);
            
            check.put("valid", hasBegin && hasEnd && hasRSA && hasPrivateKey);
            check.put("message", suggestion);
            
        } catch (Exception e) {
            check.put("valid", false);
            check.put("message", "私钥检查异常: " + e.getMessage());
        }
        
        return check;
    }

    /**
     * 检查支付宝公钥
     */
    private Map<String, Object> checkPublicKey(String publicKey) {
        Map<String, Object> check = new HashMap<>();
        
        if (publicKey == null || publicKey.trim().isEmpty()) {
            check.put("valid", false);
            check.put("message", "公钥为空");
            return check;
        }
        
        try {
            // 检查公钥长度
            check.put("length", publicKey.length());
            check.put("isEmpty", publicKey.trim().isEmpty());
            
            // 检查是否包含BEGIN标记
            boolean hasBegin = publicKey.contains("-----BEGIN");
            check.put("hasBegin", hasBegin);
            
            // 检查是否包含END标记
            boolean hasEnd = publicKey.contains("-----END");
            check.put("hasEnd", hasEnd);
            
            // 检查是否包含PUBLIC KEY标记
            boolean hasPublicKey = publicKey.contains("PUBLIC KEY");
            check.put("hasPublicKey", hasPublicKey);
            
            // 显示公钥前100个字符（用于调试）
            String preview = publicKey.length() > 100 ? 
                publicKey.substring(0, 100) + "..." : publicKey;
            check.put("preview", preview);
            
            // 检查是否包含特殊字符
            boolean hasSpecialChars = publicKey.contains("\\n") || publicKey.contains("\\r");
            check.put("hasSpecialChars", hasSpecialChars);
            
            // 格式化建议
            String suggestion = "";
            if (!hasBegin || !hasEnd) {
                suggestion = "建议添加BEGIN和END标记";
            } else if (!hasPublicKey) {
                suggestion = "建议使用PUBLIC KEY格式";
            } else if (hasSpecialChars) {
                suggestion = "建议清理特殊字符";
            } else {
                suggestion = "格式看起来正确";
            }
            check.put("suggestion", suggestion);
            
            check.put("valid", hasBegin && hasEnd && hasPublicKey);
            check.put("message", suggestion);
            
        } catch (Exception e) {
            check.put("valid", false);
            check.put("message", "公钥检查异常: " + e.getMessage());
        }
        
        return check;
    }

    /**
     * 格式化密钥
     */
    @PostMapping("/format")
    public Map<String, Object> formatKeys() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            AlipayConfigBackend config = alipayConfigBackendService.getLatestConfig();
            
            if (config == null) {
                result.put("success", false);
                result.put("message", "数据库中未找到支付宝配置");
                return result;
            }
            
            // 格式化私钥
            String formattedPrivateKey = formatPrivateKey(config.getPrivateKey());
            
            // 格式化公钥
            String formattedPublicKey = formatPublicKey(config.getAlipayPublicKey());
            
            // 更新配置
            config.setPrivateKey(formattedPrivateKey);
            config.setAlipayPublicKey(formattedPublicKey);
            
            boolean success = alipayConfigBackendService.updateById(config);
            
            if (success) {
                result.put("success", true);
                result.put("message", "密钥格式化成功");
                result.put("privateKeyLength", formattedPrivateKey.length());
                result.put("publicKeyLength", formattedPublicKey.length());
            } else {
                result.put("success", false);
                result.put("message", "密钥格式化失败");
            }
            
        } catch (Exception e) {
            log.error("密钥格式化异常", e);
            result.put("success", false);
            result.put("message", "密钥格式化异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 格式化私钥
     */
    private String formatPrivateKey(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            throw new RuntimeException("私钥为空");
        }
        
        // 清理特殊字符
        String cleaned = privateKey.replace("\\n", "\n")
                                 .replace("\\r", "")
                                 .replace("\\t", "")
                                 .trim();
        
        // 如果已经包含BEGIN和END标记，直接返回
        if (cleaned.contains("-----BEGIN") && cleaned.contains("-----END")) {
            return cleaned;
        }
        
        // 添加BEGIN和END标记
        return "-----BEGIN RSA PRIVATE KEY-----\n" + cleaned + "\n-----END RSA PRIVATE KEY-----";
    }

    /**
     * 格式化公钥
     */
    private String formatPublicKey(String publicKey) {
        if (publicKey == null || publicKey.trim().isEmpty()) {
            throw new RuntimeException("公钥为空");
        }
        
        // 清理特殊字符
        String cleaned = publicKey.replace("\\n", "\n")
                                 .replace("\\r", "")
                                 .replace("\\t", "")
                                 .trim();
        
        // 如果已经包含BEGIN和END标记，直接返回
        if (cleaned.contains("-----BEGIN") && cleaned.contains("-----END")) {
            return cleaned;
        }
        
        // 添加BEGIN和END标记
        return "-----BEGIN PUBLIC KEY-----\n" + cleaned + "\n-----END PUBLIC KEY-----";
    }
} 