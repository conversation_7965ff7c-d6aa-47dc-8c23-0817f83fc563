package com.kdsjkj.controller;

import com.kdsjkj.entity.AlipayConfigBackend;
import com.kdsjkj.service.IAlipayConfigBackendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付宝配置管理控制器
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/api/alipay/config")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class AlipayConfigController {

    @Autowired
    private IAlipayConfigBackendService alipayConfigBackendService;

    /**
     * 保存支付宝配置
     */
    @PostMapping("/save")
    public Map<String, Object> saveConfig(@RequestBody AlipayConfigBackend config) {
        Map<String, Object> response = new HashMap<>();
        try {
            log.info("保存支付宝配置: {}", config);
            boolean success = alipayConfigBackendService.save(config);
            if (success) {
                response.put("success", true);
                response.put("message", "配置保存成功");
                response.put("data", config);
            } else {
                response.put("success", false);
                response.put("message", "配置保存失败");
            }
        } catch (Exception e) {
            log.error("保存配置异常", e);
            response.put("success", false);
            response.put("message", "保存失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 获取最新配置
     */
    @GetMapping("/latest")
    public Map<String, Object> getLatestConfig() {
        Map<String, Object> response = new HashMap<>();
        try {
            AlipayConfigBackend config = alipayConfigBackendService.getLatestConfig();
            response.put("success", true);
            response.put("data", config);
            response.put("message", config != null ? "获取配置成功" : "暂无配置");
        } catch (Exception e) {
            log.error("获取配置异常", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 获取所有配置
     */
    @GetMapping("/list")
    public Map<String, Object> getAllConfigs() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<AlipayConfigBackend> configs = alipayConfigBackendService.list();
            response.put("success", true);
            response.put("data", configs);
            response.put("message", "获取配置列表成功");
        } catch (Exception e) {
            log.error("获取配置列表异常", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 更新配置
     */
    @PutMapping("/update")
    public Map<String, Object> updateConfig(@RequestBody AlipayConfigBackend config) {
        Map<String, Object> response = new HashMap<>();
        try {
            log.info("更新支付宝配置: {}", config);
            boolean success = alipayConfigBackendService.updateById(config);
            if (success) {
                response.put("success", true);
                response.put("message", "配置更新成功");
                response.put("data", config);
            } else {
                response.put("success", false);
                response.put("message", "配置更新失败");
            }
        } catch (Exception e) {
            log.error("更新配置异常", e);
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteConfig(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            log.info("删除支付宝配置: {}", id);
            boolean success = alipayConfigBackendService.removeById(id);
            if (success) {
                response.put("success", true);
                response.put("message", "配置删除成功");
            } else {
                response.put("success", false);
                response.put("message", "配置删除失败");
            }
        } catch (Exception e) {
            log.error("删除配置异常", e);
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
        }
        return response;
    }
} 