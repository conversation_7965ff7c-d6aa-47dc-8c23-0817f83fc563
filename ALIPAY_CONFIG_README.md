# 支付宝配置管理功能

## 功能说明

该功能允许通过数据库动态管理支付宝配置，而不需要硬编码在代码中。

## 数据库表结构

```sql
CREATE TABLE IF NOT EXISTS `alipay_config_backend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `server_url` varchar(255) NOT NULL COMMENT '支付宝网关地址',
  `app_id` varchar(50) NOT NULL COMMENT '支付宝应用ID',
  `private_key` text NOT NULL COMMENT '应用私钥',
  `alipay_public_key` text NOT NULL COMMENT '支付宝公钥',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='后台支付宝配置表';
```

## API 接口

### 1. 保存配置
- **接口**: `POST /api/alipay/config/save`
- **说明**: 保存新的支付宝配置
- **请求体**:
```json
{
  "serverUrl": "https://openapi.alipay.com/gateway.do",
  "appId": "你的AppId",
  "privateKey": "你的应用私钥",
  "alipayPublicKey": "支付宝公钥"
}
```

### 2. 获取最新配置
- **接口**: `GET /api/alipay/config/latest`
- **说明**: 获取最新的支付宝配置（按ID降序）

### 3. 获取所有配置
- **接口**: `GET /api/alipay/config/list`
- **说明**: 获取所有支付宝配置列表

### 4. 更新配置
- **接口**: `PUT /api/alipay/config/update`
- **说明**: 更新指定的支付宝配置
- **请求体**:
```json
{
  "id": 1,
  "serverUrl": "https://openapi.alipay.com/gateway.do",
  "appId": "更新后的AppId",
  "privateKey": "更新后的应用私钥",
  "alipayPublicKey": "更新后的支付宝公钥"
}
```

### 5. 删除配置
- **接口**: `DELETE /api/alipay/config/delete/{id}`
- **说明**: 删除指定ID的支付宝配置

## 使用方式

1. **初始化数据库**: 执行 `init_alipay_config.sql` 脚本创建表结构
2. **添加配置**: 使用保存接口添加你的支付宝配置
3. **自动使用**: UserController 会自动从数据库读取最新配置

## 文件说明

- `AlipayConfigBackend.java`: 实体类
- `AlipayConfigBackendMapper.java`: MyBatis-Plus Mapper接口
- `IAlipayConfigBackendService.java`: 服务接口
- `AlipayConfigBackendServiceImpl.java`: 服务实现类
- `AlipayConfigController.java`: 配置管理控制器
- `UserController.java`: 修改后的用户控制器（从数据库读取配置）

## 注意事项

1. 确保数据库连接正常
2. 私钥和公钥信息请妥善保管
3. 生产环境中建议对敏感信息进行加密存储
4. 如果数据库中没有配置，系统会使用默认的占位符配置 