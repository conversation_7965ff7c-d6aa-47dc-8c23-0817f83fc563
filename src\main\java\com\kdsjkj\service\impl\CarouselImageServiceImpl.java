package com.kdsjkj.service.impl;

import com.kdsjkj.entity.CarouselImage;
import com.kdsjkj.mapper.CarouselImageMapper;
import com.kdsjkj.service.ICarouselImageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 轮播图表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class CarouselImageServiceImpl extends ServiceImpl<CarouselImageMapper, CarouselImage> implements ICarouselImageService {

    @Override
    public List<CarouselImage> getCarouselImagesByAppid(String appid) {
        QueryWrapper<CarouselImage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("appid", appid)
                   .eq("status", true) // 只查询启用状态的轮播图
                   .orderByAsc("sort_order") // 按排序字段升序
                   .orderByDesc("created_time"); // 再按创建时间降序
        return this.list(queryWrapper);
    }
}
