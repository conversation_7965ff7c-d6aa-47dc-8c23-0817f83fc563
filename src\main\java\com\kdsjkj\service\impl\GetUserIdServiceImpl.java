package com.kdsjkj.service.impl;

import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.kdsjkj.entity.GetUserId;
import com.kdsjkj.mapper.GetUserIdMapper;
import com.kdsjkj.service.GetUserIdService;
import com.kdsjkj.appTest.userID.AlipaySystemOauthToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

/**
 * 获取用户ID相关配置服务实现类
 */
@Slf4j
@Service
public class GetUserIdServiceImpl implements GetUserIdService {
    
    @Autowired
    private GetUserIdMapper getUserIdMapper;
    
    @Autowired
    private AlipaySystemOauthToken alipaySystemOauthToken;
    
    @Override
    public void saveConfig(GetUserId getUserId) {
        getUserIdMapper.insert(getUserId);
    }
    
    @Override
    public GetUserId getLatestByAppId(String appId) {
        return getUserIdMapper.getLatestByAppId(appId);
    }
    
    @Override
    public GetUserId getByAppAuthToken(String appAuthToken) {
        return getUserIdMapper.getByAppAuthToken(appAuthToken);
    }
    
    @Override
    public GetUserId getByUserId(String userId) {
        return getUserIdMapper.getByUserId(userId);
    }
    
    @Override
    public GetUserId processAuthCode(String authCode, String appAuthToken) {
        try {
            // 获取或创建配置记录
            GetUserId config = getByAppAuthToken(appAuthToken);
            if (config == null) {
                // 如果没有找到配置，创建新的配置记录
                config = createDefaultConfig(appAuthToken);
                saveConfig(config);
            }
            
            // 设置授权码
            config.setAuthCode(authCode);
            getUserIdMapper.updateById(config);
            
            // 调用支付宝API获取访问令牌
            AlipaySystemOauthTokenResponse response = alipaySystemOauthToken.getAccessToken(authCode);
            
            if (response.isSuccess()) {
                // 更新访问令牌信息
                updateTokenInfo(config.getId(), response);
                
                // 重新查询更新后的记录
                config = getUserIdMapper.selectById(config.getId());
                log.info("成功获取用户访问令牌，用户ID: {}", response.getUserId());
            } else {
                // 更新错误信息
                updateErrorInfo(config.getId(), response.getSubCode(), response.getSubMsg());
                log.error("获取访问令牌失败: {}, {}", response.getSubCode(), response.getSubMsg());
            }
            
            return config;
            
        } catch (Exception e) {
            log.error("处理授权码异常", e);
            throw new RuntimeException("处理授权码失败: " + e.getMessage());
        }
    }
    
    @Override
    public void updateTokenInfo(Long id, AlipaySystemOauthTokenResponse response) {
        getUserIdMapper.updateTokenInfo(
            id,
            response.getAccessToken(),
            response.getUserId(),
            Long.valueOf(response.getExpiresIn()),
            response.getRefreshToken(),
            Long.valueOf(response.getReExpiresIn()),
            response.getBody()
        );
    }
    
    @Override
    public void updateErrorInfo(Long id, String errorCode, String errorMsg) {
        getUserIdMapper.updateErrorInfo(id, errorCode, errorMsg, "FAILED");
    }
    
    /**
     * 创建默认配置
     */
    private GetUserId createDefaultConfig(String appAuthToken) {
        GetUserId config = new GetUserId();
        config.setAppId("2021005163695273");
        config.setAppAuthToken(appAuthToken);
        config.setPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC22iPz+K4hYmN7JlrgLz7v9jHgMVNbtpIAcNO2iuFEQU/cw8Uovl/6ZWNT0FRrlBYNfG7RyRSWeIDmjRhq7PRSp4aGhTUNVQXzGrP8B4ZkKIxcv3NCdM4jbTTibUSNZSOxQpCpObH1YkPloVsyf1rwR/iKytO77aSzRunvkbEf8g1qMygdUQr1jmcNYq+FtKvQihxKgb4/6aR+/V2KcItYplUerc2LRPARLjMryUtMQ0sbIiGd9n+TgAyOjDDWeLTDZCuD0ULOL61lyW8Pb8sgb8EsxXyBilxmEqrw2b+0k/P5baETa3lBKwsYS08hGKxfhYafBCMBKfDtwLxVOlYtAgMBAAECggEAJTBOmaMdPzr0gqp1Vx0kzscis7c0ffWu0hogZQLv9+LUrFDAu52khZezUOuRD+QpbAq/uzkLT+MoTDusGKsiseDSkm9rL8/4Cs6Ghp69LYe1rHNfrMd22WuvktGoXJ2SWc3xAAn08LRQnNZpZubSBwaMPSYtVA5sLe4hTBE0lgtbeiiFLquqNS9exwxVnUgEV+oNucfuwrGyM5NVR7iJs6ToHtdIcC0WcdXg9gbpnk2FiCOfmPUMY6Qp9F2Q7ZT2EI2MuxGvRFhRrO4z9/ogE4vzUxx6NdFs9NsZQiO2erh10eh7HLa+LKP8F5pZk9WOMgQ0BtABGg3zb7Rf845PYQKBgQDp+5urn3NQXQTrnzYCuJsnMHtGHbL5ajpolthftkOglJ0CWI4Xbjfs3XBJOqLcWU2pyGJZ84em5mGDCfZx4q8KUiGtnfPKa5abM8PUi50oIRdkQfVOgZf2/tHGHyRbKG8qKZZIN2yQussGbgS/dVTN5WH9RQKnoICYBhbxv3Uk1QKBgQDIDtlC2Z4GLN/831Xl6KEaJasMQ1Lvk2UefzD6ns4HtMF6MKJKDONmXYwxWkSwpkQq7FuRG98yPayOTy58zZudsuUWHVz5WsAi3JNd5YXtCzbP52EL04K3VO3/W41kuy5SzYzE1Ua95h15Ri70CgvG0r+utRmz4ZeNiWIonsD3+QKBgQCXt6U+CHseLeKl4auE4i0AoUgds948KaxL6xvMHAQH2LViMweXm8k5lnBbm2DE3kckgyF60BUynIm4DTq2p9T2LUFXMWdF5rNWWZ9ub0TtI1/mi3pDeHaQGrTs8X6CeVpzjVm74+xKgERrG+WKO8tMYqNCNYA+Ly2S5cf04z9TLQKBgGlGXw/tolmPjcLs56/XbcdroQoP93m475JPzj9kB/lIJjQdmZksFRIOEjL6VIE8zUxlGn5O03IFwGMWydjE/anLtP+hLgbNuaPevPHBUWt+5jYbhRcLE5NT7vXcgJhMY8ERB42gydwuVr41r4meK7pRtqrCBCeDhBPoAq5rdBJJAoGAB2HYFQqZ/AIRmdcCYQ3XboAN0txJ/NHKcmx77Hmf5LNJ50ELX8v7aEUXzHxRhk/5B75G8yRoxhd/uOi3aOs6LhPmRSANBSVZPSimlqJMmaLsyBhiQatJlOHMjAjSK3oHSvoAE0kgmAWVl5QQON3F0NN/qe3faPIx725Em6RZXcs=");
        config.setAlipayPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwW0Ug2JjoYNUhhfxdPuQlzy3YXgdQs9R3xMXQuhYfuMUMM6D39YbI9YxRDQzl3f6ZjPF44iHNWOSE5pYr/WHNQQslPxifMpX4dqz5lHiasm18o0yY0xHsZPMJr6yoBQN+5MMGDZx3XqIvGRl2vRcAy2ueUiqLCPhaW+R/g4/PlqCvZHPknYekGmuS6zKSdms3cTOdZC+exaVZI83/pOUYF5gpGkebzP4r3GTRmZPWSVhm33B3Z1oLn3nf7Ex/pVoqHRGsQk+P+FxDonWbk0fx3GEE5uOsuY3tfr8wqroVz2V5L6rnL7YqNqu/kbgVrKkkGBiVh/kN0iDZl9BMYHARQIDAQAB");
        config.setServerUrl("https://openapi.alipay.com/gateway.do");
        config.setFormat("json");
        config.setCharset("UTF-8");
        config.setSignType("RSA2");
        config.setGrantType("authorization_code");
        config.setStatus("ACTIVE");
        config.setCreateTime(new Timestamp(System.currentTimeMillis()));
        config.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        
        return config;
    }
} 