# 支付宝应用网关配置说明

## 概述

应用网关是支付宝为开发者提供的消息推送服务，可以接收支付宝系统推送的各种事件通知，如用户授权、交易状态变更等。

## 配置步骤

### 1. 支付宝开放平台配置

1. 登录支付宝开放平台：https://open.alipay.com/
2. 进入你的应用详情页面
3. 找到"应用网关"配置项
4. 填入网关地址：`https://yourdomain.com/api/alipay/gateway`
5. 配置验证Token（可选）
6. 配置AES密钥（用于消息加密，可选）

### 2. 本地配置

在 `application-dev.yml` 或 `application-prod.yml` 中配置：

```yaml
alipay:
  # 基础配置
  app-id: 2021005163695273
  private-key: YOUR_PRIVATE_KEY
  alipay-public-key: YOUR_ALIPAY_PUBLIC_KEY
  
  # 应用网关配置
  app-gateway-url: http://127.0.0.1:8080/api/alipay/gateway
  app-gateway-token: your_gateway_token_here
  app-gateway-aes-key: your_aes_key_here
```

### 3. 环境变量配置（生产环境推荐）

```bash
export ALIPAY_PRIVATE_KEY="YOUR_PRIVATE_KEY"
export ALIPAY_GATEWAY_URL="https://yourdomain.com/api/alipay/gateway"
export ALIPAY_GATEWAY_TOKEN="your_gateway_token"
export ALIPAY_GATEWAY_AES_KEY="your_aes_key"
```

## 接口说明

### 主要接口

1. **应用网关接口**: `POST /api/alipay/gateway`
   - 接收支付宝推送的消息
   - 自动验证签名
   - 支持消息解密

2. **授权码接口**: `POST /api/alipay/auth`
   - 接收小程序授权码
   - 获取用户信息

3. **测试接口**: `GET /api/alipay/test/config`
   - 查看当前配置状态
   - 验证配置是否正确

### 测试接口

1. **配置检查**: `GET /api/alipay/test/config`
   ```json
   {
     "appId": "2021005163695273",
     "signType": "RSA2",
     "charset": "UTF-8",
     "format": "json",
     "gatewayUrl": "https://openapi.alipay.com/gateway.do",
     "appGatewayUrl": "http://127.0.0.1:8080/api/alipay/gateway",
     "privateKeyConfigured": true,
     "publicKeyConfigured": true,
     "gatewayTokenConfigured": false,
     "aesKeyConfigured": false
   }
   ```

2. **加密测试**: `POST /api/alipay/test/encrypt`
   ```bash
   curl -X POST "http://127.0.0.1:8080/api/alipay/test/encrypt" \
        -d "data=Hello World"
   ```

3. **模拟网关**: `POST /api/alipay/test/mockGateway`
   - 用于测试消息接收和处理

## 消息类型

目前支持的消息类型：

1. **user_auth_notify**: 用户授权通知
2. **trade_status_sync**: 交易状态同步

## 安全特性

1. **签名验证**: 自动验证支付宝推送消息的签名
2. **消息加密**: 支持AES加密的消息内容解密
3. **错误处理**: 完善的异常处理和日志记录

## 部署注意事项

1. **HTTPS要求**: 生产环境应用网关地址必须使用HTTPS
2. **域名配置**: 确保域名可以被支付宝服务器访问
3. **防火墙设置**: 开放相应端口（如8080或443）
4. **日志监控**: 建议监控应用网关接口的访问日志

## 故障排查

### 常见问题

1. **签名验证失败**
   - 检查私钥和公钥配置
   - 确认使用RSA2签名类型

2. **消息解密失败**
   - 检查AES密钥配置
   - 确认密钥格式正确

3. **网关无法访问**
   - 检查网络连通性
   - 确认域名解析正确
   - 检查防火墙设置

### 日志查看

```bash
# 查看应用日志
tail -f /var/log/alipay-auth/app.log

# 筛选网关相关日志
grep "gateway" /var/log/alipay-auth/app.log
```

## 开发测试

### 启动应用

```bash
# 开发环境
cd shouquanceshi/shouquanceshi
./start.sh

# 生产环境
export SPRING_PROFILES_ACTIVE=prod
./start.sh
```

### 测试流程

1. 启动应用
2. 访问配置检查接口确认配置正确
3. 在支付宝开放平台配置应用网关地址
4. 触发相关事件进行测试

## API参考

详细的API文档请参考支付宝开放平台官方文档：
- [应用网关介绍](https://opendocs.alipay.com/mini/introduce/gateway)
- [消息推送格式](https://opendocs.alipay.com/mini/introduce/notify) 