package com.kdsjkj.controller;

import com.kdsjkj.entity.Order;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.constant.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 订单控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@RestController
@RequestMapping("/order")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class OrderController {

    @Autowired
    private IOrderService orderService;

    /**
     * 根据商户订单号查询订单
     */
    @GetMapping("/getByOutTradeNo")
    public Result getByOutTradeNo(@RequestParam String outTradeNo) {
        try {
            Order order = orderService.getByOutTradeNo(outTradeNo);
            if (order != null) {
                return Result.success("查询成功", order);
            } else {
                return Result.error("订单不存在");
            }
        } catch (Exception e) {
            log.error("查询订单异常", e);
            return Result.error("查询订单异常: " + e.getMessage());
        }
    }

    /**
     * 根据支付宝交易号查询订单
     */
    @GetMapping("/getByTradeNo")
    public Result getByTradeNo(@RequestParam String tradeNo) {
        try {
            Order order = orderService.getByTradeNo(tradeNo);
            if (order != null) {
                return Result.success("查询成功", order);
            } else {
                return Result.error("订单不存在");
            }
        } catch (Exception e) {
            log.error("查询订单异常", e);
            return Result.error("查询订单异常: " + e.getMessage());
        }
    }

    /**
     * 根据应用ID查询订单列表
     */
    @GetMapping("/getByAppId")
    public Result getByAppId(@RequestParam String appId) {
        try {
            List<Order> orders = orderService.lambdaQuery()
                    .eq(Order::getAppId, appId)
                    .orderByDesc(Order::getCreateTime)
                    .list();
            return Result.success("查询成功", orders);
        } catch (Exception e) {
            log.error("查询订单列表异常", e);
            return Result.error("查询订单列表异常: " + e.getMessage());
        }
    }

    /**
     * 根据交易状态查询订单列表
     */
    @GetMapping("/getByStatus")
    public Result getByStatus(@RequestParam String tradeStatus) {
        try {
            List<Order> orders = orderService.lambdaQuery()
                    .eq(Order::getTradeStatus, tradeStatus)
                    .orderByDesc(Order::getCreateTime)
                    .list();
            return Result.success("查询成功", orders);
        } catch (Exception e) {
            log.error("查询订单列表异常", e);
            return Result.error("查询订单列表异常: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestParam String outTradeNo, @RequestParam String tradeStatus) {
        try {
            boolean result = orderService.updateTradeStatusByOutTradeNo(outTradeNo, tradeStatus);
            if (result) {
                return Result.success("订单状态更新成功");
            } else {
                return Result.error("订单状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新订单状态异常", e);
            return Result.error("更新订单状态异常: " + e.getMessage());
        }
    }
}
