package com.kdsjkj.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 达标奖励表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("achievement_rewards")
public class AchievementRewards implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀请商户最低个数
     */
    @TableField("min_merchant_count")
    private Integer minMerchantCount;

    /**
     * 每个商户每月最低收入金额(元)
     */
    @TableField("min_monthly_income")
    private BigDecimal minMonthlyIncome;

    /**
     * 奖励金额(元)
     */
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;


}
