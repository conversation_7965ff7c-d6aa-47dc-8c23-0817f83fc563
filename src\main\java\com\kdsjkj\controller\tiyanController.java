package com.kdsjkj.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayOpenAppMembersCreateModel;
import com.alipay.api.domain.AlipayOpenMiniExperienceCreateModel;
import com.alipay.api.domain.AlipayOpenMiniExperienceQueryModel;
import com.alipay.api.request.AlipayOpenAppMembersCreateRequest;
import com.alipay.api.request.AlipayOpenMiniExperienceCreateRequest;
import com.alipay.api.request.AlipayOpenMiniExperienceQueryRequest;
import com.alipay.api.response.AlipayOpenAppMembersCreateResponse;
import com.alipay.api.response.AlipayOpenMiniExperienceCreateResponse;
import com.alipay.api.response.AlipayOpenMiniExperienceQueryResponse;
import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.kdsjkj.service.IAlipayConfigFrontService;
import com.kdsjkj.service.IMiniprogramVersionService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-19 10:45
 */
//生成体验版
@Slf4j
@RestController
@RequestMapping("/api/v1/tiyan")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class tiyanController {
    
    private static final Logger logger = LoggerFactory.getLogger(tiyanController.class);
    
    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;
    
    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;

    @Autowired
    private IMiniprogramVersionService miniprogramVersionService;

    /**
     * 创建小程序体验版
     */
    @PostMapping("/create")
    public Result createExperience(
            @RequestParam("appid") String appid, 
            @RequestParam("version") String version) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            
            if (version == null || version.trim().isEmpty()) {
                return Result.paramError("version参数不能为空");
            }

            // 初始化SDK - 使用统一配置类
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数以调用接口
            AlipayOpenMiniExperienceCreateRequest request = new AlipayOpenMiniExperienceCreateRequest();
            AlipayOpenMiniExperienceCreateModel model = new AlipayOpenMiniExperienceCreateModel();
            
            // 设置商家小程序版本号
            model.setAppVersion(version);
            
            // 设置小程序端
            model.setBundleId("com.alipay.alipaywallet");
            
            request.setBizModel(model);

            // 从alipay_auth_record表中查询app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
            
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appid的授权令牌信息");
            }

            // 设置第三方应用授权令牌
            request.putOtherTextParam("app_auth_token", appAuthToken);

            AlipayOpenMiniExperienceCreateResponse response = alipayClient.execute(request);
            log.info("创建体验版响应: {}", response.getBody());

            if (response.isSuccess()) {

                
                return Result.success("体验版创建成功", response.getBody());
            } else {
                return Result.error("体验版创建失败: " + response.getSubMsg());
            }
        } catch (Exception e) {
            log.error("创建体验版异常", e);
            return Result.error("创建体验版异常: " + e.getMessage());
        }
    }

    /**
     * 添加小程序成员
     */
    @PostMapping("/addMember")
    public Result addMember(
            @RequestParam("appid") String appid, 
            @RequestParam("logonId") String logonId, 
            @RequestParam(value = "role", defaultValue = "DEVELOPER") String role) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            
            if (logonId == null || logonId.trim().isEmpty()) {
                return Result.paramError("logonId参数不能为空");
            }

            // 初始化SDK - 使用统一配置类
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数以调用接口
            AlipayOpenAppMembersCreateRequest request = new AlipayOpenAppMembersCreateRequest();
            AlipayOpenAppMembersCreateModel model = new AlipayOpenAppMembersCreateModel();
            
            // 设置角色
            model.setRole(role);
            
            // 设置支付宝账户
            model.setLogonId(logonId);
            
            request.setBizModel(model);

            // 从alipay_auth_record表中查询app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
            
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appid的授权令牌信息");
            }

            // 设置第三方应用授权令牌
            request.putOtherTextParam("app_auth_token", appAuthToken);

            AlipayOpenAppMembersCreateResponse response = alipayClient.execute(request);
            log.info("添加成员响应: {}", response.getBody());

            if (response.isSuccess()) {
                return Result.success("成员添加成功", response.getBody());
            } else {
                return Result.error("成员添加失败: " + response.getSubMsg());
            }
        } catch (Exception e) {
            log.error("添加成员异常", e);
            return Result.error("添加成员异常: " + e.getMessage());
        }
    }

    /**
     * 查询小程序体验版状态
     */
    @GetMapping("/status")
    public Result queryExperienceStatus(
            @RequestParam("appid") String appid, 
            @RequestParam("version") String version) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            
            if (version == null || version.trim().isEmpty()) {
                return Result.paramError("version参数不能为空");
            }

            // 初始化SDK - 使用统一配置类
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数以调用接口
            AlipayOpenMiniExperienceQueryRequest request = new AlipayOpenMiniExperienceQueryRequest();
            AlipayOpenMiniExperienceQueryModel model = new AlipayOpenMiniExperienceQueryModel();
            
            // 设置商家小程序版本号
            model.setAppVersion(version);
            
            // 设置小程序端
            model.setBundleId("com.alipay.alipaywallet");
            
            request.setBizModel(model);

            // 从alipay_auth_record表中查询app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
            
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appid的授权令牌信息");
            }

            // 设置第三方应用授权令牌
            request.putOtherTextParam("app_auth_token", appAuthToken);

            AlipayOpenMiniExperienceQueryResponse response = alipayClient.execute(request);
            log.info("查询体验版状态响应: {}", response.getBody());

            if (response.isSuccess()) {
                return Result.success("查询成功", response.getBody());
            } else {
                return Result.error("查询失败: " + response.getSubMsg());
            }
        } catch (Exception e) {
            log.error("查询体验版状态异常", e);
            return Result.error("查询体验版状态异常: " + e.getMessage());
        }
    }

    /**
     * 从数据库获取支付宝配置
     */
    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();
        
        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }
}
