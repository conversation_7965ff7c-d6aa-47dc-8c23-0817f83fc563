package com.kdsjkj.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayOpenMiniCategoryQueryModel;
import com.alipay.api.request.AlipayOpenMiniCategoryQueryRequest;
import com.alipay.api.response.AlipayOpenMiniCategoryQueryResponse;
import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.kdsjkj.service.IAlipayConfigFrontService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-18 19:58
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/leimu")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class LeimuController {

    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;

    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;
    /**
     * 获取提审类目
     */

    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();

        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }
    @GetMapping("/getAuditCategories")
    public Result<Object> getAuditCategories(String appid) throws AlipayApiException {
         // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniCategoryQueryRequest request = new AlipayOpenMiniCategoryQueryRequest();
        AlipayOpenMiniCategoryQueryModel model = new AlipayOpenMiniCategoryQueryModel();
        
        // 设置是否过滤不可选类目
        model.setIsFilter(true);
        
        request.setBizModel(model);
        // 根据appid从alipay_auth_record表中查询app_auth_token
        String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);

        if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
            return Result.error("未找到对应appid的授权令牌信息");
        }
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", appAuthToken);

        AlipayOpenMiniCategoryQueryResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
            
            // 过滤类目数据，只返回needLicense、needOutDoorPic、needSpecialLicense都为false的类目
            List<?> filteredCategories = response.getCategoryList().stream()
                    .filter(category -> {
                        try {
                            // 使用反射获取字段值，避免强类型转换
                            Boolean needLicense = (Boolean) category.getClass().getMethod("getNeedLicense").invoke(category);
                            Boolean needOutDoorPic = (Boolean) category.getClass().getMethod("getNeedOutDoorPic").invoke(category);
                            Boolean needSpecialLicense = (Boolean) category.getClass().getMethod("getNeedSpecialLicense").invoke(category);
                            
                            // 只有三个字段都为false时才保留
                            return !Boolean.TRUE.equals(needLicense) && 
                                   !Boolean.TRUE.equals(needOutDoorPic) && 
                                   !Boolean.TRUE.equals(needSpecialLicense);
                        } catch (Exception e) {
                            log.error("过滤类目数据时出错: {}", e.getMessage());
                            return false; // 出错时不保留该条数据
                        }
                    })
                    .collect(Collectors.toList());
            
            log.info("原始类目数量: {}, 过滤后类目数量: {}", 
                response.getCategoryList().size(), filteredCategories.size());
            
            // 返回过滤后的类目数据
            return Result.success(response.getCategoryList());
        } else {
            System.out.println("调用失败: " + response.getSubMsg());
            log.error("获取小程序类目失败: {}", response.getSubMsg());
            return Result.error("获取小程序类目失败: " + response.getSubMsg());
        }
    }


}
