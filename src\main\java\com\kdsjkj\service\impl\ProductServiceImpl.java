package com.kdsjkj.service.impl;

import com.kdsjkj.entity.Product;
import com.kdsjkj.mapper.ProductMapper;
import com.kdsjkj.service.IProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

    @Override
    public List<Product> getProductsByAppid(String appid) {
        return this.lambdaQuery()
                .eq(Product::getProductAppid, appid)
                .orderByDesc(Product::getCreateTime)
                .list();
    }

}
