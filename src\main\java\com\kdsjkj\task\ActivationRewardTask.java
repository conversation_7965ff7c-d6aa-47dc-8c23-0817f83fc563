package com.kdsjkj.task;

import com.kdsjkj.entity.ActivationRewards;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.entity.Order;
import com.kdsjkj.service.IActivationRewardsService;
import com.kdsjkj.service.IAgentService;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.service.IAgentFundService;
import com.kdsjkj.service.IMerchantActivationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 激活奖励计算定时任务
 */
@Slf4j
@Component
public class ActivationRewardTask {

    @Autowired
    private IActivationRewardsService activationRewardsService;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IAgentFundService agentFundService;

    @Autowired
    private IMerchantActivationService merchantActivationService;

    // 激活奖励类型：2激活奖励
    private static final Integer ACTIVATION_REWARD_TYPE = 2;

    /**
     * 每小时执行一次激活奖励检查
     */
    @Scheduled(cron = "0 0 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void calculateActivationRewards() {
        log.info("开始执行激活奖励计算任务");

        try {
            // 1. 获取激活奖励规则
            ActivationRewards rewardRule = activationRewardsService.getActivationReward();
            if (rewardRule == null) {
                log.error("未找到激活奖励规则配置");
                return;
            }
            log.info("获取到激活奖励规则：最低收款金额={}, 奖励金额={}", 
                    rewardRule.getMinPaymentAmount(), rewardRule.getRewardAmount());

            // 2. 获取所有代理
            List<Agent> allAgents = agentService.list();
            log.info("获取到代理总数：{}", allAgents.size());

            // 3. 遍历每个代理进行激活检查
            for (Agent agent : allAgents) {
                try {
                    processAgentActivationReward(agent, rewardRule);
                } catch (Exception e) {
                    log.error("处理代理{}激活奖励时发生错误", agent.getUid(), e);
                }
            }

            log.info("激活奖励计算任务执行完成");
        } catch (Exception e) {
            log.error("激活奖励计算任务执行失败", e);
            throw e;
        }
    }

    /**
     * 处理单个代理的激活奖励
     */
    private void processAgentActivationReward(Agent agent, ActivationRewards rewardRule) {
        String agentUid = agent.getUid();
        log.info("开始处理代理{}的激活奖励", agentUid);

        // 1. 获取代理的所有商户appid列表
        List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(agentUid);
        
        if (merchantAppIds.isEmpty()) {
            log.info("代理{}暂无商户，跳过激活奖励检查", agentUid);
            return;
        }
        
        log.info("代理{}共有{}个商户", agentUid, merchantAppIds.size());

        // 2. 遍历每个商户检查激活状态
        for (String appId : merchantAppIds) {
            try {
                processMerchantActivation(agentUid, appId, rewardRule);
            } catch (Exception e) {
                log.error("处理商户{}激活奖励时发生错误", appId, e);
            }
        }
    }

    /**
     * 处理单个商户的激活检查
     */
    private void processMerchantActivation(String agentUid, String appId, ActivationRewards rewardRule) {
        log.debug("检查商户{}的激活状态", appId);

        // 1. 检查商户是否已激活
        if (merchantActivationService.isActivated(appId)) {
            log.debug("商户{}已激活，跳过", appId);
            return;
        }

        // 2. 计算商户的总收款金额
        BigDecimal totalAmount = calculateMerchantTotalAmount(appId);
        log.debug("商户{}总收款金额：{}", appId, totalAmount);

        // 3. 检查是否达到激活阈值
        if (totalAmount.compareTo(rewardRule.getMinPaymentAmount()) >= 0) {
            log.info("商户{}达到激活阈值，总收款：{}，阈值：{}", appId, totalAmount, rewardRule.getMinPaymentAmount());
            
            // 4. 发放激活奖励
            grantActivationReward(agentUid, appId, totalAmount, rewardRule);
        } else {
            log.debug("商户{}未达到激活阈值，总收款：{}，阈值：{}", appId, totalAmount, rewardRule.getMinPaymentAmount());
        }
    }

    /**
     * 计算商户总收款金额
     */
    private BigDecimal calculateMerchantTotalAmount(String appId) {
        List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getAppId, appId)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS")  // 只统计成功的订单
                .list();

        return orders.stream()
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 发放激活奖励
     */
    private void grantActivationReward(String agentUid, String appId, BigDecimal totalAmount, ActivationRewards rewardRule) {
        log.info("开始发放激活奖励 - 代理：{}，商户：{}，收款金额：{}，奖励金额：{}", 
                agentUid, appId, totalAmount, rewardRule.getRewardAmount());

        try {
            // 1. 在AgentFund表中记录激活奖励
            boolean fundSuccess = agentFundService.saveAgentFund(agentUid, rewardRule.getRewardAmount(), ACTIVATION_REWARD_TYPE);
            if (!fundSuccess) {
                log.error("激活奖励发放失败 - 代理：{}，商户：{}", agentUid, appId);
                throw new RuntimeException("激活奖励发放失败");
            }

            // 2. 在MerchantActivation表中记录激活状态
            merchantActivationService.recordActivation(appId, agentUid, totalAmount, rewardRule.getRewardAmount());

            log.info("激活奖励发放成功 - 代理：{}，商户：{}，奖励金额：{}", agentUid, appId, rewardRule.getRewardAmount());
        } catch (Exception e) {
            log.error("发放激活奖励时发生异常 - 代理：{}，商户：{}", agentUid, appId, e);
            throw e;
        }
    }
}