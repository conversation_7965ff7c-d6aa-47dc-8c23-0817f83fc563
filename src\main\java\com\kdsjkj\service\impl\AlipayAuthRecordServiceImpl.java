package com.kdsjkj.service.impl;

import com.kdsjkj.entity.AlipayAuthRecord;
import com.kdsjkj.mapper.AlipayAuthRecordMapper;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AlipayAuthRecordServiceImpl implements AlipayAuthRecordService {
    @Autowired
    private AlipayAuthRecordMapper alipayAuthRecordMapper;

    @Override
    public void saveAuthRecord(AlipayAuthRecord record) {
        alipayAuthRecordMapper.insert(record);
    }
    
    @Override
    public String getAppAuthTokenByAuthAppId(String authAppId) {
        LambdaQueryWrapper<AlipayAuthRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlipayAuthRecord::getAuthAppId, authAppId)
                   .orderByDesc(AlipayAuthRecord::getCreateTime)
                   .last("LIMIT 1");
        
        AlipayAuthRecord record = alipayAuthRecordMapper.selectOne(queryWrapper);
        return record != null ? record.getAppAuthToken() : null;
    }
    
    @Override
    public String getUserIdByAuthAppId(String authAppId) {
        LambdaQueryWrapper<AlipayAuthRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlipayAuthRecord::getAuthAppId, authAppId)
                   .orderByDesc(AlipayAuthRecord::getCreateTime)
                   .last("LIMIT 1");
        
        AlipayAuthRecord record = alipayAuthRecordMapper.selectOne(queryWrapper);
        return record != null ? record.getUserId() : null;
    }
} 