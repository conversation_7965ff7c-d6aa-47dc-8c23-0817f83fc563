//package com.kdsjkj.appTest.xiaochengxu.A生成版本;
//
//import com.alipay.api.AlipayApiException;
//import com.alipay.api.AlipayClient;
//import com.alipay.api.DefaultAlipayClient;
//import com.alipay.api.diagnosis.DiagnosisUtils;
//import com.alipay.api.request.AlipayOpenMiniBaseinfoQueryRequest;
//import com.alipay.api.response.AlipayOpenMiniBaseinfoQueryResponse;
////import com.alipay.api.domain.AlipayOpenMiniBaseinfoQueryModel;
//import com.kdsjkj.config.AlipayMiniProgramConfig;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.JSONArray;
//
///**
// * 配置继承检查工具
// * 专门用于检查小程序是否正确继承了第三方应用的配置
// */
//public class ConfigInheritanceChecker {
//
//    public static void main(String[] args) {
//        System.out.println("=== 小程序配置继承检查工具 ===");
//
//        ConfigInheritanceChecker checker = new ConfigInheritanceChecker();
//        checker.checkConfigInheritance();
//    }
//
//    /**
//     * 检查配置继承状态
//     */
//    public void checkConfigInheritance() {
//        try {
//            System.out.println("\n🔍 正在检查小程序配置继承状态...");
//
//            // 显示当前配置
//            displayCurrentConfig();
//
//            // 查询小程序基本信息
//            queryMiniProgramInfo();
//
//        } catch (Exception e) {
//            System.err.println("❌ 检查过程中发生异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 显示当前配置信息
//     */
//    private void displayCurrentConfig() {
//        System.out.println("\n📋 当前第三方应用配置:");
//        System.out.println("======================================");
//        System.out.println("应用ID: " + AlipayMiniProgramConfig.getAlipayConfig().getAppId());
//        System.out.println("授权令牌: " + AlipayMiniProgramConfig.getAppAuthToken());
//        System.out.println("网关地址: " + AlipayMiniProgramConfig.getAlipayConfig().getServerUrl());
//        System.out.println("签名类型: " + AlipayMiniProgramConfig.getAlipayConfig().getSignType());
//        System.out.println("字符编码: " + AlipayMiniProgramConfig.getAlipayConfig().getCharset());
//        System.out.println("======================================");
//    }
//
//    /**
//     * 查询小程序基本信息
//     */
//    private void queryMiniProgramInfo() {
//        try {
//            // 初始化SDK
//            AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());
//
//            // 构造请求参数
//            AlipayOpenMiniBaseinfoQueryRequest request = new AlipayOpenMiniBaseinfoQueryRequest();
//
//
//            request.setBizModel(model);
//            request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
//
//            System.out.println("\n📤 发送小程序基本信息查询请求...");
//            AlipayOpenMiniBaseinfoQueryResponse response = alipayClient.execute(request);
//
//            System.out.println("\n📥 服务器完整响应:");
//            System.out.println("======================================");
//            System.out.println(response.getBody());
//            System.out.println("======================================");
//
//            if (response.isSuccess()) {
//                System.out.println("\n✅ 小程序信息查询成功");
//                analyzeInheritedConfig(response.getBody());
//            } else {
//                System.out.println("\n❌ 小程序信息查询失败");
//                System.out.println("错误码: " + response.getCode());
//                System.out.println("错误信息: " + response.getMsg());
//
//                // 分析错误原因
//                analyzeQueryError(response.getCode(), response.getMsg());
//
//                // 获取诊断链接
//                try {
//                    String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
//                    System.out.println("🔍 诊断链接: " + diagnosisUrl);
//                } catch (Exception e) {
//                    System.out.println("⚠️ 无法获取诊断链接");
//                }
//            }
//
//        } catch (AlipayApiException e) {
//            System.err.println("❌ API调用异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 分析继承的配置
//     */
//    private void analyzeInheritedConfig(String responseBody) {
//        try {
//            JSONObject rootNode = JSON.parseObject(responseBody);
//            JSONObject responseNode = rootNode.getJSONObject("alipay_open_mini_baseinfo_query_response");
//
//            if (responseNode != null) {
//                System.out.println("\n📊 配置继承分析报告:");
//                System.out.println("======================================");
//
//                // 基本信息
//                String appName = responseNode.getString("app_name");
//                String appStatus = responseNode.getString("app_status");
//                String appSlogan = responseNode.getString("app_slogan");
//
//                System.out.println("📱 小程序名称: " + (appName != null ? appName : "未设置"));
//                System.out.println("📊 小程序状态: " + (appStatus != null ? appStatus : "未知"));
//                System.out.println("🏷️ 小程序标语: " + (appSlogan != null ? appSlogan : "未设置"));
//
//                // 检查关键配置继承
//                checkKeyConfigInheritance(responseNode);
//
//                // 检查接口权限
//                checkApiPermissions(responseNode);
//
//                // 检查域名配置
//                checkDomainConfig(responseNode);
//
//                System.out.println("======================================");
//
//                // 提供修复建议
//                provideFixSuggestions(responseNode);
//
//            } else {
//                System.out.println("⚠️ 无法解析响应数据");
//            }
//
//        } catch (Exception e) {
//            System.err.println("❌ 分析配置继承失败: " + e.getMessage());
//            System.out.println("原始响应: " + responseBody);
//        }
//    }
//
//    /**
//     * 检查关键配置继承
//     */
//    private void checkKeyConfigInheritance(JSONObject responseNode) {
//        System.out.println("\n🔧 关键配置继承检查:");
//
//        // 检查服务配置
//        String servicePhone = responseNode.getString("service_phone");
//        String serviceEmail = responseNode.getString("service_email");
//
//        System.out.println("📞 服务电话: " + (servicePhone != null ? "✅ 已设置" : "❌ 未设置"));
//        System.out.println("📧 服务邮箱: " + (serviceEmail != null ? "✅ 已设置" : "❌ 未设置"));
//
//        // 检查应用配置
//        String appLogo = responseNode.getString("app_logo");
//        String appDesc = responseNode.getString("app_desc");
//
//        System.out.println("🖼️ 应用Logo: " + (appLogo != null ? "✅ 已设置" : "❌ 未设置"));
//        System.out.println("📝 应用描述: " + (appDesc != null ? "✅ 已设置" : "❌ 未设置"));
//    }
//
//    /**
//     * 检查API权限
//     */
//    private void checkApiPermissions(JSONObject responseNode) {
//        System.out.println("\n🔐 API权限继承检查:");
//
//        if (responseNode.containsKey("api_scope")) {
//            JSONArray apiScope = responseNode.getJSONArray("api_scope");
//            if (apiScope != null && apiScope.size() > 0) {
//                System.out.println("✅ API权限已继承，共 " + apiScope.size() + " 个权限");
//                for (int i = 0; i < apiScope.size(); i++) {
//                    System.out.println("   - " + apiScope.getString(i));
//                }
//            } else {
//                System.out.println("❌ API权限为空");
//            }
//        } else {
//            System.out.println("❌ 未找到API权限配置");
//        }
//    }
//
//    /**
//     * 检查域名配置
//     */
//    private void checkDomainConfig(JSONObject responseNode) {
//        System.out.println("\n🌐 域名配置继承检查:");
//
//        if (responseNode.containsKey("domain_list")) {
//            JSONArray domainList = responseNode.getJSONArray("domain_list");
//            if (domainList != null && domainList.size() > 0) {
//                System.out.println("✅ 域名白名单已继承，共 " + domainList.size() + " 个域名");
//                for (int i = 0; i < domainList.size(); i++) {
//                    System.out.println("   - " + domainList.getString(i));
//                }
//            } else {
//                System.out.println("❌ 域名白名单为空");
//            }
//        } else {
//            System.out.println("❌ 未找到域名白名单配置");
//        }
//    }
//
//    /**
//     * 分析查询错误
//     */
//    private void analyzeQueryError(String errorCode, String errorMsg) {
//        System.out.println("\n🔍 错误分析:");
//        System.out.println("==============================");
//
//        if ("40004".equals(errorCode)) {
//            System.out.println("❌ 业务处理失败 - 关键问题:");
//            System.out.println("   1. 第三方应用授权令牌无效或过期");
//            System.out.println("   2. 小程序与第三方应用关联绑定不完整");
//            System.out.println("   3. 第三方应用配置不完整");
//
//            System.out.println("\n🔧 这是导致配置无法继承的主要原因！");
//
//        } else if ("20000".equals(errorCode)) {
//            System.out.println("❌ 系统繁忙 - 建议稍后重试");
//
//        } else {
//            System.out.println("❌ 其他错误: " + errorCode);
//            System.out.println("   详细信息: " + errorMsg);
//        }
//
//        System.out.println("==============================");
//    }
//
//    /**
//     * 提供修复建议
//     */
//    private void provideFixSuggestions(JSONObject responseNode) {
//        System.out.println("\n💡 修复建议:");
//        System.out.println("======================================");
//
//        System.out.println("1. 检查第三方应用配置:");
//        System.out.println("   - 登录支付宝开放平台");
//        System.out.println("   - 进入第三方应用管理");
//        System.out.println("   - 确保接口加签方式已正确配置");
//        System.out.println("   - 检查域名白名单和IP白名单");
//
//        System.out.println("\n2. 检查小程序关联绑定:");
//        System.out.println("   - 确保小程序已正确关联到第三方应用");
//        System.out.println("   - 检查授权状态和权限范围");
//        System.out.println("   - 验证 app_auth_token 的有效性");
//
//        System.out.println("\n3. 重新获取授权令牌:");
//        System.out.println("   - 如果token过期，重新获取授权");
//        System.out.println("   - 更新配置文件中的 APP_AUTH_TOKEN");
//
//        System.out.println("\n4. 重新构建小程序:");
//        System.out.println("   - 在确认配置正确后，重新执行构建");
//        System.out.println("   - 构建完成后再次检查配置继承");
//
//        System.out.println("======================================");
//    }
//}