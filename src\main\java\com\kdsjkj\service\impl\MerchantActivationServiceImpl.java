package com.kdsjkj.service.impl;

import com.kdsjkj.entity.MerchantActivation;
import com.kdsjkj.mapper.MerchantActivationMapper;
import com.kdsjkj.service.IMerchantActivationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户激活状态记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
public class MerchantActivationServiceImpl extends ServiceImpl<MerchantActivationMapper, MerchantActivation> implements IMerchantActivationService {

    @Override
    public boolean isActivated(String appId) {
        if (appId == null || appId.trim().isEmpty()) {
            return false;
        }
        
        MerchantActivation activation = this.lambdaQuery()
                .eq(MerchantActivation::getAppId, appId)
                .one();
        
        return activation != null;
    }

    @Override
    public MerchantActivation recordActivation(String appId, String agentUid, BigDecimal activationAmount, BigDecimal rewardAmount) {
        log.info("记录商户激活 - appId: {}, agentUid: {}, activationAmount: {}, rewardAmount: {}", 
                appId, agentUid, activationAmount, rewardAmount);
        
        // 检查是否已激活
        if (isActivated(appId)) {
            log.warn("商户已激活，无需重复记录 - appId: {}", appId);
            return null;
        }
        
        MerchantActivation activation = new MerchantActivation();
        activation.setAppId(appId);
        activation.setAgentUid(agentUid);
        activation.setActivationAmount(activationAmount);
        activation.setRewardAmount(rewardAmount);
        activation.setActivationTime(LocalDateTime.now());
        activation.setCreatedAt(LocalDateTime.now());
        activation.setUpdatedAt(LocalDateTime.now());
        
        boolean success = this.save(activation);
        if (success) {
            log.info("商户激活记录保存成功 - ID: {}", activation.getId());
            return activation;
        } else {
            log.error("商户激活记录保存失败 - appId: {}", appId);
            throw new RuntimeException("商户激活记录保存失败");
        }
    }

    @Override
    public int getActivatedMerchantCount(String agentUid) {
        if (agentUid == null || agentUid.trim().isEmpty()) {
            return 0;
        }
        
        return Math.toIntExact(this.lambdaQuery()
                .eq(MerchantActivation::getAgentUid, agentUid)
                .count());
    }

    @Override
    public int getActivatedMerchantCountByTimeRange(String agentUid, LocalDateTime startTime, LocalDateTime endTime) {
        if (agentUid == null || agentUid.trim().isEmpty()) {
            return 0;
        }
        
        return Math.toIntExact(this.lambdaQuery()
                .eq(MerchantActivation::getAgentUid, agentUid)
                .ge(MerchantActivation::getActivationTime, startTime)
                .le(MerchantActivation::getActivationTime, endTime)
                .count());
    }
}
