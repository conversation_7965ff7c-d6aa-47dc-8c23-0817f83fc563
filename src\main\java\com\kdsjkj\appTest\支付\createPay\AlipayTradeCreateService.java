package com.kdsjkj.appTest.支付.createPay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeCreateRequest;
import com.alipay.api.response.AlipayTradeCreateResponse;
import com.alipay.api.domain.AlipayTradeCreateModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;
import com.kdsjkj.service.AlipayAuthRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 支付宝订单创建服务
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Service
public class AlipayTradeCreateService {
    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;
    /**
     * 创建支付宝订单
     * 
     * @param outTradeNo 商户订单号
     * @param totalAmount 订单金额
     * @param subject 订单标题
     * @param buyerId 买家用户ID
     * @return 订单创建响应
     * @throws AlipayApiException 支付宝API异常
     */
    public AlipayTradeCreateResponse createOrder(String outTradeNo, String totalAmount, 
                                                String subject, String buyerId,String appid) throws AlipayApiException {
        
        log.info("开始创建订单 - 订单号: {}, 金额: {}, 买家ID: {},appID: {}", outTradeNo, totalAmount, buyerId,appid);
        
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeCreateRequest request = new AlipayTradeCreateRequest();
        AlipayTradeCreateModel model = new AlipayTradeCreateModel();

        // 设置商户订单号
        model.setOutTradeNo(outTradeNo);

        // 设置订单总金额
        model.setTotalAmount(totalAmount);

        // 设置订单标题
        model.setSubject(subject);

        // 设置买家用户ID
        model.setBuyerId(buyerId);

        request.setBizModel(model);

        // 设置异步通知地址
        request.setNotifyUrl("https://zf.kdsjkj.com/api/alipay/createPay");
        String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
        if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
            throw new AlipayApiException("未找到对应appid的授权令牌信息");
        }

        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", appAuthToken);

        // 调用API创建订单
        AlipayTradeCreateResponse response = alipayClient.execute(request);
        
        log.info("订单创建API调用完成 - 响应: {}", response.getBody());
        
        if (response.isSuccess()) {
            log.info("订单创建成功 - 商户订单号: {}, 支付宝交易号: {}", 
                    response.getOutTradeNo(), response.getTradeNo());
        } else {
            log.error("订单创建失败 - 错误码: {}, 错误信息: {}", 
                    response.getCode(), response.getSubMsg());
        }
        
        return response;
    }
} 