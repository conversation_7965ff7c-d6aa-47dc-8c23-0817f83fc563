package com.kdsjkj.controller;

import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.service.IAgentService;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.dto.AgentRankDTO;
import com.kdsjkj.dto.AgentTransactionDTO;
import com.kdsjkj.dto.MerchantTransactionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表数据控制器
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/api/report-data")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class ReportDataController {
    
    @Autowired
    private IAgentService agentService;
    
    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;
    
    @Autowired
    private IOrderService orderService;
    
    /**
     * 获取交易总数据统计
     * 
     * @param agentUid 代理UID
     * @return 统计数据
     */
    @GetMapping("/transaction-statistics")
    public ResponseEntity<Map<String, Object>> getTransactionStatistics(
            @RequestParam(required = true) String agentUid) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取交易总数据统计 - 代理UID: {}", agentUid);
            
            // 1. 验证代理是否存在
            Agent agent = agentService.getAgentByUid(agentUid);
            if (agent == null) {
                response.put("success", false);
                response.put("message", "代理不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 2. 获取所有相关代理UID（包括自己和所有下级代理）
            List<String> allAgentUids = agentService.getAllSubAgentUidsByAgentUid(agentUid);
            log.info("找到相关代理数量: {}", allAgentUids.size());
            
            // 3. 获取所有代理关联的商户appid
            List<String> allMerchantAppIds = new ArrayList<>();
            for (String uid : allAgentUids) {
                List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(uid);
                allMerchantAppIds.addAll(merchantAppIds);
            }
            log.info("找到商户appid数量: {}", allMerchantAppIds.size());
            
            // 4. 计算统计数据
            Map<String, Object> statistics = calculateTransactionStatistics(allMerchantAppIds);
            
            // 5. 添加代理信息
            Map<String, Object> agentInfo = new HashMap<>();
            agentInfo.put("uid", agent.getUid());
            agentInfo.put("name", agent.getName());
            agentInfo.put("phone", agent.getPhone());
            statistics.put("agentInfo", agentInfo);
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "交易总数据统计获取成功");
            
            log.info("交易总数据统计获取成功 - 代理UID: {}, 昨日交易额: {}, 昨日交易笔数: {}, 本月交易额: {}, 本月交易笔数: {}", 
                    agentUid,
                    statistics.get("yesterdayAmount"),
                    statistics.get("yesterdayCount"),
                    statistics.get("monthAmount"),
                    statistics.get("monthCount"));
            
        } catch (Exception e) {
            log.error("获取交易总数据统计异常 - 代理UID: {}", agentUid, e);
            response.put("success", false);
            response.put("message", "获取交易总数据统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取代理排名（基于交易金额）
     * 
     * @param agentUid 代理UID
     * @param startTime 开始时间（可选，默认昨天开始时间）
     * @param endTime 结束时间（可选，默认昨天结束时间）
     * @return 代理排名列表
     * # 使用默认时间范围（昨天）
     * GET /api/report-data/agent-rank?agentUid=AGENT123
     *
     * # 指定时间范围
     * GET /api/report-data/agent-rank?agentUid=AGENT123&startTime=2024-12-25T00:00:00&endTime=2024-12-25T23:59:59
     */
    @GetMapping("/agent-rank")
    public ResponseEntity<Map<String, Object>> getAgentRank(
            @RequestParam(required = true) String agentUid,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取代理排名 - 代理UID: {}, 开始时间: {}, 结束时间: {}", agentUid, startTime, endTime);
            
            // 1. 验证代理是否存在
            Agent agent = agentService.getAgentByUid(agentUid);
            if (agent == null) {
                response.put("success", false);
                response.put("message", "代理不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 2. 处理时间参数
            LocalDateTime startDateTime;
            LocalDateTime endDateTime;
            
            if (startTime == null || endTime == null || startTime.trim().isEmpty() || endTime.trim().isEmpty()) {
                // 默认使用昨天的时间范围
                LocalDate yesterday = LocalDate.now().minusDays(1);
                startDateTime = yesterday.atStartOfDay();
                endDateTime = yesterday.atTime(23, 59, 59);
                log.info("使用默认时间范围 - 开始时间: {}, 结束时间: {}", startDateTime, endDateTime);
            } else {
                try {
                    // 解析传入的时间参数
                    startDateTime = LocalDateTime.parse(startTime);
                    endDateTime = LocalDateTime.parse(endTime);
                    log.info("使用指定时间范围 - 开始时间: {}, 结束时间: {}", startDateTime, endDateTime);
                } catch (Exception e) {
                    log.warn("时间参数解析失败，使用默认时间范围 - startTime: {}, endTime: {}", startTime, endTime);
                    // 如果解析失败，使用默认的昨天时间范围
                    LocalDate yesterday = LocalDate.now().minusDays(1);
                    startDateTime = yesterday.atStartOfDay();
                    endDateTime = yesterday.atTime(23, 59, 59);
                }
            }
            
            // 3. 获取所有相关代理UID（包括自己和所有下级代理）
            List<String> allAgentUids = agentService.getAllSubAgentUidsByAgentUid(agentUid);
            log.info("找到相关代理数量: {}", allAgentUids.size());
            
            // 4. 获取每个代理的基本信息和交易统计
            List<AgentRankDTO> rankList = new ArrayList<>();
            
            for (String uid : allAgentUids) {
                Agent currentAgent = agentService.getAgentByUid(uid);
                if (currentAgent == null) continue;
                
                // 获取该代理的所有商户appid
                List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(uid);
                
                // 获取指定时间范围内的交易统计
                BigDecimal totalAmount = BigDecimal.ZERO;
                
                for (String appId : merchantAppIds) {
                    Map<String, Object> stats = orderService.getTransactionStatisticsByAppIdAndTimeRange(
                            appId, startDateTime, endDateTime);
                    totalAmount = totalAmount.add((BigDecimal) stats.get("totalAmount"));
                }
                
                // 创建排名对象
                AgentRankDTO rankDTO = new AgentRankDTO();
                rankDTO.setUid(uid);
                rankDTO.setName(currentAgent.getName());
                rankDTO.setTotalAmount(totalAmount);
                rankList.add(rankDTO);
            }
            
            // 5. 根据交易总金额排序并设置排名
            rankList.sort((a, b) -> b.getTotalAmount().compareTo(a.getTotalAmount()));
            
            for (int i = 0; i < rankList.size(); i++) {
                rankList.get(i).setRank(i + 1);
            }
            
            // 6. 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("rankList", rankList);
            // 创建时间范围Map（JDK 1.8兼容）
            Map<String, Object> timeRange = new HashMap<>();
            timeRange.put("startTime", startDateTime);
            timeRange.put("endTime", endDateTime);
            data.put("timeRange", timeRange);
            data.put("totalAgents", rankList.size());
            
            response.put("success", true);
            response.put("data", data);
            response.put("message", "代理排名获取成功");
            
            log.info("代理排名获取成功 - 代理UID: {}, 总代理数: {}", agentUid, rankList.size());
            
        } catch (Exception e) {
            log.error("获取代理排名异常 - 代理UID: {}", agentUid, e);
            response.put("success", false);
            response.put("message", "获取代理排名失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 计算交易统计数据
     */
    private Map<String, Object> calculateTransactionStatistics(List<String> appIds) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取时间范围
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate monthStart = today.withDayOfMonth(1);
        
        if (appIds.isEmpty()) {
            // 如果没有商户，返回空统计
            statistics.put("yesterdayAmount", BigDecimal.ZERO);
            statistics.put("yesterdayCount", 0);
            statistics.put("monthAmount", BigDecimal.ZERO);
            statistics.put("monthCount", 0);
            return statistics;
        }

        // 昨日统计
        LocalDateTime yesterdayStart = yesterday.atStartOfDay();
        LocalDateTime yesterdayEnd = yesterday.atTime(23, 59, 59);
        
        // 本月统计
        LocalDateTime monthStartTime = monthStart.atStartOfDay();
        LocalDateTime monthEndTime = today.atTime(23, 59, 59);
        
        // 统计所有商户的昨日数据
        Map<String, Object> yesterdayStats = orderService.getTransactionStatisticsByAppIds(appIds);
        Map<String, Object> yesterdayStatsInRange = orderService.getTransactionStatisticsByAppIdAndTimeRange(
                appIds.get(0), yesterdayStart, yesterdayEnd);
        
        // 统计所有商户的本月数据
        Map<String, Object> monthStatsInRange = orderService.getTransactionStatisticsByAppIdAndTimeRange(
                appIds.get(0), monthStartTime, monthEndTime);
        log.info("传入的时间参数 - startTime: {}, endTime: {}", monthStartTime, monthEndTime);
        
        // 设置统计数据
        statistics.put("yesterdayAmount", yesterdayStatsInRange.get("totalAmount"));
        statistics.put("yesterdayCount", yesterdayStatsInRange.get("transactionCount"));
        statistics.put("monthAmount", monthStatsInRange.get("totalAmount"));
        statistics.put("monthCount", monthStatsInRange.get("transactionCount"));
        statistics.put("totalAmount", yesterdayStats.get("totalAmount"));
        statistics.put("totalCount", yesterdayStats.get("transactionCount"));
        
        return statistics;
    }
    
    /**
     * 获取代理和商户交易统计
     * 
     * @param agentUid 代理UID
     * @param startTime 开始时间（可选，默认昨天开始时间）
     * @param endTime 结束时间（可选，默认昨天结束时间）
     * @return 代理和商户的交易统计数据
     */
    @GetMapping("/transaction-details")
    public ResponseEntity<Map<String, Object>> getTransactionDetails(
            @RequestParam(required = true) String agentUid,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取代理和商户交易统计 - 代理UID: {}, 开始时间: {}, 结束时间: {}", agentUid, startTime, endTime);
            
            // 1. 验证代理是否存在
            Agent agent = agentService.getAgentByUid(agentUid);
            if (agent == null) {
                response.put("success", false);
                response.put("message", "代理不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 2. 处理时间参数
            LocalDateTime startDateTime;
            LocalDateTime endDateTime;
            
            if (startTime == null || endTime == null || startTime.trim().isEmpty() || endTime.trim().isEmpty()) {
                // 默认使用昨天的时间范围
                LocalDate yesterday = LocalDate.now().minusDays(1);
                startDateTime = yesterday.atStartOfDay();
                endDateTime = yesterday.atTime(23, 59, 59);
                log.info("使用默认时间范围 - 开始时间: {}, 结束时间: {}", startDateTime, endDateTime);
            } else {
                try {
                    // 解析传入的时间参数
                    startDateTime = LocalDateTime.parse(startTime);
                    endDateTime = LocalDateTime.parse(endTime);
                    log.info("使用指定时间范围 - 开始时间: {}, 结束时间: {}", startDateTime, endDateTime);
                } catch (Exception e) {
                    log.warn("时间参数解析失败，使用默认时间范围 - startTime: {}, endTime: {}", startTime, endTime);
                    // 如果解析失败，使用默认的昨天时间范围
                    LocalDate yesterday = LocalDate.now().minusDays(1);
                    startDateTime = yesterday.atStartOfDay();
                    endDateTime = yesterday.atTime(23, 59, 59);
                }
            }
            
            // 3. 获取所有相关代理UID（包括自己和所有下级代理）
            List<String> allAgentUids = agentService.getAllSubAgentUidsByAgentUid(agentUid);
            log.info("找到相关代理数量: {}", allAgentUids.size());
            
            // 4. 统计代理交易数据
            List<AgentTransactionDTO> agentStats = calculateAgentTransactions(allAgentUids, startDateTime, endDateTime);
            
            // 5. 统计商户交易数据
            List<MerchantTransactionDTO> merchantStats = calculateMerchantTransactions(allAgentUids, startDateTime, endDateTime);
            
            // 6. 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("agentTransactions", agentStats);
            data.put("merchantTransactions", merchantStats);
            
            Map<String, Object> timeRange = new HashMap<>();
            timeRange.put("startTime", startDateTime);
            timeRange.put("endTime", endDateTime);
            data.put("timeRange", timeRange);
            
            data.put("totalAgents", agentStats.size());
            data.put("totalMerchants", merchantStats.size());
            
            response.put("success", true);
            response.put("data", data);
            response.put("message", "交易统计数据获取成功");
            
            log.info("交易统计数据获取成功 - 代理数: {}, 商户数: {}", agentStats.size(), merchantStats.size());
            
        } catch (Exception e) {
            log.error("获取交易统计数据异常 - 代理UID: {}", agentUid, e);
            response.put("success", false);
            response.put("message", "获取交易统计数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 计算代理交易统计数据
     */
    private List<AgentTransactionDTO> calculateAgentTransactions(
            List<String> agentUids, LocalDateTime startTime, LocalDateTime endTime) {
        
        List<AgentTransactionDTO> agentStats = new ArrayList<>();
        
        for (String uid : agentUids) {
            Agent agent = agentService.getAgentByUid(uid);
            if (agent == null) continue;
            
            // 获取该代理的所有商户appid
            List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(uid);
            
            // 统计交易数据
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalCount = 0;
            
            for (String appId : merchantAppIds) {
                Map<String, Object> stats = orderService.getTransactionStatisticsByAppIdAndTimeRange(
                        appId, startTime, endTime);
                
                totalAmount = totalAmount.add((BigDecimal) stats.get("totalAmount"));
                totalCount += (Integer) stats.get("transactionCount");
            }
            
            // 创建统计对象
            AgentTransactionDTO dto = new AgentTransactionDTO();
            dto.setUid(uid);
            dto.setName(agent.getName());
            dto.setTotalAmount(totalAmount);
            dto.setTransactionCount(totalCount);
            agentStats.add(dto);
        }
        
        // 根据交易金额排序并设置排名
        agentStats.sort((a, b) -> b.getTotalAmount().compareTo(a.getTotalAmount()));
        for (int i = 0; i < agentStats.size(); i++) {
            agentStats.get(i).setRank(i + 1);
        }
        
        return agentStats;
    }
    
    /**
     * 计算商户交易统计数据
     */
    private List<MerchantTransactionDTO> calculateMerchantTransactions(
            List<String> agentUids, LocalDateTime startTime, LocalDateTime endTime) {
        
        List<MerchantTransactionDTO> merchantStats = new ArrayList<>();
        Set<String> processedAppIds = new HashSet<>(); // 用于去重
        
        for (String uid : agentUids) {
            // 获取该代理关联的所有商户信息
            List<ServiceMarketOrder> merchantOrders = serviceMarketOrderService.lambdaQuery()
                    .eq(ServiceMarketOrder::getAgentUid, uid)
                    .select(
                        ServiceMarketOrder::getConsumerMiniappid,
                        ServiceMarketOrder::getContactor,
                        ServiceMarketOrder::getPhone
                    )
                    .list();
            
            for (ServiceMarketOrder order : merchantOrders) {
                String appId = order.getConsumerMiniappid();
                if (appId == null || appId.trim().isEmpty() || !processedAppIds.add(appId)) {
                    continue; // 跳过空appId或已处理过的appId
                }
                
                // 获取交易统计
                Map<String, Object> stats = orderService.getTransactionStatisticsByAppIdAndTimeRange(
                        appId, startTime, endTime);
                
                // 创建商户统计对象
                MerchantTransactionDTO dto = new MerchantTransactionDTO();
                dto.setAppId(appId);
                dto.setContactor(order.getContactor());
                dto.setPhone(order.getPhone());
                dto.setTotalAmount((BigDecimal) stats.get("totalAmount"));
                dto.setTransactionCount((Integer) stats.get("transactionCount"));
                merchantStats.add(dto);
            }
        }
        
        // 根据交易金额排序并设置排名
        merchantStats.sort((a, b) -> b.getTotalAmount().compareTo(a.getTotalAmount()));
        for (int i = 0; i < merchantStats.size(); i++) {
            merchantStats.get(i).setRank(i + 1);
        }
        
        return merchantStats;
    }
} 