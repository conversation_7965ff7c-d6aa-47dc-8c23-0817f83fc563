package com.kdsjkj.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取项目根目录的绝对路径
        String projectRoot = System.getProperty("user.dir");
        String uploadPath = projectRoot + File.separator + "upload" + File.separator;
        
        // 确保路径以file:///开头且以/结尾
        String fileProtocolPath = "file:///" + uploadPath.replace("\\", "/");
        if (!fileProtocolPath.endsWith("/")) {
            fileProtocolPath += "/";
        }
        
        // 配置upload目录的访问映射
        registry.addResourceHandler("/upload/**")  // 访问路径
                .addResourceLocations(fileProtocolPath)  // 映射到本地目录
                .setCachePeriod(3600)  // 缓存一小时
                .resourceChain(true);  // 开启资源链
        
        // 打印配置信息用于调试
        System.out.println("=== WebConfig 静态资源配置 ===");
        System.out.println("项目根目录: " + projectRoot);
        System.out.println("上传目录路径: " + uploadPath);
        System.out.println("文件协议路径: " + fileProtocolPath);
        System.out.println("访问映射: /upload/** -> " + fileProtocolPath);
        System.out.println("================================");
    }
} 