package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.CarouselImage;
import com.kdsjkj.service.ICarouselImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import javax.annotation.PostConstruct;

/**
 * <p>
 * 轮播图表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@RestController
@RequestMapping("/carousel-image")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class CarouselImageController {

    @Autowired
    private ICarouselImageService carouselImageService;

    // 轮播图保存路径
    private static final String UPLOAD_PATH = "upload/carousel/";
    
    // 上传目录的绝对路径
    private String absoluteUploadPath;
    
    @PostConstruct
    public void init() {
        // 获取项目根目录的绝对路径
        String projectRoot = System.getProperty("user.dir");
        // 确保路径分隔符正确
        absoluteUploadPath = projectRoot + File.separator + UPLOAD_PATH.replace("/", File.separator);
        
        // 初始化上传目录，确保目录存在
        File uploadDir = new File(absoluteUploadPath);
        if (!uploadDir.exists()) {
            boolean created = uploadDir.mkdirs();
            log.info("初始化轮播图上传目录: {}, 创建结果: {}", uploadDir.getAbsolutePath(), created);
            
            if (!created) {
                log.error("无法创建上传目录: {}", uploadDir.getAbsolutePath());
            }
        } else {
            log.info("轮播图上传目录已存在: {}", uploadDir.getAbsolutePath());
        }
        
        // 验证目录权限
        if (uploadDir.exists()) {
            log.info("目录权限检查 - 可读: {}, 可写: {}", uploadDir.canRead(), uploadDir.canWrite());
        }
        
        log.info("项目根目录: {}", projectRoot);
        log.info("轮播图上传目录绝对路径: {}", absoluteUploadPath);
    }

    /**
     * 测试接口 - 验证控制器是否正常工作
     */
    @GetMapping("/test")
    public Result<String> test() {
        log.info("轮播图控制器测试接口被调用");
        return Result.success("轮播图控制器工作正常", "CarouselImageController is working");
    }

    /**
     * 添加轮播图
     */
    @PostMapping("/add")
    public Result addCarouselImage(
            @RequestParam("carouselImage") MultipartFile carouselImage,
            @RequestParam("appid") String appid,
            @RequestParam(value = "sortOrder", required = false, defaultValue = "0") Integer sortOrder) {
        
        try {
            // 参数校验
            if (carouselImage == null || carouselImage.isEmpty()) {
                return Result.paramError("轮播图不能为空");
            }
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid不能为空");
            }



            // 保存图片文件
            String imageUrl = saveCarouselImage(carouselImage);
            if (imageUrl == null) {
                return Result.error("图片上传失败");
            }

            // 创建轮播图对象
            CarouselImage carousel = new CarouselImage();
            carousel.setCarouselUrl(imageUrl);
            carousel.setAppid(appid.trim());
            carousel.setSortOrder(sortOrder != null ? sortOrder : 0);
            carousel.setStatus(true); // 默认启用
            carousel.setCreatedTime(LocalDateTime.now());
            carousel.setUpdatedTime(LocalDateTime.now());

            // 保存到数据库
            boolean success = carouselImageService.save(carousel);
            if (success) {
                log.info("轮播图添加成功 - ID: {}, appid: {}", carousel.getId(), appid);
                return Result.success("轮播图添加成功", carousel);
            } else {
                return Result.error("轮播图添加失败");
            }

        } catch (Exception e) {
            log.error("添加轮播图异常 - appid: {}", appid, e);
            return Result.error("添加轮播图异常: " + e.getMessage());
        }
    }

    /**
     * 删除轮播图
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteCarouselImage(@PathVariable("id") Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("轮播图ID不能为空");
            }

            log.info("删除轮播图 - ID: {}", id);

            // 先查询轮播图是否存在
            CarouselImage carouselImage = carouselImageService.getById(id);
            if (carouselImage == null) {
                return Result.error("轮播图不存在");
            }

            // 删除轮播图
            boolean success = carouselImageService.removeById(id);
            if (success) {
                log.info("轮播图删除成功 - ID: {}, appid: {}", id, carouselImage.getAppid());
                return Result.success("轮播图删除成功");
            } else {
                return Result.error("轮播图删除失败");
            }

        } catch (Exception e) {
            log.error("删除轮播图异常 - ID: {}", id, e);
            return Result.error("删除轮播图异常: " + e.getMessage());
        }
    }

    /**
     * 根据appid查询轮播图列表
     */
    @GetMapping("/list")
    public Result getCarouselImageList(@RequestParam("appid") String appid) {
        try {
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid不能为空");
            }

            log.info("查询轮播图列表 - appid: {}", appid);

            // 查询该appid下的所有轮播图，按sort_order和创建时间排序
            List<CarouselImage> carouselList = carouselImageService.getCarouselImagesByAppid(appid);
            
            log.info("查询到轮播图数量: {} - appid: {}", carouselList.size(), appid);
            return Result.success("查询成功", carouselList);

        } catch (Exception e) {
            log.error("查询轮播图列表异常 - appid: {}", appid, e);
            return Result.error("查询轮播图列表异常: " + e.getMessage());
        }
    }

    /**
     * 保存轮播图
     */
    private String saveCarouselImage(MultipartFile file) {
        try {
            log.info("开始保存轮播图，原文件名: {}, 文件大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());

            // 验证文件
            if (file.isEmpty()) {
                throw new IOException("上传文件为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new IOException("只支持图片文件上传，当前文件类型: " + contentType);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = ".jpg"; // 默认扩展名
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = "carousel_" + UUID.randomUUID().toString() + extension;
            
            // 构建完整的文件路径 - 使用绝对路径
            File targetFile = new File(absoluteUploadPath, fileName);
            
            log.info("目标文件路径: {}", targetFile.getAbsolutePath());
            
            // 确保父目录存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                log.info("创建父目录: {}, 结果: {}", parentDir.getAbsolutePath(), created);
                
                if (!created && !parentDir.exists()) {
                    throw new IOException("无法创建上传目录: " + parentDir.getAbsolutePath());
                }
            }

            // 验证目录权限
            if (parentDir != null && (!parentDir.exists() || !parentDir.canWrite())) {
                throw new IOException("上传目录不可写: " + parentDir.getAbsolutePath());
            }
            
            // 保存文件
            file.transferTo(targetFile);
            
            // 验证文件是否保存成功
            if (!targetFile.exists() || targetFile.length() == 0) {
                throw new IOException("文件保存失败，目标文件不存在或大小为0");
            }
            
            // 构建用于Web访问的相对路径
            String webAccessPath = UPLOAD_PATH + fileName;
            
            log.info("轮播图保存成功 - 文件路径: {}, 文件大小: {} bytes, 访问路径: {}", 
                    targetFile.getAbsolutePath(), targetFile.length(), webAccessPath);
            
            // 返回相对路径用于Web访问
            return webAccessPath;
            
        } catch (IOException e) {
            log.error("保存轮播图失败: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("保存轮播图发生未知异常: {}", e.getMessage(), e);
            return null;
        }
    }
}
