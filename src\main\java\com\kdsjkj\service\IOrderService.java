package com.kdsjkj.service;

import com.kdsjkj.entity.Order;
import com.baomidou.mybatisplus.extension.service.IService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface IOrderService extends IService<Order> {

    /**
     * 根据商户订单号更新交易状态
     */
    boolean updateTradeStatusByOutTradeNo(String outTradeNo, String tradeStatus);

    /**
     * 根据支付宝交易号更新交易状态
     */
    boolean updateTradeStatusByTradeNo(String tradeNo, String tradeStatus);

    /**
     * 根据商户订单号查询订单
     */
    Order getByOutTradeNo(String outTradeNo);

    /**
     * 根据支付宝交易号查询订单
     */
    Order getByTradeNo(String tradeNo);

    /**
     * 根据支付宝交易号获取appId
     */
    String getAppIdByTradeNo(String tradeNo);

    /**
     * 根据appId和时间范围获取交易统计
     * 
     * @param appId 小程序appId
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 包含交易笔数和总金额的Map
     */
    Map<String, Object> getTransactionStatisticsByAppIdAndTimeRange(String appId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据appId列表获取交易统计
     * 
     * @param appIds 小程序appId列表
     * @return 包含交易笔数和总金额的Map
     */
    Map<String, Object> getTransactionStatisticsByAppIds(List<String> appIds);
    
    /**
     * 根据appId列表和今日时间获取交易统计
     * 
     * @param appIds 小程序appId列表
     * @return 包含交易笔数和总金额的Map
     */
    Map<String, Object> getTodayTransactionStatisticsByAppIds(List<String> appIds);

}
