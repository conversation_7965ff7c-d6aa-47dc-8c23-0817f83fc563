package com.kdsjkj.service;

import com.kdsjkj.entity.Product;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface IProductService extends IService<Product> {

    /**
     * 根据appid查询商品列表
     * 
     * @param appid 小程序APPID
     * @return 商品列表
     */
    List<Product> getProductsByAppid(String appid);

}
