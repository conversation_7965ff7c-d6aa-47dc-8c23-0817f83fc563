package com.kdsjkj.获取推广码;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.FileWriter;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Scanner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-08 23:47
 */
public class Getma {
    public static void main(String[] args) throws IOException {
        String url = "https://b.alipay.com/page/fw-portal/itemQrCodeToIsv?marketCode=OFFLINE_PROMOTION&merchandiseId=CM010301000000292210&ticket=2052zvb&callback=https://zf.kdsjkj.com/api/alipay/health&appName=小程序测试版2052";

        System.out.println("正在使用纯HTTP方式加载页面...");
        
        // 使用纯HTTP连接获取页面内容
        String htmlContent = getHtmlContent(url);
        
        // 使用正则表达式直接从HTML源码中查找二维码URL
        String qrCodeUrl = null;
        String qrCodeValue = null;
        
        // 1. 首先尝试查找完整的二维码URL
        Pattern pattern = Pattern.compile("https://mobilecodec\\.alipay\\.com/show\\.htm\\?code=([^&\"'\\s]+)");
        Matcher matcher = pattern.matcher(htmlContent);
        if (matcher.find()) {
            qrCodeUrl = matcher.group(0);
            qrCodeValue = matcher.group(1);
            System.out.println("通过正则表达式找到二维码URL: " + qrCodeUrl);
            System.out.println("二维码值: " + qrCodeValue);
        } else {
            // 2. 如果没找到，尝试解析HTML
            Document document = Jsoup.parse(htmlContent);
            
            // 查找特定格式的支付宝二维码链接
            Elements imgElements = document.select("img[src*=mobilecodec.alipay.com/show.htm]");
            if (!imgElements.isEmpty()) {
                Element img = imgElements.first();
                qrCodeUrl = img.attr("src");
                
                // 提取code参数
                pattern = Pattern.compile("code=([^&]+)");
                matcher = pattern.matcher(qrCodeUrl);
                if (matcher.find()) {
                    qrCodeValue = matcher.group(1);
                }
                
                System.out.println("找到支付宝二维码URL: " + qrCodeUrl);
                System.out.println("二维码值: " + qrCodeValue);
            } else {
                // 3. 查找带有特定class的图片
                imgElements = document.select("img.img___MR_lU");
                if (!imgElements.isEmpty()) {
                    Element img = imgElements.first();
                    qrCodeUrl = img.attr("src");
                    
                    // 提取code参数
                    pattern = Pattern.compile("code=([^&]+)");
                    matcher = pattern.matcher(qrCodeUrl);
                    if (matcher.find()) {
                        qrCodeValue = matcher.group(1);
                    }
                    
                    System.out.println("通过class找到二维码URL: " + qrCodeUrl);
                    System.out.println("二维码值: " + qrCodeValue);
                }
            }
            
            // 4. 如果还没找到，保存所有图片链接
            if (qrCodeUrl == null) {
                System.out.println("未找到二维码URL，将保存所有图片链接以供分析");
                
                // 保存所有图片链接，以便分析
                Elements allImages = document.select("img");
                System.out.println("页面包含 " + allImages.size() + " 个图片元素");
                try (FileWriter fileWriter = new FileWriter("all_image_urls.txt")) {
                    for (Element img : allImages) {
                        String src = img.attr("src");
                        String className = img.attr("class");
                        fileWriter.write("图片URL: " + src + "\n");
                        fileWriter.write("图片类名: " + className + "\n");
                        fileWriter.write("-------------------\n");
                    }
                    System.out.println("已将所有图片URL保存到 all_image_urls.txt 文件中");
                } catch (IOException e) {
                    System.err.println("保存图片URL时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        
        // 将HTML保存到文件中
        try (FileWriter fileWriter = new FileWriter("promotion_page.html")) {
            fileWriter.write(htmlContent);
            System.out.println("已将页面内容保存到 promotion_page.html 文件中");
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        if (qrCodeUrl != null) {
            // 将二维码URL保存到单独的文件
            try (FileWriter fileWriter = new FileWriter("qrcode_url.txt")) {
                fileWriter.write(qrCodeUrl);
                System.out.println("已将二维码URL保存到 qrcode_url.txt 文件中");
            } catch (IOException e) {
                System.err.println("保存二维码URL时出错: " + e.getMessage());
                e.printStackTrace();
            }
            
            // 保存二维码值到单独文件
            if (qrCodeValue != null) {
                try (FileWriter fileWriter = new FileWriter("qrcode_value.txt")) {
                    fileWriter.write(qrCodeValue);
                    System.out.println("已将二维码值保存到 qrcode_value.txt 文件中");
                } catch (IOException e) {
                    System.err.println("保存二维码值时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            System.out.println("未找到二维码URL，请检查保存的HTML文件分析页面结构");
            
            // 尝试分析页面结构，查找可能的线索
            Document document = Jsoup.parse(htmlContent);
            Elements scripts = document.select("script");
            System.out.println("页面包含 " + scripts.size() + " 个脚本标签");
            
            // 查找可能包含二维码生成逻辑的脚本
            for (Element script : scripts) {
                String scriptContent = script.html();
                if (scriptContent.contains("mobilecodec") || scriptContent.contains("qrcode") || scriptContent.contains("QRCode")) {
                    System.out.println("找到可能包含二维码生成逻辑的脚本:");
                    System.out.println(scriptContent.substring(0, Math.min(200, scriptContent.length())) + "...");
                }
            }
        }
    }
    
    /**
     * 使用纯HTTP连接获取页面内容
     */
    private static String getHtmlContent(String urlString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // 设置请求头，模拟浏览器
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        connection.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        connection.setRequestProperty("Connection", "keep-alive");
        connection.setRequestProperty("Cache-Control", "max-age=0");
        
        // 获取响应
        int responseCode = connection.getResponseCode();
        System.out.println("HTTP响应码: " + responseCode);
        
        // 读取内容
        StringBuilder content = new StringBuilder();
        try (Scanner scanner = new Scanner(connection.getInputStream())) {
            while (scanner.hasNextLine()) {
                content.append(scanner.nextLine()).append("\n");
            }
        }
        
        return content.toString();
    }
} 