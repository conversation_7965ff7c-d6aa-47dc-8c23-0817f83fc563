package com.kdsjkj.controller;

import com.kdsjkj.entity.ActivationRewards;
import com.kdsjkj.service.IActivationRewardsService;
import com.kdsjkj.dto.UpdateActivationRewardRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.math.BigDecimal;

/**
 * <p>
 * 激活奖励表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/api/activation-rewards")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class ActivationRewardsController {

    @Autowired
    private IActivationRewardsService activationRewardsService;
    
    /**
     * 获取激活奖励配置
     */
    @GetMapping("/config")
    public ResponseEntity<HashMap<String, Object>> getConfig() {
        HashMap<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取激活奖励配置");
            
            ActivationRewards config = activationRewardsService.getActivationReward();
            
            response.put("success", true);
            response.put("data", config);
            response.put("message", "获取激活奖励配置成功");
            
            log.info("获取激活奖励配置成功");
            
        } catch (Exception e) {
            log.error("获取激活奖励配置失败", e);
            response.put("success", false);
            response.put("message", "获取激活奖励配置失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 更新激活奖励配置
     */
    @PutMapping("/config/{id}")
    public ResponseEntity<HashMap<String, Object>> updateConfig(
            @PathVariable Long id,
            @RequestBody UpdateActivationRewardRequest request) {
        
        HashMap<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始更新激活奖励配置 - ID: {}", id);
            
            // 参数验证
            if (request.getMinPaymentAmount() == null || request.getMinPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
                response.put("success", false);
                response.put("message", "最低收款金额必须大于0");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (request.getRewardAmount() == null || request.getRewardAmount().compareTo(BigDecimal.ZERO) <= 0) {
                response.put("success", false);
                response.put("message", "奖励金额必须大于0");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 更新配置
            ActivationRewards updatedConfig = activationRewardsService.updateActivationReward(id, request);
            
            response.put("success", true);
            response.put("data", updatedConfig);
            response.put("message", "更新激活奖励配置成功");
            
            log.info("更新激活奖励配置成功 - ID: {}", id);
            
        } catch (Exception e) {
            log.error("更新激活奖励配置失败 - ID: {}", id, e);
            response.put("success", false);
            response.put("message", "更新激活奖励配置失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
}
