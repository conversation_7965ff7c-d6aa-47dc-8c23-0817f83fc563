package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付宝服务市场订单通知表实体
 */
@Data
@TableName("alipay_service_order")
public class AlipayServiceOrder {
    
    @TableId(type = IdType.AUTO)
    private Long id; // 主键ID
    
    @TableField("notify_id")
    private String notifyId; // 通知ID
    
    @TableField("notify_type")
    private String notifyType; // 通知类型
    
    @TableField("merchant_pid")
    private String merchantPid; // 商户PID
    
    @TableField("commodity_order_id")
    private String commodityOrderId; // 订单ID
    
    @TableField("order_time")
    private Date orderTime; // 订单时间
    
    @TableField("notify_time")
    private Date notifyTime; // 通知时间
    
    @TableField("app_id")
    private String appId; // 应用ID
    
    @TableField("consumer_miniapp_id")
    private String consumerMiniappId; // 消费者小程序ID
    
    @TableField("consumer_app_name")
    private String consumerAppName; // 消费者应用名称
    
    @TableField("item_code")
    private String itemCode; // 商品编码
    
    @TableField("title")
    private String title; // 商品标题
    
    @TableField("specifications")
    private String specifications; // 规格
    
    @TableField("quantity")
    private Integer quantity; // 数量
    
    @TableField("total_price")
    private BigDecimal totalPrice; // 总价
    
    @TableField("period_day")
    private Integer periodDay; // 服务期限
    
    @TableField("contactor")
    private String contactor; // 联系人
    
    @TableField("phone")
    private String phone; // 联系电话
    
    @TableField("is_fast_open")
    private Boolean isFastOpen; // 是否快速开通
    
    @TableField("sign")
    private String sign; // 签名
    
    @TableField("sign_type")
    private String signType; // 签名类型
    
    @TableField("raw_content")
    private String rawContent; // 原始内容
    
    @TableField("process_status")
    private String processStatus; // 处理状态：PENDING-待处理，SUCCESS-成功，FAILED-失败
    
    @TableField("error_msg")
    private String errorMsg; // 错误信息
    
    @TableField("create_time")
    private Date createTime; // 创建时间
    
    @TableField("update_time")
    private Date updateTime; // 更新时间
} 