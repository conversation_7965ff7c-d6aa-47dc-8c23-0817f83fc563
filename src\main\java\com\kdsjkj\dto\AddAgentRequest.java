package com.kdsjkj.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 添加代理人请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-12
 */
@Data
public class AddAgentRequest {
    
    /**
     * 代理姓名
     */
    @NotBlank(message = "代理人姓名不能为空")
    @Size(min = 2, max = 50, message = "代理人姓名长度必须在2-50个字符之间")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户唯一标识不能为空")
    @Size(min = 2, max = 50, message = "用户唯一标识长度必须在2-50个字符之间")
    private String uid;

    /**
     * 费率（例如：0.0038表示0.38%）
     */
    @NotNull(message = "费率不能为空")
    @DecimalMin(value = "0.0001", message = "费率必须大于0.0001")
    private BigDecimal rate;

    /**
     * 上级代理UID（可选）
     */
    private String parentUid;
} 