package com.kdsjkj.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayTradeCreateResponse;
import com.kdsjkj.service.AlipayAuthService;
import com.kdsjkj.appTest.userID.AlipaySystemOauthToken;
import com.kdsjkj.appTest.支付.createPay.AlipayTradeCreateService;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.kdsjkj.config.AlipayMiniProgramConfig;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.service.IAlipayConfigFrontService;
import com.kdsjkj.entity.Order;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.service.IRoyaltySettleService;
import com.kdsjkj.service.IAgentCommissionService;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IAgentPointsService;
import com.kdsjkj.constant.TradeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝授权回调控制器
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/api/alipay")
@CrossOrigin(origins = "*", allowedHeaders = "*")  // 允许跨域访问
public class AlipayAuthController {
    
    @Autowired
    private AlipayAuthService alipayAuthService;
    

    
    @Autowired
    private AlipayTradeCreateService alipayTradeCreateService;

    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;

    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IRoyaltySettleService royaltySettleService;

    @Autowired
    private IAgentCommissionService agentCommissionService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IAgentPointsService agentPointsService;


    /**
     * 接收支付宝授权成功通知
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/notifyAuth")
    public String notifyAuth(HttpServletRequest request) {
        try {
            String result = alipayAuthService.processAuthNotify(request);
            log.info("授权通知处理结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("处理授权通知异常", e);
            return "fail";
        }
    }
    
    @PostMapping("/gateway")
    public String notifyAuth2(HttpServletRequest request) {
        try {
            String result = alipayAuthService.processGateWayNotify(request);
            log.info("授权通知处理结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("处理授权通知异常", e);
            return "fail";
        }
    }
    
    /**
     * 接收小程序授权码，获取用户ID并创建订单
     * 
     * @param authCode 授权码
     * @param amount 订单金额（可选，默认0.01）
     * @param subject 订单标题（可选）
     * @return 订单创建结果和交易号
     */
    @PostMapping("/auth")
    @ResponseBody
    public Map<String, Object> receiveAuthCode(@RequestParam(required = true) String authCode,
                                              @RequestParam(required = false, defaultValue = "0.01") String amount,
                                              @RequestParam(required = false, defaultValue = "测试商品订单") String subject,
                                               @RequestParam(required = true, defaultValue = "小程序appId") String appId ) {
        Map<String, Object> response = new HashMap<>();
        try {
            log.info("收到授权码: {}, 金额: {}, 商品: {}", authCode, amount, subject);
            
            // 1. 通过授权码获取访问令牌和用户ID
   
                AlipaySystemOauthTokenResponse tokenResponse = getAccessToken(authCode,appId);
                log.info("tokenResponse: {}", tokenResponse);
                
                if (tokenResponse.isSuccess()) {
                    String userId = tokenResponse.getUserId();
                    log.info("获取到用户ID: {}", userId);
                // 2. 使用获取的用户ID创建订单
                String outTradeNo = "ORDER_" + System.currentTimeMillis(); // 生成唯一订单号
                AlipayTradeCreateResponse createResponse = alipayTradeCreateService.createOrder(
                    outTradeNo, 
                    amount, 
                    subject, 
                    userId,
                        appId
                );
                
                if (createResponse.isSuccess()) {
                    // 3. 保存订单信息到数据库
                    try {
                        Order order = new Order();
                        order.setAppId(appId);
                        order.setOutTradeNo(createResponse.getOutTradeNo());
                        order.setAmount(new java.math.BigDecimal(amount));
                        order.setTradeNo(createResponse.getTradeNo());
                        order.setTradeStatus(TradeStatus.WAIT_BUYER_PAY);
                        order.setCreateTime(java.time.LocalDateTime.now());
                        
                        boolean saveResult = orderService.save(order);
                        if (saveResult) {
                            log.info("订单信息保存到数据库成功 - ID: {}", order.getId());
                        } else {
                            log.error("订单信息保存到数据库失败");
                        }
                    } catch (Exception e) {
                        log.error("保存订单信息到数据库异常", e);
                        // 不影响主流程，只记录错误
                    }
                    
                    // 4. 返回成功结果和交易号
                    response.put("success", true);
                    response.put("message", "授权成功，订单创建成功");
                    response.put("userId", userId);
                    response.put("outTradeNo", createResponse.getOutTradeNo());
                    response.put("tradeNo", createResponse.getTradeNo());
                    response.put("orderInfo", createResponse.getBody()); // 完整的订单信息
                    response.put("amount", amount);
                    response.put("subject", subject);
                    
                    log.info("订单创建成功 - 商户订单号: {}, 支付宝交易号: {}, 金额: {}", 
                            createResponse.getOutTradeNo(), createResponse.getTradeNo(), amount);
                } else {
                    // 订单创建失败
                    response.put("success", false);
                    response.put("message", "授权成功，但订单创建失败: " + createResponse.getSubMsg());
                    response.put("userId", userId);
                    log.error("订单创建失败: {}", createResponse.getSubMsg());
                }

                } else {
                    response.put("success", false);
                    response.put("message", "授权失败: " + tokenResponse.getSubMsg());
                    log.error("授权失败: {}", tokenResponse.getSubMsg());
                    return response;
                }
                


            
        } catch (AlipayApiException e) {
            log.error("处理授权码异常", e);
            response.put("success", false);
            response.put("message", "处理授权码异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            response.put("success", false);
            response.put("message", "系统异常: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 支付宝支付回调接口
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/createPay")
    public String payNotify(HttpServletRequest request) {
        try {
            // 获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            
            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }
            
            log.info("收到支付回调通知: {}", params);
            
            // 这里可以添加验签和业务处理逻辑
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String totalAmount = params.get("total_amount");
            
            log.info("支付状态: {}, 商户订单号: {}, 支付宝交易号: {}, 金额: {}", 
                    tradeStatus, outTradeNo, tradeNo, totalAmount);
            
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                log.info("支付成功 - 订单号: {}", outTradeNo);
                
                // 更新订单状态为支付成功
                try {
                    boolean updateResult = orderService.updateTradeStatusByOutTradeNo(outTradeNo, TradeStatus.TRADE_SUCCESS);
                    if (updateResult) {
                        log.info("订单状态更新成功 - 订单号: {}, 状态: {}", outTradeNo, TradeStatus.TRADE_SUCCESS);
                        
                        // 订单状态更新成功后，执行分账逻辑
                        if (tradeNo != null && !tradeNo.trim().isEmpty() && 
                            totalAmount != null && !totalAmount.trim().isEmpty()) {
                            
                            log.info("开始执行分账逻辑 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);
                            
                            try {
                                boolean royaltyResult = royaltySettleService.executeRoyaltySettle(tradeNo, totalAmount);
                                if (royaltyResult) {
                                    log.info("✅ 分账执行成功 - 交易号: {}", tradeNo);
                                } else {
                                    log.error("❌ 分账执行失败 - 交易号: {}", tradeNo);
                                    // 分账失败不影响主流程，继续返回success
                                }
                            } catch (Exception e) {
                                log.error("分账执行异常 - 交易号: {}", tradeNo, e);
                                // 分账异常不影响主流程，继续返回success
                            }
                        } else {
                            log.warn("分账参数不完整，跳过分账逻辑 - tradeNo: {}, totalAmount: {}", tradeNo, totalAmount);
                        }

                        // 计算每个代理分多少钱
                        if (tradeNo != null && !tradeNo.trim().isEmpty() && 
                            totalAmount != null && !totalAmount.trim().isEmpty()) {
                            
                            log.info("🎯 开始计算代理分润 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);
                            
                            // 1. 先计算最底层代理分润（商户费率 - 最底层代理费率）
                            try {
                                boolean bottomCommissionResult = agentCommissionService.calculateAndSaveBottomAgentCommission(tradeNo, totalAmount);
                                if (bottomCommissionResult) {
                                    log.info("✅ 最底层代理分润计算完成 - 交易号: {}", tradeNo);
                                } else {
                                    log.error("❌ 最底层代理分润计算失败 - 交易号: {}", tradeNo);
                                    // 分润失败不影响主流程，继续执行
                                }
                            } catch (Exception e) {
                                log.error("最底层代理分润计算异常 - 交易号: {}", tradeNo, e);
                                // 分润异常不影响主流程，继续执行
                            }
                            
                            // 2. 再计算上级代理之间的分润
                            try {
                                boolean commissionResult = agentCommissionService.calculateAndSaveAgentCommission(tradeNo, totalAmount);
                                if (commissionResult) {
                                    log.info("✅ 上级代理分润计算完成 - 交易号: {}", tradeNo);
                                } else {
                                    log.error("❌ 上级代理分润计算失败 - 交易号: {}", tradeNo);
                                    // 分润失败不影响主流程，继续返回success
                                }
                            } catch (Exception e) {
                                log.error("上级代理分润计算异常 - 交易号: {}", tradeNo, e);
                                // 分润异常不影响主流程，继续返回success
                            }
                        } else {
                            log.warn("代理分润参数不完整，跳过分润逻辑 - tradeNo: {}, totalAmount: {}", tradeNo, totalAmount);
                        }

                        // 计算代理积分奖励（每满100元奖励1积分）
                        if (tradeNo != null && !tradeNo.trim().isEmpty() &&
                            totalAmount != null && !totalAmount.trim().isEmpty()) {

                            log.info("🎯 开始计算代理积分奖励 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);

                            try {
                                boolean pointsResult = calculateAndSaveAgentPoints(tradeNo, totalAmount);
                                if (pointsResult) {
                                    log.info("✅ 代理积分奖励计算完成 - 交易号: {}", tradeNo);
                                } else {
                                    log.error("❌ 代理积分奖励计算失败 - 交易号: {}", tradeNo);
                                    // 积分奖励失败不影响主流程，继续执行
                                }
                            } catch (Exception e) {
                                log.error("代理积分奖励计算异常 - 交易号: {}", tradeNo, e);
                                // 积分奖励异常不影响主流程，继续执行
                            }
                        } else {
                            log.warn("代理积分奖励参数不完整，跳过积分奖励逻辑 - tradeNo: {}, totalAmount: {}", tradeNo, totalAmount);
                        }

                    } else {
                        log.error("订单状态更新失败 - 订单号: {}", outTradeNo);
                    }
                } catch (Exception e) {
                    log.error("更新订单状态异常 - 订单号: {}", outTradeNo, e);
                }
            }
            
            return "success";
        } catch (Exception e) {
            log.error("处理支付回调异常", e);
            return "fail";
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public String health() {
        return "success";
    }

    /**
     * 通过授权码获取访问令牌
     * @param authCode 授权码
     * @return 访问令牌响应
     * @throws AlipayApiException
     */
    private AlipaySystemOauthTokenResponse getAccessToken(String authCode,String appid) throws AlipayApiException {
        log.info("=== 开始获取访问令牌 ===");
        log.info("授权码: {}", authCode);
        
        // 使用统一配置类，确保签名配置正确
        AlipayConfig alipayConfig = getAlipayConfig();

        // 从alipay_auth_record表中获取app_auth_token
        String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
        if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
            throw new AlipayApiException("未找到对应appid的授权令牌信息");
        }
        
        log.info("配置信息:");
        log.info("- AppId: {}", alipayConfig.getAppId());
        log.info("- 网关地址: {}", alipayConfig.getServerUrl());
        log.info("- 签名类型: {}", alipayConfig.getSignType());
        log.info("- 字符编码: {}", alipayConfig.getCharset());
        log.info("- app_auth_token: {}", appAuthToken);
        log.info("- 私钥长度: {}", alipayConfig.getPrivateKey() != null ? alipayConfig.getPrivateKey().length() : "null");
        log.info("- 支付宝公钥长度: {}", alipayConfig.getAlipayPublicKey() != null ? alipayConfig.getAlipayPublicKey().length() : "null");
        
        // 构造请求参数
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);
        request.setGrantType("authorization_code");
        
        // 设置第三方应用授权令牌
        request.putOtherTextParam("app_auth_token", appAuthToken);
        
        log.info("请求参数:");
        log.info("- 授权码: {}", authCode);
        log.info("- 授权类型: authorization_code");
        log.info("- app_auth_token: {}", appAuthToken);

        try {
            // 使用统一配置初始化客户端
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            
            log.info("发送请求到支付宝...");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
            
            log.info("支付宝响应:");
            log.info("- 完整响应: {}", response.getBody());
            log.info("- 是否成功: {}", response.isSuccess());
            log.info("- 错误码: {}", response.getCode());
            log.info("- 错误信息: {}", response.getMsg());
            log.info("- 子错误码: {}", response.getSubCode());
            log.info("- 子错误信息: {}", response.getSubMsg());
            
            if (response.isSuccess()) {
                log.info("✅ 获取访问令牌成功");
                log.info("- 用户ID: {}", response.getUserId());
                log.info("- 访问令牌: {}", response.getAccessToken());
                log.info("- 刷新令牌: {}", response.getRefreshToken());
                return response;
            } else {
                log.error("❌ 获取访问令牌失败");
                
                // 分析具体错误
                if ("40004".equals(response.getCode())) {
                    log.error("业务处理失败，可能原因:");
                    log.error("1. app_auth_token 无效或过期");
                    log.error("2. 授权码已过期或已使用");
                    log.error("3. 小程序与第三方应用关联不完整");
                } else if ("40001".equals(response.getCode())) {
                    log.error("缺少必选参数");
                } else if ("40002".equals(response.getCode())) {
                    log.error("非法的参数");
                }
                
                throw new AlipayApiException("获取访问令牌失败: " + response.getSubMsg());
            }
            
        } catch (AlipayApiException e) {
            log.error("❌ 调用支付宝API异常", e);
            
            // 检查是否是签名错误
            if (e.getMessage().contains("sign check fail")) {
                log.error("🔧 签名验证失败，可能原因:");
                log.error("1. 应用私钥配置错误");
                log.error("2. 支付宝公钥配置错误");
                log.error("3. 签名类型不匹配");
                log.error("4. 字符编码不正确");
                log.error("5. 小程序未正确继承第三方应用的签名配置");
                
                log.error("💡 建议解决方案:");
                log.error("1. 检查支付宝开放平台的应用配置");
                log.error("2. 确认小程序已正确关联到第三方应用");
                log.error("3. 验证 app_auth_token 的有效性");
                log.error("4. 重新构建小程序以继承配置");
            }
            
            throw e;
        }
    }

    /**
     * 从数据库获取支付宝配置
     */
    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();
        
        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }

    /**
     * 计算并保存代理积分奖励
     * 每满100元奖励1积分
     *
     * @param tradeNo 支付宝交易号
     * @param totalAmount 交易总金额
     * @return 处理是否成功
     */
    private boolean calculateAndSaveAgentPoints(String tradeNo, String totalAmount) {
        try {
            log.info("🎯 开始计算代理积分奖励 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);

            // 1. 通过tradeNo在order表中查找对应appid
            Order order = orderService.lambdaQuery()
                    .eq(Order::getTradeNo, tradeNo)
                    .one();

            if (order == null) {
                log.warn("❌ 未找到对应的订单记录 - 交易号: {}", tradeNo);
                return false;
            }

            String appId = order.getAppId();
            if (appId == null || appId.trim().isEmpty()) {
                log.warn("❌ 订单中appId为空 - 交易号: {}", tradeNo);
                return false;
            }

            log.info("📱 找到对应appId: {}", appId);

            // 2. 通过appid在ServiceMarketOrder表中匹配consumerMiniappid找到agentUid
            String agentUid = serviceMarketOrderService.getAgentUidByAppid(appId);
            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.warn("❌ 未找到对应的代理UID - appId: {}", appId);
                return false;
            }

            log.info("👤 找到对应代理UID: {}", agentUid);

            // 3. 计算积分（每满100元奖励1积分）
            BigDecimal amount = new BigDecimal(totalAmount);
            int points = amount.divide(new BigDecimal("100"), 0, BigDecimal.ROUND_DOWN).intValue();

            if (points <= 0) {
                log.info("💰 交易金额不足100元，无积分奖励 - 金额: {}", totalAmount);
                return true; // 不是错误，只是没有积分奖励
            }

            log.info("🎁 计算得出积分奖励: {} 积分", points);

            // 4. 为代理添加积分
            boolean addPointsResult = agentPointsService.addPointsToAgent(agentUid, points);

            if (addPointsResult) {
                log.info("✅ 代理积分奖励发放成功 - 代理UID: {}, 奖励积分: {}, 交易金额: {}",
                        agentUid, points, totalAmount);
                return true;
            } else {
                log.error("❌ 代理积分奖励发放失败 - 代理UID: {}, 奖励积分: {}", agentUid, points);
                return false;
            }

        } catch (Exception e) {
            log.error("计算代理积分奖励异常 - 交易号: {}, 金额: {}", tradeNo, totalAmount, e);
            return false;
        }
    }

   }
