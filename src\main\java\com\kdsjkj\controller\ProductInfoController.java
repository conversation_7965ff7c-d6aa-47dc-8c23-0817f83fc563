package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.ProductInfo;
import com.kdsjkj.service.IProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import javax.annotation.PostConstruct;

/**
 * <p>
 * 商品信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/product-info")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class ProductInfoController {

    @Autowired
    private IProductInfoService productInfoService;

    // 图片保存路径
    private static final String UPLOAD_PATH = "upload/product-info/";
    
    // 上传目录的绝对路径
    private String absoluteUploadPath;
    
    @PostConstruct
    public void init() {
        // 获取项目根目录的绝对路径
        String projectRoot = System.getProperty("user.dir");
        // 确保路径分隔符正确
        absoluteUploadPath = projectRoot + File.separator + UPLOAD_PATH.replace("/", File.separator);
        
        // 初始化上传目录，确保目录存在
        File uploadDir = new File(absoluteUploadPath);
        if (!uploadDir.exists()) {
            boolean created = uploadDir.mkdirs();
            log.info("初始化商品信息图片上传目录: {}, 创建结果: {}", uploadDir.getAbsolutePath(), created);
            
            if (!created) {
                log.error("无法创建上传目录: {}", uploadDir.getAbsolutePath());
            }
        } else {
            log.info("商品信息图片上传目录已存在: {}", uploadDir.getAbsolutePath());
        }
        
        // 验证目录权限
        if (uploadDir.exists()) {
            log.info("目录权限检查 - 可读: {}, 可写: {}", uploadDir.canRead(), uploadDir.canWrite());
        }
        
        log.info("项目根目录: {}", projectRoot);
        log.info("上传目录绝对路径: {}", absoluteUploadPath);
    }

    /**
     * 创建商品信息
     */
    @PostMapping("/add")
    public Result addProductInfo(
            @RequestParam("image") MultipartFile image,
            @RequestParam("name") String name,
            @RequestParam("description") String description,
            @RequestParam("requiredPoints") Integer requiredPoints) {
        
        try {
            // 参数校验
            if (image == null || image.isEmpty()) {
                return Result.paramError("商品图片不能为空");
            }
            if (name == null || name.trim().isEmpty()) {
                return Result.paramError("商品名称不能为空");
            }
            if (description == null || description.trim().isEmpty()) {
                return Result.paramError("商品描述不能为空");
            }
            if (requiredPoints == null || requiredPoints < 0) {
                return Result.paramError("所需积分不能为空且不能小于0");
            }

            log.info("添加商品信息 - 商品名称: {}, 描述: {}, 所需积分: {}", name, description, requiredPoints);

            // 保存图片文件
            String imageUrl = saveProductInfoImage(image);
            if (imageUrl == null) {
                return Result.error("图片上传失败");
            }

            // 创建商品信息对象
            ProductInfo productInfo = new ProductInfo();
            productInfo.setName(name.trim());
            productInfo.setDescription(description.trim());
            productInfo.setRequiredPoints(requiredPoints);
            productInfo.setImageUrl(imageUrl);
            productInfo.setCreateTime(LocalDateTime.now());
            productInfo.setUpdateTime(LocalDateTime.now());

            // 保存到数据库
            boolean success = productInfoService.save(productInfo);
            if (success) {
                log.info("商品信息添加成功 - ID: {}, 商品名称: {}", productInfo.getId(), name);
                return Result.success("商品信息添加成功", productInfo);
            } else {
                return Result.error("商品信息添加失败");
            }

        } catch (Exception e) {
            log.error("添加商品信息异常 - 商品名称: {}", name, e);
            return Result.error("添加商品信息异常: " + e.getMessage());
        }
    }

    /**
     * 修改商品信息
     */
    @PutMapping("/update/{id}")
    public Result updateProductInfo(
            @PathVariable("id") Long id,
            @RequestParam(value = "image", required = false) MultipartFile image,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "requiredPoints", required = false) Integer requiredPoints) {
        
        try {
            if (id == null || id <= 0) {
                return Result.paramError("商品信息ID不能为空");
            }

            log.info("修改商品信息 - ID: {}", id);

            // 先查询商品信息是否存在
            ProductInfo productInfo = productInfoService.getById(id);
            if (productInfo == null) {
                return Result.error("商品信息不存在");
            }

            // 更新字段
            boolean hasUpdate = false;
            
            if (name != null && !name.trim().isEmpty()) {
                productInfo.setName(name.trim());
                hasUpdate = true;
            }
            
            if (description != null && !description.trim().isEmpty()) {
                productInfo.setDescription(description.trim());
                hasUpdate = true;
            }
            
            if (requiredPoints != null && requiredPoints >= 0) {
                productInfo.setRequiredPoints(requiredPoints);
                hasUpdate = true;
            }
            
            if (image != null && !image.isEmpty()) {
                String imageUrl = saveProductInfoImage(image);
                if (imageUrl != null) {
                    productInfo.setImageUrl(imageUrl);
                    hasUpdate = true;
                }
            }

            if (!hasUpdate) {
                return Result.paramError("至少需要更新一个字段");
            }

            productInfo.setUpdateTime(LocalDateTime.now());

            // 更新到数据库
            boolean success = productInfoService.updateById(productInfo);
            if (success) {
                log.info("商品信息修改成功 - ID: {}, 商品名称: {}", id, productInfo.getName());
                return Result.success("商品信息修改成功", productInfo);
            } else {
                return Result.error("商品信息修改失败");
            }

        } catch (Exception e) {
            log.error("修改商品信息异常 - ID: {}", id, e);
            return Result.error("修改商品信息异常: " + e.getMessage());
        }
    }

    /**
     * 删除商品信息
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteProductInfo(@PathVariable("id") Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("商品信息ID不能为空");
            }

            log.info("删除商品信息 - ID: {}", id);

            // 先查询商品信息是否存在
            ProductInfo productInfo = productInfoService.getById(id);
            if (productInfo == null) {
                return Result.error("商品信息不存在");
            }

            // 删除商品信息
            boolean success = productInfoService.removeById(id);
            if (success) {
                log.info("商品信息删除成功 - ID: {}, 商品名称: {}", id, productInfo.getName());
                return Result.success("商品信息删除成功");
            } else {
                return Result.error("商品信息删除失败");
            }

        } catch (Exception e) {
            log.error("删除商品信息异常 - ID: {}", id, e);
            return Result.error("删除商品信息异常: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询商品信息
     */
    @GetMapping("/get/{id}")
    public Result getProductInfoById(@PathVariable("id") Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("商品信息ID不能为空");
            }

            log.info("查询商品信息 - ID: {}", id);

            // 查询商品信息
            ProductInfo productInfo = productInfoService.getById(id);
            if (productInfo == null) {
                return Result.error("商品信息不存在");
            }

            log.info("查询商品信息成功 - ID: {}, 商品名称: {}", id, productInfo.getName());
            return Result.success("查询成功", productInfo);

        } catch (Exception e) {
            log.error("查询商品信息异常 - ID: {}", id, e);
            return Result.error("查询商品信息异常: " + e.getMessage());
        }
    }

    /**
     * 查询所有商品信息
     */
    @GetMapping("/list")
    public Result getProductInfoList() {
        try {
            log.info("查询所有商品信息");

            // 查询所有商品信息
            List<ProductInfo> productInfoList = productInfoService.list();
            
            log.info("查询到商品信息数量: {}", productInfoList.size());
            return Result.success("查询成功", productInfoList);

        } catch (Exception e) {
            log.error("查询商品信息列表异常", e);
            return Result.error("查询商品信息列表异常: " + e.getMessage());
        }
    }

    /**
     * 保存商品信息图片
     */
    private String saveProductInfoImage(MultipartFile file) {
        try {
            log.info("开始保存商品信息图片，原文件名: {}, 文件大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());

            // 验证文件
            if (file.isEmpty()) {
                throw new IOException("上传文件为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new IOException("只支持图片文件上传，当前文件类型: " + contentType);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = ".jpg"; // 默认扩展名
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = "product_info_" + UUID.randomUUID().toString() + extension;
            
            // 构建完整的文件路径 - 使用绝对路径
            File targetFile = new File(absoluteUploadPath, fileName);
            
            log.info("目标文件路径: {}", targetFile.getAbsolutePath());
            
            // 确保父目录存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                log.info("创建父目录: {}, 结果: {}", parentDir.getAbsolutePath(), created);
                
                if (!created && !parentDir.exists()) {
                    throw new IOException("无法创建上传目录: " + parentDir.getAbsolutePath());
                }
            }

            // 验证目录权限
            if (parentDir != null && (!parentDir.exists() || !parentDir.canWrite())) {
                throw new IOException("上传目录不可写: " + parentDir.getAbsolutePath());
            }
            
            // 保存文件
            file.transferTo(targetFile);
            
            // 验证文件是否保存成功
            if (!targetFile.exists() || targetFile.length() == 0) {
                throw new IOException("文件保存失败，目标文件不存在或大小为0");
            }
            
            // 构建用于Web访问的相对路径
            String webAccessPath = UPLOAD_PATH + fileName;
            
            log.info("图片保存成功 - 文件路径: {}, 文件大小: {} bytes, 访问路径: {}", 
                    targetFile.getAbsolutePath(), targetFile.length(), webAccessPath);
            
            // 返回相对路径用于Web访问
            return webAccessPath;
            
        } catch (IOException e) {
            log.error("保存图片失败: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("保存图片发生未知异常: {}", e.getMessage(), e);
            return null;
        }
    }

}
