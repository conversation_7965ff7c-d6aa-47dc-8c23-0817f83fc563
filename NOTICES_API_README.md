# 公告管理API接口文档

## 概述
本文档描述了公告管理系统的API接口，包括新增公告、查询公告列表、查询公告详情、更新公告和删除公告等功能。

## 基础信息
- 基础URL: `/api/v1/notices`
- 请求方式: RESTful API
- 数据格式: JSON
- 字符编码: UTF-8

## 接口列表

### 1. 新增公告
**接口地址:** `POST /api/v1/notices/add`

**请求参数:**
```json
{
  "title": "公告标题",
  "content": "公告内容",
  "publishTime": "2025-01-01T10:00:00" // 可选，默认为当前时间
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "公告新增成功",
  "data": {
    "id": 1,
    "title": "公告标题",
    "content": "公告内容",
    "createdAt": "2025-01-01T10:00:00",
    "publishTime": "2025-01-01T10:00:00"
  },
  "timestamp": 1704067200000
}
```

### 2. 查询公告列表
**接口地址:** `GET /api/v1/notices/list`

**请求参数:** 无

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "title": "公告标题1",
      "content": "公告内容1",
      "createdAt": "2025-01-01T10:00:00",
      "publishTime": "2025-01-01T10:00:00"
    },
    {
      "id": 2,
      "title": "公告标题2",
      "content": "公告内容2",
      "createdAt": "2025-01-01T11:00:00",
      "publishTime": "2025-01-01T11:00:00"
    }
  ],
  "timestamp": 1704067200000
}
```

### 3. 查询公告详情
**接口地址:** `GET /api/v1/notices/{id}`

**路径参数:**
- `id`: 公告ID (整数)

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "title": "公告标题",
    "content": "公告内容",
    "createdAt": "2025-01-01T10:00:00",
    "publishTime": "2025-01-01T10:00:00"
  },
  "timestamp": 1704067200000
}
```

### 4. 更新公告
**接口地址:** `PUT /api/v1/notices/{id}`

**路径参数:**
- `id`: 公告ID (整数)

**请求参数:**
```json
{
  "title": "更新后的公告标题",
  "content": "更新后的公告内容",
  "publishTime": "2025-01-01T12:00:00" // 可选
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "公告更新成功",
  "data": {
    "id": 1,
    "title": "更新后的公告标题",
    "content": "更新后的公告内容",
    "createdAt": "2025-01-01T10:00:00",
    "publishTime": "2025-01-01T12:00:00"
  },
  "timestamp": 1704067200000
}
```

### 5. 删除公告
**接口地址:** `DELETE /api/v1/notices/{id}`

**路径参数:**
- `id`: 公告ID (整数)

**响应示例:**
```json
{
  "code": 200,
  "message": "公告删除成功",
  "data": null,
  "timestamp": 1704067200000
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 系统异常 |

## 数据验证规则

### 新增和更新公告时的验证规则:
- `title`: 必填，长度1-200个字符
- `content`: 必填，长度1-5000个字符
- `publishTime`: 可选，如果不提供则默认为当前时间

## 使用示例

### 使用curl测试接口

1. **新增公告:**
```bash
curl -X POST http://localhost:8080/api/v1/notices/add \
  -H "Content-Type: application/json" \
  -d '{
    "title": "系统维护通知",
    "content": "系统将于今晚22:00-24:00进行维护，期间可能影响正常使用。"
  }'
```

2. **查询公告列表:**
```bash
curl -X GET http://localhost:8080/api/v1/notices/list
```

3. **查询公告详情:**
```bash
curl -X GET http://localhost:8080/api/v1/notices/1
```

4. **更新公告:**
```bash
curl -X PUT http://localhost:8080/api/v1/notices/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "系统维护通知（更新）",
    "content": "系统维护时间调整为今晚23:00-01:00。"
  }'
```

5. **删除公告:**
```bash
curl -X DELETE http://localhost:8080/api/v1/notices/1
```

## 注意事项

1. 所有接口都支持跨域访问
2. 时间格式使用ISO 8601标准 (yyyy-MM-ddTHH:mm:ss)
3. 列表查询按发布时间和创建时间倒序排列
4. 删除操作不可恢复，请谨慎操作
5. 所有接口都有完整的日志记录，便于问题排查 