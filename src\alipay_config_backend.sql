/*
 Navicat Premium Data Transfer

 Source Server         : 支付宝小程序
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : *************:3306
 Source Schema         : sft

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 06/08/2025 00:33:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for alipay_config_backend
-- ----------------------------
DROP TABLE IF EXISTS `alipay_config_backend`;
CREATE TABLE `alipay_config_backend`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `server_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝网关地址',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用ID',
  `private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '应用私钥',
  `alipay_public_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '支付宝公钥',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后端支付宝配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of alipay_config_backend
-- ----------------------------
INSERT INTO `alipay_config_backend` VALUES (1, 'https://openapi.alipay.com/gateway.do', '2021005171630618', 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9xxlousayisEsI5lzIcuXR/Mm1j+mmnw5DEf0QURvxtcC4MCUocKvZPYMF7SPn2zumXiHOV5XWhdiDcJpXg/KqgDkI4B5QMSxQw2YQiv/hU3nfeWB1kEl9zIjkaM+Y6PaU9P4nc9zPU2HFmQxmnG03JWWT6/yMuoXWEnIQzOovBx0DFMR10IRE14CGoB7n08kBCTZdbQearfIxgQan+6d0xaghqX0cqwGwLs+xadV0/bU2MFmhWCwDOBo3iZzfD+HtNkbIlcrXRdThIYTe2yPOMBKGYzTeLSSz7SsHDR/ZbhGsC+UBYhQlpp5ceG9MSsFS8Djzp9UsrH8mRLrTw0dAgMBAAECggEBAJgTRQxEp9cJE7UwLdYx3bizwmh7xXPXVfZNHhfgeVvtBf1bZ1dzrNPqXrTgBiQVSDCHMSuDUFNFcXl0tzbCVns78gzfWiNQQI4fwnQhiqpvAzefDlwV+gzXYwlXum3TYMki5bjTlRqupEK9MbsCHV4maYSTBNZb8HSWnkFpgKsT9dwUnaCXvCUF2GAxAMbrq3nCVJTBlkAWkokVIp7ZrRfuuJdVJvJQbhK4WyB0xzU1d1weFWjMBTkCKF685QiwAS+8tc2ua+5DqhlVECKJTqQIdtz8xlpjgVxYlgOt4zPlQml2UySX+cecPOhkcd1Ttk6GR2VwpAC5Hdo1iQbkRG0CgYEA/xa3KqK5SVW9idczGayLk4jq44tmHYcqYendIoEf+oHXX8Mzblxb7AttvaBM9w03yvBsg1/8E4LTguU6MqNXV59pPG6vHW6O+oO8w8yp1oOtJfykBqw3yB9prnrIpB+QTnLNuuneKvLhJY1w8f5MCk4qR5lsbKbqPKL8nKJDtQ8CgYEAvnSnxPixBTtBQEgMIIgwNBlrmGsvlc1/MgUH4B9naj9YN59Of0ciZLuvHKMZEKKhj1xbCYbLhyF6sqeaohyaLg8oAVUcTueU8jofwgtrXVjJtK/IIea5ZCIKoNpjYLYhq5on5YCpFghyZ2bJ3eQeoCky3eYoEOkfARvD3MWfkxMCgYEA8scF0ThKLHfECvqcEeVcejAnjiVQLO5F8iaIZpyKePs2ple8DV8txXwjGAUG+2LDJQyp2mdyHIe5Eq1kNGmtaSG5IOEfHV3qCpdzAs85HYb5Nlw7KHakKH1tMbh+WWYJDADLuQd4gVvj2L/DLS4f5Pb72mU1AjviV4+v6MuusRUCgYAsPruO9dtiBL8G6PXwRNM190XYteXrHj9TZnnK5mDG/QpIebjptqPV1Qg8VeOHytBPlrwLfYYg0XVItcklqhFja9/2V0hpSEGA+GHSWYbKu8q4MDIFNi2RhpsDfVii6gzuabSlC3u8g5/YR7fT1aALwyZvCH38V7v9LO8Whc6pgwKBgCrj6gsPoKI3FTxvN3bYqSj6DR7TVCrLpOeZSrO12lrqO2NF3r0dPm4fIk2JBS2MZQ2Iue3XKilyTBHxjcPW8uUl4RCyD6JIYj2L67AypKqoYYzQ0K9bqdkF/XkbFy3M5oq8aJDwDGiAvEDGPesHChFirakjcgY5Zpig6/YhM63o', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwW0Ug2JjoYNUhhfxdPuQlzy3YXgdQs9R3xMXQuhYfuMUMM6D39YbI9YxRDQzl3f6ZjPF44iHNWOSE5pYr/WHNQQslPxifMpX4dqz5lHiasm18o0yY0xHsZPMJr6yoBQN+5MMGDZx3XqIvGRl2vRcAy2ueUiqLCPhaW+R/g4/PlqCvZHPknYekGmuS6zKSdms3cTOdZC+exaVZI83/pOUYF5gpGkebzP4r3GTRmZPWSVhm33B3Z1oLn3nf7Ex/pVoqHRGsQk+P+FxDonWbk0fx3GEE5uOsuY3tfr8wqroVz2V5L6rnL7YqNqu/kbgVrKkkGBiVh/kN0iDZl9BMYHARQIDAQAB');

SET FOREIGN_KEY_CHECKS = 1;
