package com.kdsjkj.service.impl;

import com.kdsjkj.entity.Order;
import com.kdsjkj.mapper.OrderMapper;
import com.kdsjkj.service.IOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IOrderService {

    @Override
    public boolean updateTradeStatusByOutTradeNo(String outTradeNo, String tradeStatus) {
        return this.lambdaUpdate()
                .eq(Order::getOutTradeNo, outTradeNo)
                .set(Order::getTradeStatus, tradeStatus)
                .update();
    }

    @Override
    public boolean updateTradeStatusByTradeNo(String tradeNo, String tradeStatus) {
        return this.lambdaUpdate()
                .eq(Order::getTradeNo, tradeNo)
                .set(Order::getTradeStatus, tradeStatus)
                .update();
    }

    @Override
    public Order getByOutTradeNo(String outTradeNo) {
        return this.lambdaQuery()
                .eq(Order::getOutTradeNo, outTradeNo)
                .one();
    }

    @Override
    public Order getByTradeNo(String tradeNo) {
        return this.lambdaQuery()
                .eq(Order::getTradeNo, tradeNo)
                .one();
    }

    @Override
    public String getAppIdByTradeNo(String tradeNo) {
        Order order = this.lambdaQuery()
                .eq(Order::getTradeNo, tradeNo)
                .select(Order::getAppId)
                .one();
        return order != null ? order.getAppId() : null;
    }

    @Override
    public Map<String, Object> getTransactionStatisticsByAppIdAndTimeRange(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> result = new java.util.HashMap<>();
        
        if (appId == null || appId.trim().isEmpty()) {
            result.put("transactionCount", 0);
            result.put("totalAmount", java.math.BigDecimal.ZERO);
            return result;
        }
        
        // 查询交易笔数
        long transactionCount = this.lambdaQuery()
                .eq(Order::getAppId, appId)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS") // 只统计支付成功的订单
                .ge(Order::getCreateTime, startTime)
                .le(Order::getCreateTime, endTime)
                .count();
        
        // 查询交易总金额
        List<Order> orders = this.lambdaQuery()
                .eq(Order::getAppId, appId)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS") // 只统计支付成功的订单
                .ge(Order::getCreateTime, startTime)
                .le(Order::getCreateTime, endTime)
                .select(Order::getAmount)
                .list();
        
        java.math.BigDecimal totalAmount = java.math.BigDecimal.ZERO;
        for (Order order : orders) {
            if (order.getAmount() != null) {
                totalAmount = totalAmount.add(order.getAmount());
            }
        }
        
        result.put("transactionCount", (int) transactionCount);
        result.put("totalAmount", totalAmount);
        
        return result;
    }

    @Override
    public Map<String, Object> getTransactionStatisticsByAppIds(List<String> appIds) {
        Map<String, Object> result = new java.util.HashMap<>();
        
        if (appIds == null || appIds.isEmpty()) {
            result.put("transactionCount", 0);
            result.put("totalAmount", java.math.BigDecimal.ZERO);
            return result;
        }
        
        // 查询交易笔数
        long transactionCount = this.lambdaQuery()
                .in(Order::getAppId, appIds)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS") // 只统计支付成功的订单
                .count();
        
        // 查询交易总金额
        List<Order> orders = this.lambdaQuery()
                .in(Order::getAppId, appIds)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS") // 只统计支付成功的订单
                .select(Order::getAmount)
                .list();
        
        java.math.BigDecimal totalAmount = java.math.BigDecimal.ZERO;
        for (Order order : orders) {
            if (order.getAmount() != null) {
                totalAmount = totalAmount.add(order.getAmount());
            }
        }
        
        result.put("transactionCount", (int) transactionCount);
        result.put("totalAmount", totalAmount);
        
        return result;
    }
    
    @Override
    public Map<String, Object> getTodayTransactionStatisticsByAppIds(List<String> appIds) {
        Map<String, Object> result = new java.util.HashMap<>();
        
        if (appIds == null || appIds.isEmpty()) {
            result.put("transactionCount", 0);
            result.put("totalAmount", java.math.BigDecimal.ZERO);
            return result;
        }
        
        // 获取今日的开始和结束时间
        java.time.LocalDate today = java.time.LocalDate.now();
        LocalDateTime startTime = today.atStartOfDay();
        LocalDateTime endTime = today.atTime(23, 59, 59);
        
        // 查询今日交易笔数
        long transactionCount = this.lambdaQuery()
                .in(Order::getAppId, appIds)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS") // 只统计支付成功的订单
                .ge(Order::getCreateTime, startTime)
                .le(Order::getCreateTime, endTime)
                .count();
        
        // 查询今日交易总金额
        List<Order> orders = this.lambdaQuery()
                .in(Order::getAppId, appIds)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS") // 只统计支付成功的订单
                .ge(Order::getCreateTime, startTime)
                .le(Order::getCreateTime, endTime)
                .select(Order::getAmount)
                .list();
        
        java.math.BigDecimal totalAmount = java.math.BigDecimal.ZERO;
        for (Order order : orders) {
            if (order.getAmount() != null) {
                totalAmount = totalAmount.add(order.getAmount());
            }
        }
        
        result.put("transactionCount", (int) transactionCount);
        result.put("totalAmount", totalAmount);
        
        return result;
    }

}
