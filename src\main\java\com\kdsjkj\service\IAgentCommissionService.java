package com.kdsjkj.service;

import java.math.BigDecimal;

/**
 * <p>
 * 代理分润计算服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IAgentCommissionService {

    /**
     * 计算并保存代理分润
     * 
     * @param tradeNo 支付宝交易号
     * @param totalAmount 交易总金额
     * @return 处理是否成功
     */
    boolean calculateAndSaveAgentCommission(String tradeNo, String totalAmount);

    /**
     * 计算代理分润金额
     * 
     * @param lowerRate 下级代理费率
     * @param upperRate 上级代理费率
     * @param totalAmount 交易总金额
     * @return 分润金额
     */
    BigDecimal calculateCommissionAmount(BigDecimal lowerRate, BigDecimal upperRate, BigDecimal totalAmount);

    /**
     * 计算并保存最底层代理分润
     * 
     * @param tradeNo 支付宝交易号
     * @param totalAmount 交易总金额
     * @return 处理是否成功
     */
    boolean calculateAndSaveBottomAgentCommission(String tradeNo, String totalAmount);

} 