package com.kdsjkj.appTest.转账.沙箱;

import com.alipay.api.AlipayConfig;

/**
 * 支付宝沙箱环境配置类
 * 用于转账相关接口的统一配置管理
 */
public class AlipaySandboxConfig {
    
    // 沙箱环境应用私钥
    private static final String PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDEaoU0mfwT+3rFWW9wY50UW5ULfS6YPTjvS3pBCcMy8rkYihDlc1ctW2sfxxYy+VX2bY4FrQcwD6G6b3aX6b43WrSh9pheQZX/Yr0aay8qnVC+ECzfSFws4Cgs/1Qf+Kiq3WCKBHchXz3f7w057xfTxPy92/R7rN6vkERZ/zX4afbz/CDuft+5ypoq3E98xcPBZJQe0i4QiSe72LKWkXNO8MrCthXvM2sBswN01WUj9uVwsWCuY5FpRq++oHmhrr3W1Epx1H6JMtlEj73nltDdV8APmlzjM4nq/MtW2E6TU3aUjxlURhLm5np4TJr2A3x2M77ascdLOmS+Hua4SvxBAgMBAAECggEAZ/hGyKBWGjG9PCfqYGCqbnNIAwvhlT6bG7glX7YOOLLZHc8/wGV8mQrSOsEE1Di9szMswX5k1qD/qBOGgEV/pgeCpFVHNGWdQ9F9bjWcgv9BFUq0pgDEohCJ07/YRRA4IDIM/yH7+ZNTymzc4HTadQPKEaoT0T+hpOdF9ymr5PEr91DCoRDIoRbdkJDO0vqXWW5XbJENMy5XvIOwYjlrZOvZJBxSuUsE5OacXJ15mMs7btX1CKmlaVZObOwOXavJYsp7RY4YulwcxMt4CoITizPD01kPOSt4e8bEwLrgMMNWKV43TPpM5d3KXCAfhCxd7cFVJ9xVKiy0qsnWocDWfQKBgQD3ZrUCoA5tC0EPb5pMVkYwyvxbvXRUfYL+xVNAA4l29jqm126hPrxQhoCIopnobktMOjb+H2h3qV3DQAD9u8EnSeav+iCZ7QH4yyiIv0FhN0nISmU8N2fJl6qRWGcKmQ3C5m+wjDh0g9pme9Wu8gL2vSrJjPx7a2LKmDsVyFypAwKBgQDLPio2qs36KWp95yA4EAC0UyWR+2xu8nc2rM4Rv9kT1S+ewAR3bg9CNnQ6hcSSx5ETOzSVeNhMck1rMMJOLz/AE+RA0Ia4rU4VbgPSlzGyd6aAJhuVS0KonvOADPQB8RS7hPFR3evhfofOsaQQiMYRsAmbtzzmlJwcPIa6loLIawKBgQDHuw5VYTxkQlqzCImMKXafO7ECq2/4KiAQaOPt4S0gcmclfPUejlRfukaYiJUydaCqmLD+9gJT4NMGp2xlbCfi/SGVXpzlR9d4sSM3SxiQUJ9raMxUEfLjaGNW1gTSJIBcocb3xCxrvGsco8r+YvmLk2qwrvbC+Q4Xu1CwJELSWwKBgFIbbxpero/do3Pxp4Z+kIpI/OiY1hmMQrUH4sgw5RWqDm7ny+YjxG5PaW0rFVrm3KkURg4Mn/ViR/yB2j2TwQFsB85Xb9WzzdPCejDde+pJhC5JNuN9YBSXAnA8y2btah7HemZEbULPxzWtdkZnuvzyTS9nO2c7qkWN/i/rFjK5AoGBAKYA+u/66REB2bxdG91vWPyPe00tWLW26hhJTYOokyPDnzp5DLoobeQOnNoEMHST4yIWlqhc/G8ljRIjCO/l3I6LJl8b8n+zNOUHtJEMLFBZIQE/JuhHihGsvPUkGqIPW55qO8l2t2Wrf2d6zULGIF+hhBkdYcx6xtoNE/Zo633N";
    
    // 支付宝沙箱环境公钥
    private static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgBVd8GyGIH/AM3F54FkPP94CKll/XDaoI0nuJldep3GBhclNSXZ/ulbmKfhOJdU9M4bU+rMyEQVqNIn0JsUdtA9s9hPQgUmJPndqYWv7mAnb2mxPQuxyHsrXx4PzxVjoPC4C0oAvmSyB1pL8XjPpETErbYf3H+Q5/8GtCT4QouN8aBKmlRQDfbf+6rgJ4w6SU8xGaLOm/flR2HG2SdgtaunIrAzyVO9/UUY5hrIFVZHH2XR3kE8B0QOvqg+oDd70fFA40DZ5msmP4EK3swrNjSJRWUWQWqUJ5sriTA0dgLVst86HVGH6U1XDAf35GgQYxqUwhISemyTaU/WNDoNYdwIDAQAB";
    
    // 沙箱环境网关地址
    private static final String SERVER_URL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    
    // 沙箱环境应用ID
    private static final String APP_ID = "2021005171630618";
    
    /**
     * 获取支付宝沙箱环境配置
     * @return AlipayConfig 配置对象
     */
    public static AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(SERVER_URL);
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(PRIVATE_KEY);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
} 