<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.MiniprogramTishengMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.MiniprogramTisheng">
        <id column="id" property="id" />
        <result column="app_version" property="appVersion" />
        <result column="app_name" property="appName" />
        <result column="version_desc" property="versionDesc" />
        <result column="service_phone" property="servicePhone" />
        <result column="mini_category_ids" property="miniCategoryIds" />
        <result column="app_slogan" property="appSlogan" />
        <result column="app_desc" property="appDesc" />
        <result column="logo" property="logo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_version, app_name, version_desc, service_phone, mini_category_ids, app_slogan, app_desc, logo
    </sql>

</mapper>
