@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 配置参数
set SERVER_HOST=your_server_ip
set SERVER_USER=root
set SERVER_PORT=22
set REMOTE_PATH=/opt/app/
set JAR_NAME=backend.jar
set LOCAL_JAR_PATH=target\%JAR_NAME%

echo 🚀 开始部署 %JAR_NAME% 到服务器...

:: 检查本地JAR文件是否存在
if not exist "%LOCAL_JAR_PATH%" (
    echo ❌ 错误: 找不到JAR文件 %LOCAL_JAR_PATH%
    echo 💡 请先执行: mvn clean package
    pause
    exit /b 1
)

echo ✓ 找到本地JAR文件: %LOCAL_JAR_PATH%

:: 使用PSCP上传文件（需要安装PuTTY）
echo 📤 上传JAR文件...
pscp -P %SERVER_PORT% "%LOCAL_JAR_PATH%" %SERVER_USER%@%SERVER_HOST%:%REMOTE_PATH%

if %errorlevel% equ 0 (
    echo ✓ JAR文件上传成功
) else (
    echo ❌ JAR文件上传失败
    pause
    exit /b 1
)

:: 使用PLINK执行远程命令（重启服务）
echo ▶️ 重启服务...
plink -P %SERVER_PORT% %SERVER_USER%@%SERVER_HOST% "cd %REMOTE_PATH% && pkill -f %JAR_NAME% || true && sleep 3 && nohup java -jar %JAR_NAME% > app.log 2>&1 &"

echo 🎉 部署完成！
pause 