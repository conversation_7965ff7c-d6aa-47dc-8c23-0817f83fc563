package com.kdsjkj.appTest.分账.分账请求;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeOrderSettleModel;
import com.alipay.api.response.AlipayTradeOrderSettleResponse;
import com.alipay.api.request.AlipayTradeOrderSettleRequest;
import com.alipay.api.domain.OpenApiRoyaltyDetailInfoPojo;
import com.kdsjkj.config.AlipayMiniProgramConfig;

import java.util.ArrayList;
import java.util.List;

public class AlipayTradeOrderSettle {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeOrderSettleRequest request = new AlipayTradeOrderSettleRequest();
        AlipayTradeOrderSettleModel model = new AlipayTradeOrderSettleModel();
        
        // 设置结算请求流水号
        model.setOutRequestNo("20160727001");
        
        // 设置支付宝订单号
        model.setTradeNo("2025070322001456831412525726");
        
        // 设置分账明细信息
        List<OpenApiRoyaltyDetailInfoPojo> royaltyParameters = new ArrayList<OpenApiRoyaltyDetailInfoPojo>();
        OpenApiRoyaltyDetailInfoPojo royaltyParameters0 = new OpenApiRoyaltyDetailInfoPojo();
        royaltyParameters0.setRoyaltyType("transfer");
        royaltyParameters0.setTransInType("userId");
        royaltyParameters0.setTransIn("2088622225329120");
        royaltyParameters0.setAmount("0.01");
        royaltyParameters0.setDesc("分账给测试");
        royaltyParameters.add(royaltyParameters0);
        model.setRoyaltyParameters(royaltyParameters);
        
        request.setBizModel(model);
        
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayTradeOrderSettleResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}