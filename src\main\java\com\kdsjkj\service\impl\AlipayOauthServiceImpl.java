package com.kdsjkj.service.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.domain.AlipayOpenAuthTokenAppQueryModel;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.kdsjkj.service.AlipayOauthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AlipayOauthServiceImpl implements AlipayOauthService {

    private final AlipayClient alipayClient;

    public AlipayOauthServiceImpl() throws AlipayApiException {
        this.alipayClient = new DefaultAlipayClient(getAlipayConfig());
    }

    @Override
    public AlipaySystemOauthTokenResponse getAccessToken(String authCode) throws AlipayApiException {
        // 构造请求参数
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);
        request.setGrantType("authorization_code");
        AlipayOpenAuthTokenAppQueryModel model = new AlipayOpenAuthTokenAppQueryModel();
        // 设置应用授权令牌
        model.setAppAuthToken("202506BBede8989bb392416e878ce01c9de4aX81");

        request.setBizModel(model);
        
        // 调用SDK获取访问令牌
        AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
        log.info("获取访问令牌响应: {}", response.getBody());

        if (response.isSuccess()) {
            log.info("获取访问令牌成功，用户id: {}", response.getUserId());
            return response;
        } else {
            log.error("获取访问令牌失败: {}, {}", response.getSubCode(), response.getSubMsg());
            throw new AlipayApiException("获取访问令牌失败: " + response.getSubMsg());
        }
    }

    private static AlipayConfig getAlipayConfig() {
        // 商户小程序的私钥
        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC22iPz+K4hYmN7JlrgLz7v9jHgMVNbtpIAcNO2iuFEQU/cw8Uovl/6ZWNT0FRrlBYNfG7RyRSWeIDmjRhq7PRSp4aGhTUNVQXzGrP8B4ZkKIxcv3NCdM4jbTTibUSNZSOxQpCpObH1YkPloVsyf1rwR/iKytO77aSzRunvkbEf8g1qMygdUQr1jmcNYq+FtKvQihxKgb4/6aR+/V2KcItYplUerc2LRPARLjMryUtMQ0sbIiGd9n+TgAyOjDDWeLTDZCuD0ULOL61lyW8Pb8sgb8EsxXyBilxmEqrw2b+0k/P5baETa3lBKwsYS08hGKxfhYafBCMBKfDtwLxVOlYtAgMBAAECggEAJTBOmaMdPzr0gqp1Vx0kzscis7c0ffWu0hogZQLv9+LUrFDAu52khZezUOuRD+QpbAq/uzkLT+MoTDusGKsiseDSkm9rL8/4Cs6Ghp69LYe1rHNfrMd22WuvktGoXJ2SWc3xAAn08LRQnNZpZubSBwaMPSYtVA5sLe4hTBE0lgtbeiiFLquqNS9exwxVnUgEV+oNucfuwrGyM5NVR7iJs6ToHtdIcC0WcdXg9gbpnk2FiCOfmPUMY6Qp9F2Q7ZT2EI2MuxGvRFhRrO4z9/ogE4vzUxx6NdFs9NsZQiO2erh10eh7HLa+LKP8F5pZk9WOMgQ0BtABGg3zb7Rf845PYQKBgQDp+5urn3NQXQTrnzYCuJsnMHtGHbL5ajpolthftkOglJ0CWI4Xbjfs3XBJOqLcWU2pyGJZ84em5mGDCfZx4q8KUiGtnfPKa5abM8PUi50oIRdkQfVOgZf2/tHGHyRbKG8qKZZIN2yQussGbgS/dVTN5WH9RQKnoICYBhbxv3Uk1QKBgQDIDtlC2Z4GLN/831Xl6KEaJasMQ1Lvk2UefzD6ns4HtMF6MKJKDONmXYwxWkSwpkQq7FuRG98yPayOTy58zZudsuUWHVz5WsAi3JNd5YXtCzbP52EL04K3VO3/W41kuy5SzYzE1Ua95h15Ri70CgvG0r+utRmz4ZeNiWIonsD3+QKBgQCXt6U+CHseLeKl4auE4i0AoUgds948KaxL6xvMHAQH2LViMweXm8k5lnBbm2DE3kckgyF60BUynIm4DTq2p9T2LUFXMWdF5rNWWZ9ub0TtI1/mi3pDeHaQGrTs8X6CeVpzjVm74+xKgERrG+WKO8tMYqNCNYA+Ly2S5cf04z9TLQKBgGlGXw/tolmPjcLs56/XbcdroQoP93m475JPzj9kB/lIJjQdmZksFRIOEjL6VIE8zUxlGn5O03IFwGMWydjE/anLtP+hLgbNuaPevPHBUWt+5jYbhRcLE5NT7vXcgJhMY8ERB42gydwuVr41r4meK7pRtqrCBCeDhBPoAq5rdBJJAoGAB2HYFQqZ/AIRmdcCYQ3XboAN0txJ/NHKcmx77Hmf5LNJ50ELX8v7aEUXzHxRhk/5B75G8yRoxhd/uOi3aOs6LhPmRSANBSVZPSimlqJMmaLsyBhiQatJlOHMjAjSK3oHSvoAE0kgmAWVl5QQON3F0NN/qe3faPIx725Em6RZXcs=";
        // 支付宝公钥
        String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwW0Ug2JjoYNUhhfxdPuQlzy3YXgdQs9R3xMXQuhYfuMUMM6D39YbI9YxRDQzl3f6ZjPF44iHNWOSE5pYr/WHNQQslPxifMpX4dqz5lHiasm18o0yY0xHsZPMJr6yoBQN+5MMGDZx3XqIvGRl2vRcAy2ueUiqLCPhaW+R/g4/PlqCvZHPknYekGmuS6zKSdms3cTOdZC+exaVZI83/pOUYF5gpGkebzP4r3GTRmZPWSVhm33B3Z1oLn3nf7Ex/pVoqHRGsQk+P+FxDonWbk0fx3GEE5uOsuY3tfr8wqroVz2V5L6rnL7YqNqu/kbgVrKkkGBiVh/kN0iDZl9BMYHARQIDAQAB";

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        // 使用商户小程序的appId
        alipayConfig.setAppId("2021005168630224");
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
} 