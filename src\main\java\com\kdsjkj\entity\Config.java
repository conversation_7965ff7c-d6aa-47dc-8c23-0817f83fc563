package com.kdsjkj.entity;

import com.alipay.api.AlipayConfig;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-28 16:42
 */
public class Config {
    public static AlipayConfig getAlipayConfig() {
        String privateKey  = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCnIh9v6T36I3n1fILkNdAQmGUJOky7tBOFDiQsctKRIu2aQeLNNnNhuTzdbFnnPUucYhTzMwevKWemxCytgzJ6/yHa8gjRclIo7VJ9i8JySdU0BWfiIUO57mR3VyVpEnoc1pWe5/cu3Im8QJK1vMxT9iwsDSI8aKrftYCiOeq9722+HWz5WVElakLYKZoXuT8l8mqPMWsfWTyQ80xBNZb9MUtZqpHOVZtWKDseS7OrMKoxUlJIxHPoLNOQsbeIF5DtoIIeJVG0Q5cMidp1rXrVw8cR5Um/qyoYb+blSOW+ZOnQUA3si8V6WPJdK8tiQari3qyMf9hBVT3Kuomjsia/AgMBAAECggEAdaq0b1nK5JytdIOgs4KfZc+k8oY1F8GELJz0IscEMGJi3cOdJdU8DoUXsNxjv0WMQiFblo1Kz6ta6jIbMl234JhXAYp9YtgVt/K9rN4Em+oXTHSx2qaIxBJucQbNs9fqWPtKg4HUqQ1je0ILCCBPypz6JFlqzRTzmGR0l8Im3mja8IhtWaWmidlHVbBl8ihBxRuz0C7RK5GKzQtU4THpd/SRuo6YJBfwjVHdvFz0EA/A1SR+gv2GM5hqnuUGM3i6jDJeHojXK/8iAnh21uJqgzlt6u2c17Jowgm9sE5hWOXsO14nXDkPi0AlESNxF2ZDMVBPUYKYzOkeI7VokDYxIQKBgQDdXzcPeH+CY+HL4JW0DCv3N+PqwPpp3f9Ik5Ch/f4trwG37YF2mRPvH8ygNwxcAiVHmB0VwlDABCqAUlK/a/LTj7ZNHkIEbk7WoTQK7+zy5qUwvM9+pH3G3Ooy53B7onPL+rE2HJ00dk7Uh3SxrZ5tMIIsrckHjsKgtSMc9fCsOwKBgQDBRu9BG0cKyq1/sd623sDnLn91SVnc9LmX2deRoANxJsgI39pCuCysmtX2TiSY29Ll7v//Hn+MHS/PJx1FVwdBeoRZWOOPBwZzZkiZLLSa/TC8K4UHELTJXRvuZJ9NSLW4jyS4X5CUO2nhBJteWyK0f/w4qefT1FzM0U4eU8p7TQKBgQCbRX3JTCujuYtXBV/7uc3cyUcEEfeS6jN1JIv+E7mFMYtzBxQXq6dfwl40wmfY4Whknz1HVdRt2n41I4kG2yCnY9XqoZ6MnRKPWOK4CTGZ7k1Psh+YJKhUI//hVJSBI6E1IdHKsqdRdOo2CAzB05Wb4LptvDeVJX0+W88VMSxuoQKBgQCmnfEJbmt6+ShCieLWQQeJBEOqp3ZJjcslBg3ZI1l0RRty0w+ELuOMVNqYv9q6z95qi/OySedosYySh0Srjw/ZObY4k/FwTn0YJJf3PFvhtN07ikTex18irBq1E5dk94Z4uSXNr8yijOmEqwUJfYVYl3CmpIoOyebKRrGSYWePjQKBgQDJ/QhtNS7GdHdao3BJk+Zuv+aVUgsrKztDS184pVrEL7j7Z3tCeggaFbyh+OxKR/Dq7ZFHR/1kYCEGlgg58JrP4WnkFzxiktBj8i0WCtiDtH7kIh0h1DAvjlc4mtjDoRxruJYJzzgIFP+Uex6NpY5ATVUiFCcMHenAxJrnl6CYyQ==";
        String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwW0Ug2JjoYNUhhfxdPuQlzy3YXgdQs9R3xMXQuhYfuMUMM6D39YbI9YxRDQzl3f6ZjPF44iHNWOSE5pYr/WHNQQslPxifMpX4dqz5lHiasm18o0yY0xHsZPMJr6yoBQN+5MMGDZx3XqIvGRl2vRcAy2ueUiqLCPhaW+R/g4/PlqCvZHPknYekGmuS6zKSdms3cTOdZC+exaVZI83/pOUYF5gpGkebzP4r3GTRmZPWSVhm33B3Z1oLn3nf7Ex/pVoqHRGsQk+P+FxDonWbk0fx3GEE5uOsuY3tfr8wqroVz2V5L6rnL7YqNqu/kbgVrKkkGBiVh/kN0iDZl9BMYHARQIDAQAB";
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId("2021005163695273");
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
}
