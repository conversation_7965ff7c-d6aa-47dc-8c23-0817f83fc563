package com.kdsjkj.appTest.分账.交易分账查询接;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeOrderSettleQueryModel;
import com.alipay.api.request.AlipayTradeOrderSettleQueryRequest;
import com.alipay.api.response.AlipayTradeOrderSettleQueryResponse;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayTradeOrderSettleQuery {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeOrderSettleQueryRequest request = new AlipayTradeOrderSettleQueryRequest();
        AlipayTradeOrderSettleQueryModel model = new AlipayTradeOrderSettleQueryModel();
        
        // 设置支付宝分账请求单号
        model.setSettleNo("20250703002530020083560341371649");
        
        // 设置调用分账接口时指定的外部请求号
        model.setOutRequestNo("20160727001");
        
        // 设置支付宝交易号
        model.setTradeNo("2025070322001456831412525726");
        
        request.setBizModel(model);
        
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayTradeOrderSettleQueryResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}