package com.kdsjkj.controller;

import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.service.IAgentService;
import com.kdsjkj.service.IMiniprogramVersionService;
import com.kdsjkj.entity.Agent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代理数据统计控制器
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/api/agent-data")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class AgentDataController {
    
    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;
    
    @Autowired
    private IOrderService orderService;
    
    @Autowired
    private IAgentService agentService;
    
    @Autowired
    private IMiniprogramVersionService miniprogramVersionService;
    
    /**
     * 时间范围枚举
     */
    public enum TimeRange {
        YESTERDAY("昨日"),
        THIS_WEEK("这周"),
        THIS_MONTH("这月");
        
        private final String description;
        
        TimeRange(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 统计类型枚举
     */
    public enum StatisticsType {
        DIRECT("直属"),
        TEAM("团队");
        
        private final String description;
        
        StatisticsType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取代理数据统计
     * 
     * @param agentUid 代理UID
     * @param timeRange 时间范围：YESTERDAY(昨日)、THIS_WEEK(这周)、THIS_MONTH(这月)
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getAgentStatistics(
            @RequestParam(required = true) String agentUid,
            @RequestParam(required = true) TimeRange timeRange) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取代理统计数据 - 代理UID: {}, 时间范围: {}", agentUid, timeRange.getDescription());
            
            // 1. 验证代理是否存在
            Agent agent = agentService.getAgentByUid(agentUid);
            if (agent == null) {
                response.put("success", false);
                response.put("message", "代理不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 2. 获取时间范围
            LocalDateTime startTime = getStartTime(timeRange);
            LocalDateTime endTime = getEndTime(timeRange);
            
            log.info("时间范围: {} 到 {}", startTime, endTime);
            
            // 3. 获取该代理下的所有商户appid（在指定时间范围内的）
            List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUidAndTimeRange(
                    agentUid, startTime, endTime);
            
            if (merchantAppIds.isEmpty()) {
                // 没有商户数据，返回空结果
                response.put("success", true);
                response.put("data", createEmptyStatistics());
                response.put("message", "该时间段内无商户数据");
                return ResponseEntity.ok(response);
            }
            
            log.info("找到商户数量: {}", merchantAppIds.size());
            
            // 4. 统计交易数据
            Map<String, Object> statistics = calculateStatistics(merchantAppIds, startTime, endTime);
            
            // 5. 添加商户数量
            statistics.put("merchantCount", merchantAppIds.size());
            statistics.put("timeRange", timeRange.getDescription());
            
            // 创建代理信息Map（JDK 1.8兼容）
            Map<String, Object> agentInfo = new HashMap<>();
            agentInfo.put("uid", agent.getUid());
            agentInfo.put("name", agent.getName());
            agentInfo.put("phone", agent.getPhone());
            statistics.put("agentInfo", agentInfo);
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "统计数据获取成功");
            
            log.info("统计数据获取成功 - 商户数: {}, 交易笔数: {}, 交易金额: {}", 
                    statistics.get("merchantCount"), 
                    statistics.get("transactionCount"), 
                    statistics.get("totalAmount"));
            
        } catch (Exception e) {
            log.error("获取代理统计数据异常 - 代理UID: {}, 时间范围: {}", agentUid, timeRange, e);
            response.put("success", false);
            response.put("message", "获取统计数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取时间范围的开始时间
     */
    private LocalDateTime getStartTime(TimeRange timeRange) {
        LocalDate today = LocalDate.now();
        
        switch (timeRange) {
            case YESTERDAY:
                return today.minusDays(1).atStartOfDay();
            case THIS_WEEK:
                return today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay();
            case THIS_MONTH:
                return today.withDayOfMonth(1).atStartOfDay();
            default:
                throw new IllegalArgumentException("不支持的时间范围: " + timeRange);
        }
    }
    
    /**
     * 获取时间范围的结束时间
     */
    private LocalDateTime getEndTime(TimeRange timeRange) {
        LocalDate today = LocalDate.now();
        
        switch (timeRange) {
            case YESTERDAY:
                return today.minusDays(1).atTime(23, 59, 59);
            case THIS_WEEK:
                return today.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY)).atTime(23, 59, 59);
            case THIS_MONTH:
                return today.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);
            default:
                throw new IllegalArgumentException("不支持的时间范围: " + timeRange);
        }
    }
    
    /**
     * 创建空统计数据
     */
    private Map<String, Object> createEmptyStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("merchantCount", 0);
        statistics.put("transactionCount", 0);
        statistics.put("totalAmount", BigDecimal.ZERO);
        statistics.put("timeRange", "");
        statistics.put("agentInfo", new HashMap<>());
        return statistics;
    }
    
    /**
     * 计算统计数据
     */
    private Map<String, Object> calculateStatistics(List<String> merchantAppIds, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        int totalTransactionCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (String appId : merchantAppIds) {
            // 获取该商户的交易统计
            Map<String, Object> merchantStats = orderService.getTransactionStatisticsByAppIdAndTimeRange(
                    appId, startTime, endTime);
            
            int transactionCount = (Integer) merchantStats.get("transactionCount");
            BigDecimal amount = (BigDecimal) merchantStats.get("totalAmount");
            
            totalTransactionCount += transactionCount;
            totalAmount = totalAmount.add(amount);
            
            log.debug("商户 {} 统计 - 交易笔数: {}, 交易金额: {}", appId, transactionCount, amount);
        }
        
        statistics.put("transactionCount", totalTransactionCount);
        statistics.put("totalAmount", totalAmount);
        
        return statistics;
    }
    
    /**
     * 获取代理数据统计（开通商户、有效商户、笔均交易额、日均交易额）
     * 
     * @param agentUid 代理UID
     * @param statisticsType 统计类型：DIRECT(直属)、TEAM(团队)
     * @return 统计数据
     */
    @GetMapping("/data-statistics")
    public ResponseEntity<Map<String, Object>> getDataStatistics(
            @RequestParam(required = true) String agentUid,
            @RequestParam(required = true) StatisticsType statisticsType) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取代理数据统计 - 代理UID: {}, 统计类型: {}", agentUid, statisticsType.getDescription());
            
            // 1. 验证代理是否存在
            Agent agent = agentService.getAgentByUid(agentUid);
            if (agent == null) {
                response.put("success", false);
                response.put("message", "代理不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, Object> statistics;
            
            if (statisticsType == StatisticsType.DIRECT) {
                // 2. 直属统计
                statistics = calculateDirectStatistics(agentUid);
            } else {
                // 3. 团队统计
                statistics = calculateTeamStatistics(agentUid);
            }
            
            // 4. 添加代理信息
            Map<String, Object> agentInfo = new HashMap<>();
            agentInfo.put("uid", agent.getUid());
            agentInfo.put("name", agent.getName());
            agentInfo.put("phone", agent.getPhone());
            statistics.put("agentInfo", agentInfo);
            statistics.put("statisticsType", statisticsType.getDescription());
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "数据统计获取成功");
            
            log.info("数据统计获取成功 - 开通商户: {}, 有效商户: {}, 笔均交易额: {}, 日均交易额: {}", 
                    statistics.get("merchantCount"), 
                    statistics.get("validMerchantCount"),
                    statistics.get("averageTransactionAmount"),
                    statistics.get("dailyAverageAmount"));
            
        } catch (Exception e) {
            log.error("获取代理数据统计异常 - 代理UID: {}, 统计类型: {}", agentUid, statisticsType, e);
            response.put("success", false);
            response.put("message", "获取数据统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 计算直属统计数据
     */
    private Map<String, Object> calculateDirectStatistics(String agentUid) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 1. 获取开通商户个数
        int merchantCount = serviceMarketOrderService.getMerchantCountByAgentUid(agentUid);
        statistics.put("merchantCount", merchantCount);
        
        // 2. 获取商户appid列表
        List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(agentUid);
        
        // 3. 获取有效商户个数（状态为"已上架"）
        int validMerchantCount = miniprogramVersionService.getValidMerchantCountByAppIdsAndStatus(merchantAppIds, "已上架");
        statistics.put("validMerchantCount", validMerchantCount);
        
        // 4. 获取交易统计
        Map<String, Object> transactionStats = orderService.getTransactionStatisticsByAppIds(merchantAppIds);
        int transactionCount = (Integer) transactionStats.get("transactionCount");
        BigDecimal totalAmount = (BigDecimal) transactionStats.get("totalAmount");
        
        // 5. 计算笔均交易额
        BigDecimal averageTransactionAmount = BigDecimal.ZERO;
        if (transactionCount > 0) {
            averageTransactionAmount = totalAmount.divide(new BigDecimal(transactionCount), 2, java.math.RoundingMode.DOWN);
        }
        statistics.put("averageTransactionAmount", averageTransactionAmount);
        
        // 6. 获取今日交易统计
        Map<String, Object> todayStats = orderService.getTodayTransactionStatisticsByAppIds(merchantAppIds);
        int todayTransactionCount = (Integer) todayStats.get("transactionCount");
        BigDecimal todayTotalAmount = (BigDecimal) todayStats.get("totalAmount");
        
        // 7. 计算日均交易额（今日平均）
        BigDecimal dailyAverageAmount = BigDecimal.ZERO;
        if (todayTransactionCount > 0) {
            dailyAverageAmount = todayTotalAmount.divide(new BigDecimal(todayTransactionCount), 2, java.math.RoundingMode.DOWN);
        }
        statistics.put("dailyAverageAmount", dailyAverageAmount);
        
        return statistics;
    }
    
    /**
     * 计算团队统计数据
     */
    private Map<String, Object> calculateTeamStatistics(String agentUid) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 1. 获取所有下级代理UID列表
        List<String> subAgentUids = agentService.getSubAgentUidsByAgentUid(agentUid);
        subAgentUids.add(agentUid);
        
        if (subAgentUids.isEmpty()) {
            // 没有下级代理，返回空统计
            statistics.put("merchantCount", 0);
            statistics.put("validMerchantCount", 0);
            statistics.put("averageTransactionAmount", BigDecimal.ZERO);
            statistics.put("dailyAverageAmount", BigDecimal.ZERO);
            return statistics;
        }
        
        // 2. 统计所有下级代理的数据
        int totalMerchantCount = 0;
        int totalValidMerchantCount = 0;
        int totalTransactionCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        int totalTodayTransactionCount = 0;
        BigDecimal totalTodayAmount = BigDecimal.ZERO;
        
        for (String subAgentUid : subAgentUids) {
            // 获取下级代理的开通商户个数
            int merchantCount = serviceMarketOrderService.getMerchantCountByAgentUid(subAgentUid);
            totalMerchantCount += merchantCount;
            
            // 获取下级代理的商户appid列表
            List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(subAgentUid);
            
            // 获取下级代理的有效商户个数
            int validMerchantCount = miniprogramVersionService.getValidMerchantCountByAppIdsAndStatus(merchantAppIds, "已上架");
            totalValidMerchantCount += validMerchantCount;
            
            // 获取下级代理的交易统计
            Map<String, Object> transactionStats = orderService.getTransactionStatisticsByAppIds(merchantAppIds);
            int transactionCount = (Integer) transactionStats.get("transactionCount");
            BigDecimal amount = (BigDecimal) transactionStats.get("totalAmount");
            totalTransactionCount += transactionCount;
            totalAmount = totalAmount.add(amount);
            
            // 获取下级代理的今日交易统计
            Map<String, Object> todayStats = orderService.getTodayTransactionStatisticsByAppIds(merchantAppIds);
            int todayTransactionCount = (Integer) todayStats.get("transactionCount");
            BigDecimal todayAmount = (BigDecimal) todayStats.get("totalAmount");
            totalTodayTransactionCount += todayTransactionCount;
            totalTodayAmount = totalTodayAmount.add(todayAmount);
        }
        
        // 3. 计算团队统计数据
        statistics.put("merchantCount", totalMerchantCount);
        statistics.put("validMerchantCount", totalValidMerchantCount);
        
        // 计算笔均交易额
        BigDecimal averageTransactionAmount = BigDecimal.ZERO;
        if (totalTransactionCount > 0) {
            averageTransactionAmount = totalAmount.divide(new BigDecimal(totalTransactionCount), 2, java.math.RoundingMode.DOWN);
        }
        statistics.put("averageTransactionAmount", averageTransactionAmount);
        
        // 计算日均交易额
        BigDecimal dailyAverageAmount = BigDecimal.ZERO;
        if (totalTodayTransactionCount > 0) {
            dailyAverageAmount = totalTodayAmount.divide(new BigDecimal(totalTodayTransactionCount), 2, java.math.RoundingMode.DOWN);
        }
        statistics.put("dailyAverageAmount", dailyAverageAmount);
        
        return statistics;
    }
} 