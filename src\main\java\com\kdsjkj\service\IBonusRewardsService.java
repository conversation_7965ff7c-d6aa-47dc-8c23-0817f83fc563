package com.kdsjkj.service;

import com.kdsjkj.entity.BonusRewards;
import com.kdsjkj.dto.UpdateBonusRewardRequest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 分红奖励表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface IBonusRewardsService extends IService<BonusRewards> {
    
    /**
     * 获取分红奖励配置
     * 
     * @return 分红奖励配置
     */
    BonusRewards getBonusReward();
    
    /**
     * 更新分红奖励配置
     * 
     * @param id 配置ID
     * @param request 更新请求
     * @return 更新后的配置
     */
    BonusRewards updateBonusReward(Long id, UpdateBonusRewardRequest request);
}
