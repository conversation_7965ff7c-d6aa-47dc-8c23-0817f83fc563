---
description:
globs:
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
# Java后端开发规范指南

## 代码风格和结构
- 编写清晰、高效且文档完善的Java代码，提供准确的Spring Boot示例
- 在整个代码中使用Spring Boot最佳实践和约定
- 创建Web服务时实现RESTful API设计模式
- 使用描述性的方法和变量名，遵循驼峰命名规范
- 合理组织Spring Boot应用程序结构：控制器、服务、仓库、模型、配置

## Spring Boot具体细节
- 使用Spring Boot starters快速设置项目和管理依赖
- 正确使用注解（如@SpringBootApplication、@RestController、@Service）
- 有效利用Spring Boot的自动配置功能
- 使用@ControllerAdvice和@ExceptionHandler实现适当的异常处理

## 命名规范
- 类名使用大驼峰命名法（如UserController、OrderService）
- 方法和变量名使用小驼峰命名法（如findUserById、isOrderValid）
- 常量使用全大写（如MAX_RETRY_ATTEMPTS、DEFAULT_PAGE_SIZE）

## Java和Spring Boot使用
- 适当使用Java 17或更高版本特性（如records、sealed classes、模式匹配）
- 利用Spring Boot 3.x特性和最佳实践
- 适当使用Spring Data JPA进行数据库操作
- 使用Bean Validation实现适当的验证（如@Valid、自定义验证器）

## 配置和属性
- 使用application.properties或application.yml进行配置
- 使用Spring Profiles实现环境特定配置
- 使用@ConfigurationProperties实现类型安全的配置属性

## 依赖注入和IoC
- 优先使用构造器注入而不是字段注入，以提高可测试性
- 利用Spring的IoC容器管理bean生命周期

## 测试
- 使用JUnit 5和Spring Boot Test编写单元测试
- 使用MockMvc测试Web层
- 使用@SpringBootTest实现集成测试
- 使用@DataJpaTest进行仓库层测试

## 性能和可扩展性
- 使用Spring Cache抽象实现缓存策略
- 使用@Async进行非阻塞操作的异步处理
- 实现适当的数据库索引和查询优化

## 安全性
- 实现Spring Security进行身份验证和授权
- 使用适当的密码编码（如BCrypt）
- 必要时实现CORS配置

## 日志和监控
- 使用SLF4J和Logback进行日志记录
- 实现适当的日志级别（ERROR、WARN、INFO、DEBUG）
- 使用Spring Boot Actuator进行应用程序监控和指标收集

## API文档
- 使用Springdoc OpenAPI（原Swagger）进行API文档生成

## 数据访问和ORM
- 使用Spring Data JPA进行数据库操作
- 实现适当的实体关系和级联
- 使用Flyway或Liquibase等工具进行数据库迁移

## 构建和部署
- 使用Maven进行依赖管理和构建过程
- 为不同环境（开发、测试、生产）实现适当的配置文件
- 适当使用Docker进行容器化

## 遵循最佳实践
- RESTful API设计（正确使用HTTP方法、状态码等）
- 微服务架构（如适用）
- 使用Spring的@Async或Spring WebFlux进行异步处理

## 设计原则
- 遵循SOLID原则
- 在Spring Boot应用程序设计中保持高内聚和低耦合

## 项目结构示例
```
src/main/java/com/example/project/
├── config/          # 配置类
├── controller/      # 控制器
├── service/         # 服务接口
│   └── impl/       # 服务实现
├── repository/      # 数据访问层
├── model/          # 实体类
│   ├── entity/     # 数据库实体
│   ├── dto/        # 数据传输对象
│   └── vo/         # 视图对象
├── common/         # 公共组件
│   ├── constant/   # 常量
│   ├── exception/  # 异常处理
│   └── utils/      # 工具类
└── security/       # 安全相关
```

## 代码示例

### 控制器示例
```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理接口")
public class UserController {
    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping
    @ApiOperation("创建用户")
    public ResponseEntity<Result<UserDTO>> createUser(@Valid @RequestBody UserDTO userDTO) {
        return ResponseEntity.ok(Result.success(userService.createUser(userDTO)));
    }

    @GetMapping("/{id}")
    @ApiOperation("获取用户详情")
    public ResponseEntity<Result<UserDTO>> getUserById(@PathVariable Long id) {
        return ResponseEntity.ok(Result.success(userService.findById(id)));
    }
}
```

### 服务接口示例
```java
public interface UserService {
    UserDTO createUser(UserDTO userDTO);
    UserDTO findById(Long id);
    Page<UserDTO> findAll(Pageable pageable);
    void deleteUser(Long id);
}
```

### 实体类示例
```java
@Entity
@Table(name = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Column(nullable = false)
    private String username;

    @Email
    @Column(nullable = false)
    private String email;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
```

### 异常处理示例
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleResourceNotFoundException(ResourceNotFoundException ex) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new ErrorResponse(ex.getMessage()));
    }
}
```

### 配置类示例
```java
@Configuration
@ConfigurationProperties(prefix = "app")
@Data
public class AppConfig {
    private String name;
    private String version;
    private Security security = new Security();

    @Data
    public static class Security {
        private String secret;
        private long tokenValidityInSeconds;
    }
}
```

## 接口开发三层架构规范

### Controller层规范
1. 职责：
   - 接收HTTP请求
   - 参数校验
   - 调用Service层方法
   - 返回统一响应格式

2. 命名规范：
   - 类名以Controller结尾
   - 方法名要体现业务含义
   - URL路径使用小写字母，多个单词用连字符（-）分隔

3. 注解使用：
   - @RestController：标识REST控制器
   - @RequestMapping：定义基础URL路径
   - @Api：Swagger文档注解
   - @Valid：参数校验注解

4. 示例代码：
```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理接口")
public class UserController {
    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping
    @ApiOperation("创建用户")
    public ResponseEntity<Result<UserDTO>> createUser(@Valid @RequestBody UserDTO userDTO) {
        return ResponseEntity.ok(Result.success(userService.createUser(userDTO)));
    }

    @GetMapping("/{id}")
    @ApiOperation("获取用户详情")
    public ResponseEntity<Result<UserDTO>> getUserById(@PathVariable Long id) {
        return ResponseEntity.ok(Result.success(userService.findById(id)));
    }
}
```

### Service层规范
1. 职责：
   - 实现业务逻辑
   - 事务管理
   - 调用Mapper层
   - 数据转换（DTO、VO等）

2. 命名规范：
   - 接口以Service结尾
   - 实现类以ServiceImpl结尾
   - 方法名要体现业务含义

3. 注解使用：
   - @Service：标识服务类
   - @Transactional：事务管理
   - @Cacheable：缓存注解

4. 示例代码：
```java
public interface UserService {
    UserDTO createUser(UserDTO userDTO);
    UserDTO findById(Long id);
    Page<UserDTO> findAll(Pageable pageable);
    void deleteUser(Long id);
}

@Service
@Transactional
public class UserServiceImpl implements UserService {
    private final UserMapper userMapper;
    private final UserConverter userConverter;

    @Autowired
    public UserServiceImpl(UserMapper userMapper, UserConverter userConverter) {
        this.userMapper = userMapper;
        this.userConverter = userConverter;
    }

    @Override
    public UserDTO createUser(UserDTO userDTO) {
        User user = userConverter.toEntity(userDTO);
        userMapper.insert(user);
        return userConverter.toDTO(user);
    }

    @Override
    @Cacheable(value = "users", key = "#id")
    public UserDTO findById(Long id) {
        User user = userMapper.selectById(id);
        return userConverter.toDTO(user);
    }
}
```

### Mapper层规范
1. 职责：
   - 数据库操作
   - SQL语句定义
   - 结果集映射

2. 命名规范：
   - 接口以Mapper结尾
   - XML文件与接口同名
   - 方法名要体现操作类型

3. 注解使用：
   - @Mapper：标识Mapper接口
   - @Select：查询注解
   - @Insert：插入注解
   - @Update：更新注解
   - @Delete：删除注解

4. 示例代码：
```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(String username);

    @Select("SELECT * FROM users WHERE email = #{email}")
    User findByEmail(String email);
}

<!-- UserMapper.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.example.entity.User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="email" property="email"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT * FROM users WHERE username = #{username}
    </select>
</mapper>
```

### 三层交互规范
1. 数据流向：
   - Controller层接收请求参数
   - Service层处理业务逻辑
   - Mapper层执行数据库操作
   - 数据通过DTO/VO在各层之间传递

2. 异常处理：
   - Controller层捕获异常并转换为统一响应格式
   - Service层抛出业务异常
   - Mapper层抛出数据库异常

3. 事务管理：
   - 事务注解放在Service层
   - 确保事务的原子性和一致性

4. 数据校验：
   - 参数校验在Controller层进行
   - 业务规则校验在Service层进行
   - 数据库约束在Mapper层体现
5.jdk版本：
   -1.8
6.加文件前先检查有没有相同功能的文件

7 不能超出我的意思写代码
8 我是jdk1.8