package com.kdsjkj.appTest.xiaochengxu.版本详情;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayOpenMiniVersionDetailQueryRequest;
import com.alipay.api.response.AlipayOpenMiniVersionDetailQueryResponse;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.AlipayOpenMiniVersionDetailQueryModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayOpenMiniVersionDetailApiQuery {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniVersionDetailQueryRequest request = new AlipayOpenMiniVersionDetailQueryRequest();
        AlipayOpenMiniVersionDetailQueryModel model = new AlipayOpenMiniVersionDetailQueryModel();
        
        // 设置小程序版本号
        model.setAppVersion("0.0.5");
        
        // 设置小程序端
        model.setBundleId("com.alipay.alipaywallet");
        
        request.setBizModel(model);
        
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenMiniVersionDetailQueryResponse response = alipayClient.execute(request);
        System.out.println();
        System.out.println(response.getBody());
        System.out.println();
        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            System.out.println(diagnosisUrl);
        }
    }
}
