package com.kdsjkj.service.impl;

import com.kdsjkj.entity.AgentPoints;
import com.kdsjkj.mapper.AgentPointsMapper;
import com.kdsjkj.service.IAgentPointsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 代理积分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
public class AgentPointsServiceImpl extends ServiceImpl<AgentPointsMapper, AgentPoints> implements IAgentPointsService {

    @Override
    @Transactional
    public boolean addPointsToAgent(String agentUid, Integer points) {
        try {
            log.info("开始为代理添加积分 - agentUid: {}, points: {}", agentUid, points);

            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.error("代理UID为空，无法添加积分");
                return false;
            }

            if (points == null || points <= 0) {
                log.error("积分数量无效 - points: {}", points);
                return false;
            }

            // 查询代理是否已有积分记录
            AgentPoints existingPoints = this.lambdaQuery()
                    .eq(AgentPoints::getUid, agentUid)
                    .one();

            if (existingPoints != null) {
                // 更新现有积分
                Integer newPoints = existingPoints.getPoints() + points;
                boolean updateResult = this.lambdaUpdate()
                        .eq(AgentPoints::getUid, agentUid)
                        .set(AgentPoints::getPoints, newPoints)
                        .update();

                if (updateResult) {
                    log.info("代理积分更新成功 - agentUid: {}, 原积分: {}, 新增: {}, 总积分: {}",
                            agentUid, existingPoints.getPoints(), points, newPoints);
                } else {
                    log.error("代理积分更新失败 - agentUid: {}", agentUid);
                }
                return updateResult;
            } else {
                // 创建新的积分记录
                AgentPoints newAgentPoints = new AgentPoints();
                newAgentPoints.setUid(agentUid);
                newAgentPoints.setPoints(points);

                boolean saveResult = this.save(newAgentPoints);
                if (saveResult) {
                    log.info("代理积分记录创建成功 - agentUid: {}, 积分: {}", agentUid, points);
                } else {
                    log.error("代理积分记录创建失败 - agentUid: {}", agentUid);
                }
                return saveResult;
            }

        } catch (Exception e) {
            log.error("为代理添加积分异常 - agentUid: {}, points: {}", agentUid, points, e);
            return false;
        }
    }

    @Override
    public Integer getAgentPoints(String agentUid) {
        try {
            if (agentUid == null || agentUid.trim().isEmpty()) {
                return 0;
            }

            AgentPoints agentPoints = this.lambdaQuery()
                    .eq(AgentPoints::getUid, agentUid)
                    .one();

            return agentPoints != null ? agentPoints.getPoints() : 0;
        } catch (Exception e) {
            log.error("查询代理积分异常 - agentUid: {}", agentUid, e);
            return 0;
        }
    }

    @Override
    @Transactional
    public boolean setAgentPoints(String agentUid, Integer points) {
        try {
            log.info("开始设置代理积分 - agentUid: {}, points: {}", agentUid, points);

            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.error("代理UID为空，无法设置积分");
                return false;
            }

            if (points == null || points < 0) {
                log.error("积分数量无效 - points: {}", points);
                return false;
            }

            // 查询代理是否已有积分记录
            AgentPoints existingPoints = this.lambdaQuery()
                    .eq(AgentPoints::getUid, agentUid)
                    .one();

            if (existingPoints != null) {
                // 更新现有积分
                boolean updateResult = this.lambdaUpdate()
                        .eq(AgentPoints::getUid, agentUid)
                        .set(AgentPoints::getPoints, points)
                        .update();

                if (updateResult) {
                    log.info("代理积分设置成功 - agentUid: {}, 积分: {}", agentUid, points);
                } else {
                    log.error("代理积分设置失败 - agentUid: {}", agentUid);
                }
                return updateResult;
            } else {
                // 创建新的积分记录
                AgentPoints newAgentPoints = new AgentPoints();
                newAgentPoints.setUid(agentUid);
                newAgentPoints.setPoints(points);

                boolean saveResult = this.save(newAgentPoints);
                if (saveResult) {
                    log.info("代理积分记录创建成功 - agentUid: {}, 积分: {}", agentUid, points);
                } else {
                    log.error("代理积分记录创建失败 - agentUid: {}", agentUid);
                }
                return saveResult;
            }

        } catch (Exception e) {
            log.error("设置代理积分异常 - agentUid: {}, points: {}", agentUid, points, e);
            return false;
        }
    }

}
