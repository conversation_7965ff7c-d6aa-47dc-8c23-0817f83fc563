package com.kdsjkj.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 代理交易统计数据传输对象
 */
@Data
public class AgentTransactionDTO {
    /**
     * 排名序号
     */
    private Integer rank;
    
    /**
     * 代理UID
     */
    private String uid;
    
    /**
     * 代理姓名
     */
    private String name;
    
    /**
     * 交易总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 交易总笔数
     */
    private Integer transactionCount;
} 