server:
  address: 0.0.0.0  # 允许所有IP访问

# 跨域配置
cors:
  allowed-origins: "*"
  allowed-methods: "*"
  allowed-headers: "*"
  allow-credentials: true

# 默认环境配置
spring:
  mvc:
    static-path-pattern: /**
  web:
    resources:
      static-locations: file:./,classpath:/static/
  profiles:
    active: dev  # 默认使用开发环境，可通过启动参数覆盖
  datasource:
    url: *******************************************************************************************************
    username: sft
    password: 123456zxc
    driver-class-name: com.mysql.cj.jdbc.Driver
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.kdsjkj.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0

