package com.kdsjkj.controller;

import com.kdsjkj.service.IAgentFundService;
import com.kdsjkj.service.IAgentService;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.entity.AgentFund;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 第三页数据统计控制器 - 奖励数据统计
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/api/reward-data")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class RewardDataController {
    
    @Autowired
    private IAgentFundService agentFundService;
    
    @Autowired
    private IAgentService agentService;
    
    /**
     * 获取奖励数据统计
     * 
     * @param agentUid 代理UID
     * @return 奖励统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getRewardStatistics(
            @RequestParam(required = true) String agentUid) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取奖励数据统计 - 代理UID: {}", agentUid);
            
            // 1. 验证代理是否存在
            Agent agent = agentService.getAgentByUid(agentUid);
            if (agent == null) {
                response.put("success", false);
                response.put("message", "代理不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 2. 获取代理资金记录
            List<AgentFund> agentFunds = agentFundService.getByAgentUid(agentUid);
            
            // 3. 统计各类奖励数据
            Map<String, Object> statistics = calculateRewardStatistics(agentFunds);
            
            // 4. 添加代理信息
            Map<String, Object> agentInfo = new HashMap<>();
            agentInfo.put("uid", agent.getUid());
            agentInfo.put("name", agent.getName());
            agentInfo.put("phone", agent.getPhone());
            statistics.put("agentInfo", agentInfo);
            
            // 5. 添加原始资金记录列表
            statistics.put("fundRecords", convertToFundRecordList(agentFunds));
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "奖励数据统计获取成功");
            
            log.info("奖励数据统计获取成功 - 代理UID: {}, 总记录数: {}", agentUid, agentFunds.size());
            
        } catch (Exception e) {
            log.error("获取奖励数据统计异常 - 代理UID: {}", agentUid, e);
            response.put("success", false);
            response.put("message", "获取奖励数据统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 计算奖励统计数据
     */
    private Map<String, Object> calculateRewardStatistics(List<AgentFund> agentFunds) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 初始化各类奖励统计
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal achievementReward = BigDecimal.ZERO;    // 1-达标奖励
        BigDecimal activationReward = BigDecimal.ZERO;     // 2-激活奖励
        BigDecimal commissionReward = BigDecimal.ZERO;     // 3-分润奖励
        BigDecimal dividendReward = BigDecimal.ZERO;       // 4-分红奖励
        
        int totalCount = 0;
        int achievementCount = 0;
        int activationCount = 0;
        int commissionCount = 0;
        int dividendCount = 0;
        
        // 统计各类奖励
        for (AgentFund fund : agentFunds) {
            totalAmount = totalAmount.add(fund.getAmount());
            totalCount++;
            
            switch (fund.getObtainType()) {
                case 1: // 达标奖励
                    achievementReward = achievementReward.add(fund.getAmount());
                    achievementCount++;
                    break;
                case 2: // 激活奖励
                    activationReward = activationReward.add(fund.getAmount());
                    activationCount++;
                    break;
                case 3: // 分润奖励
                    commissionReward = commissionReward.add(fund.getAmount());
                    commissionCount++;
                    break;
                case 4: // 分红奖励
                    dividendReward = dividendReward.add(fund.getAmount());
                    dividendCount++;
                    break;
                default:
                    log.warn("未知的奖励类型: {}", fund.getObtainType());
                    break;
            }
        }
        
        // 设置统计数据
        statistics.put("totalAmount", totalAmount);
        statistics.put("totalCount", totalCount);
        
        // 各类奖励统计
        Map<String, Object> achievementStats = new HashMap<>();
        achievementStats.put("amount", achievementReward);
        achievementStats.put("count", achievementCount);
        statistics.put("achievementReward", achievementStats);
        
        Map<String, Object> activationStats = new HashMap<>();
        activationStats.put("amount", activationReward);
        activationStats.put("count", activationCount);
        statistics.put("activationReward", activationStats);
        
        Map<String, Object> commissionStats = new HashMap<>();
        commissionStats.put("amount", commissionReward);
        commissionStats.put("count", commissionCount);
        statistics.put("commissionReward", commissionStats);
        
        Map<String, Object> dividendStats = new HashMap<>();
        dividendStats.put("amount", dividendReward);
        dividendStats.put("count", dividendCount);
        statistics.put("dividendReward", dividendStats);
        
        // 奖励类型说明
        Map<String, String> rewardTypeDescription = new HashMap<>();
        rewardTypeDescription.put("1", "达标奖励");
        rewardTypeDescription.put("2", "激活奖励");
        rewardTypeDescription.put("3", "分润奖励");
        rewardTypeDescription.put("4", "分红奖励");
        statistics.put("rewardTypeDescription", rewardTypeDescription);
        
        return statistics;
    }
    
    /**
     * 转换资金记录为前端友好的格式
     */
    private List<Map<String, Object>> convertToFundRecordList(List<AgentFund> agentFunds) {
        return agentFunds.stream().map(fund -> {
            Map<String, Object> record = new HashMap<>();
            record.put("id", fund.getId());
            record.put("agentUid", fund.getAgentUid());
            record.put("amount", fund.getAmount());
            record.put("obtainType", fund.getObtainType());
            record.put("obtainTime", fund.getObtainTime());
            
            // 添加奖励类型描述
            String typeDescription = getRewardTypeDescription(fund.getObtainType());
            record.put("obtainTypeDescription", typeDescription);
            
            return record;
        }).collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取奖励类型描述
     */
    private String getRewardTypeDescription(Integer obtainType) {
        switch (obtainType) {
            case 1:
                return "达标奖励";
            case 2:
                return "激活奖励";
            case 3:
                return "分润奖励";
            case 4:
                return "分红奖励";
            default:
                return "未知奖励";
        }
    }
} 