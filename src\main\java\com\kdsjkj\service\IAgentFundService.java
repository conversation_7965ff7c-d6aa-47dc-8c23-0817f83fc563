package com.kdsjkj.service;

import com.kdsjkj.entity.AgentFund;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 代理资金表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IAgentFundService extends IService<AgentFund> {

    /**
     * 保存代理分润记录
     * 
     * @param agentUid 代理UID
     * @param amount 分润金额
     * @param obtainType 获取方式：1达标奖励 2激活奖励 3分润奖励 4分红奖励
     * @return 保存是否成功
     */
    boolean saveAgentFund(String agentUid, BigDecimal amount, Integer obtainType);

    /**
     * 根据代理UID查询资金记录
     * 
     * @param agentUid 代理UID
     * @return 资金记录列表
     */
    List<AgentFund> getByAgentUid(String agentUid);

    /**
     * 根据获取方式查询资金记录
     * 
     * @param obtainType 获取方式
     * @return 资金记录列表
     */
    List<AgentFund> getByObtainType(Integer obtainType);

    /**
     * 计算代理总收益
     * 
     * @param agentUid 代理UID
     * @return 总收益金额
     */
    BigDecimal getTotalAmountByAgentUid(String agentUid);

}
