package com.kdsjkj.service;

import com.kdsjkj.entity.AlipayServiceOrder;

/**
 * 支付宝服务市场订单通知服务接口
 */
public interface AlipayServiceOrderService {
    
    /**
     * 保存订单通知记录
     * 
     * @param order 订单记录
     * @return 保存后的记录
     */
    AlipayServiceOrder saveOrder(AlipayServiceOrder order);
    
    /**
     * 根据通知ID查询订单
     * 
     * @param notifyId 通知ID
     * @return 订单记录
     */
    AlipayServiceOrder getByNotifyId(String notifyId);
    
    /**
     * 根据商户PID和订单ID查询订单
     * 
     * @param merchantPid 商户PID
     * @param orderId 订单ID
     * @return 订单记录
     */
    AlipayServiceOrder getByMerchantAndOrderId(String merchantPid, String orderId);
    
    /**
     * 更新处理状态
     * 
     * @param id 订单ID
     * @param status 新状态
     * @param errorMsg 错误信息（可选）
     * @return 更新后的记录
     */
    AlipayServiceOrder updateStatus(Long id, String status, String errorMsg);
} 