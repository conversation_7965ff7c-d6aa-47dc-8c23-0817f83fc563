package com.kdsjkj.service.impl;

import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.dto.ServiceMarketOrderDTO;
import com.kdsjkj.mapper.ServiceMarketOrderMapper;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务市场订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
public class ServiceMarketOrderServiceImpl extends ServiceImpl<ServiceMarketOrderMapper, ServiceMarketOrder> implements IServiceMarketOrderService {

    @Override
    public List<ServiceMarketOrderDTO> getOrdersByActiveCode(String activeCode) {
        List<ServiceMarketOrder> orders = this.lambdaQuery()
                .eq(ServiceMarketOrder::getActiveCode, activeCode)
                .select(
                    ServiceMarketOrder::getMerchantPid,
                    ServiceMarketOrder::getContactor,
                    ServiceMarketOrder::getOrderTime,
                    ServiceMarketOrder::getPhone,
                    ServiceMarketOrder::getConsumerAppname,
                    ServiceMarketOrder::getConsumerMiniappid,
                    ServiceMarketOrder::getRate
                )
                .orderByDesc(ServiceMarketOrder::getOrderTime)
                .list();

        return orders.stream().map(order -> {
            ServiceMarketOrderDTO dto = new ServiceMarketOrderDTO();
            BeanUtils.copyProperties(order, dto);
            // 将consumerMiniappid映射到appid字段
            dto.setAppid(order.getConsumerMiniappid());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ServiceMarketOrder> getOrdersByRate(BigDecimal minRate, BigDecimal maxRate) {
        return this.lambdaQuery()
                .ge(minRate != null, ServiceMarketOrder::getRate, minRate)
                .le(maxRate != null, ServiceMarketOrder::getRate, maxRate)
                .orderByDesc(ServiceMarketOrder::getOrderTime)
                .list();
    }

    @Override
    public boolean updateOrderRate(Long orderId, BigDecimal rate) {
        return this.lambdaUpdate()
                .eq(ServiceMarketOrder::getId, orderId)
                .set(ServiceMarketOrder::getRate, rate)
                .update();
    }

    @Override
    public String getAgentUidByAppid(String appid) {
        if (appid == null || appid.trim().isEmpty()) {
            return null;
        }
        
        ServiceMarketOrder order = this.lambdaQuery()
                .eq(ServiceMarketOrder::getConsumerMiniappid, appid)
                .select(ServiceMarketOrder::getAgentUid)
                .orderByDesc(ServiceMarketOrder::getId)
                .last("LIMIT 1")
                .one();
        
        return order != null ? order.getAgentUid() : null;
    }
    
    @Override
    public BigDecimal getMerchantRateByAppid(String appid) {
        if (appid == null || appid.trim().isEmpty()) {
            return null;
        }
        
        ServiceMarketOrder order = this.lambdaQuery()
                .eq(ServiceMarketOrder::getConsumerMiniappid, appid)
                .select(ServiceMarketOrder::getRate)
                .orderByDesc(ServiceMarketOrder::getId)
                .last("LIMIT 1")
                .one();
        
        return order != null ? order.getRate() : null;
    }
    
    @Override
    public List<String> getMerchantAppIdsByAgentUidAndTimeRange(String agentUid, LocalDateTime startTime, LocalDateTime endTime) {
        if (agentUid == null || agentUid.trim().isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        List<ServiceMarketOrder> orders = this.lambdaQuery()
                .eq(ServiceMarketOrder::getAgentUid, agentUid)
                .ge(ServiceMarketOrder::getOrderTime, startTime)
                .le(ServiceMarketOrder::getOrderTime, endTime)
                .select(ServiceMarketOrder::getConsumerMiniappid)
                .list();
        
        return orders.stream()
                .map(ServiceMarketOrder::getConsumerMiniappid)
                .filter(appid -> appid != null && !appid.trim().isEmpty())
                .distinct()
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<String> getMerchantAppIdsByAgentUid(String agentUid) {
        if (agentUid == null || agentUid.trim().isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        List<ServiceMarketOrder> orders = this.lambdaQuery()
                .eq(ServiceMarketOrder::getAgentUid, agentUid)
                .select(ServiceMarketOrder::getConsumerMiniappid)
                .list();
        
        return orders.stream()
                .map(ServiceMarketOrder::getConsumerMiniappid)
                .filter(appid -> appid != null && !appid.trim().isEmpty())
                .distinct()
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public int getMerchantCountByAgentUid(String agentUid) {
        if (agentUid == null || agentUid.trim().isEmpty()) {
            return 0;
        }
        
        List<String> appIds = getMerchantAppIdsByAgentUid(agentUid);
        return appIds.size();
    }
}
