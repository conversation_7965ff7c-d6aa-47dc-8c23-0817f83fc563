package com.kdsjkj.appTest.xiaochengxu.B创建体验版本.应用添加成员;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.response.AlipayOpenAppMembersCreateResponse;
import com.alipay.api.request.AlipayOpenAppMembersCreateRequest;
import com.alipay.api.domain.AlipayOpenAppMembersCreateModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

//添加成员
public class AlipayOpenAppMembersCreate {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenAppMembersCreateRequest request = new AlipayOpenAppMembersCreateRequest();
        AlipayOpenAppMembersCreateModel model = new AlipayOpenAppMembersCreateModel();
        
        // 设置角色
        model.setRole("DEVELOPER");
        
        // 设置支付宝账户邮箱
        model.setLogonId("15908293192");
        
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenAppMembersCreateResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }

    // 移除原有的getAlipayConfig方法，改为使用统一配置类
}