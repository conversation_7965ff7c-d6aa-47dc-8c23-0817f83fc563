package com.kdsjkj.dto;

import lombok.Data;

/**
 * 支付宝授权通知请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class AlipayAuthNotifyRequest {
    
    /**
     * 通知校验ID
     */
    private String notify_id;
    
    /**
     * 通知类型
     */
    private String notify_type;
    
    /**
     * 业务状态
     */
    private String status;
    
    /**
     * 通知发送时间
     */
    private String notify_time;
    
    /**
     * 字符集
     */
    private String charset;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 第三方应用的APPID
     */
    private String app_id;
    
    /**
     * 授权小程序的APPID
     */
    private String auth_app_id;
    
    /**
     * 签名
     */
    private String sign;
    
    /**
     * 签名类型
     */
    private String sign_type;
    
    /**
     * 业务内容
     */
    private String biz_content;
}
