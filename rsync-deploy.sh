#!/bin/bash

# 配置文件路径
CONFIG_FILE="deploy.conf"

# 默认配置
DEFAULT_CONFIG="
SERVER_HOST=your_server_ip
SERVER_USER=root
SERVER_PORT=22
REMOTE_PATH=/opt/app/
JAR_NAME=backend.jar
LOCAL_JAR_PATH=./target/
BACKUP_COUNT=5
JAVA_OPTS='-Xms512m -Xmx1024m -Dspring.profiles.active=prod'
"

# 创建配置文件（如果不存在）
if [ ! -f "$CONFIG_FILE" ]; then
    echo "$DEFAULT_CONFIG" > "$CONFIG_FILE"
    echo "✓ 已创建配置文件 $CONFIG_FILE，请修改其中的服务器信息"
    exit 0
fi

# 读取配置文件
source "$CONFIG_FILE"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v rsync &> /dev/null; then
        log_error "rsync 未安装，请先安装 rsync"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        log_error "ssh 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查本地文件
check_local_files() {
    log_info "检查本地文件..."
    
    if [ ! -f "${LOCAL_JAR_PATH}${JAR_NAME}" ]; then
        log_error "找不到JAR文件: ${LOCAL_JAR_PATH}${JAR_NAME}"
        log_warning "请先执行: mvn clean package"
        exit 1
    fi
    
    local file_size=$(ls -lh "${LOCAL_JAR_PATH}${JAR_NAME}" | awk '{print $5}')
    log_success "找到JAR文件: ${LOCAL_JAR_PATH}${JAR_NAME} (${file_size})"
}

# 测试服务器连接
test_connection() {
    log_info "测试服务器连接..."
    
    if ssh -p $SERVER_PORT -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST "echo 'Connection OK'" &>/dev/null; then
        log_success "服务器连接正常"
    else
        log_error "无法连接到服务器 $SERVER_USER@$SERVER_HOST:$SERVER_PORT"
        exit 1
    fi
}

# 备份远程文件
backup_remote() {
    log_info "创建远程备份..."
    
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "
        cd $REMOTE_PATH
        if [ -f $JAR_NAME ]; then
            # 创建备份
            backup_name=\"${JAR_NAME}.backup.\$(date +%Y%m%d_%H%M%S)\"
            cp $JAR_NAME \$backup_name
            echo \"✓ 备份创建: \$backup_name\"
            
            # 清理旧备份（保留最新的N个）
            ls -t ${JAR_NAME}.backup.* 2>/dev/null | tail -n +$((BACKUP_COUNT + 1)) | xargs rm -f
            echo \"✓ 清理旧备份完成\"
        else
            echo \"! 没有找到旧版本文件\"
        fi
    "
}

# 同步文件
sync_files() {
    log_info "同步文件到服务器..."
    
    # 使用rsync同步文件
    rsync -avz --progress \
        -e "ssh -p $SERVER_PORT" \
        "${LOCAL_JAR_PATH}${JAR_NAME}" \
        "$SERVER_USER@$SERVER_HOST:$REMOTE_PATH"
    
    if [ $? -eq 0 ]; then
        log_success "文件同步完成"
    else
        log_error "文件同步失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启应用服务..."
    
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "
        cd $REMOTE_PATH
        
        # 停止旧进程
        if pgrep -f '$JAR_NAME' > /dev/null; then
            echo '停止旧进程...'
            pkill -f '$JAR_NAME'
            sleep 5
            
            # 强制杀死（如果还在运行）
            if pgrep -f '$JAR_NAME' > /dev/null; then
                echo '强制停止进程...'
                pkill -9 -f '$JAR_NAME'
                sleep 2
            fi
        fi
        
        # 启动新进程
        echo '启动新服务...'
        nohup java $JAVA_OPTS -jar $JAR_NAME > app.log 2>&1 &
        
        echo '等待服务启动...'
        sleep 10
        
        # 检查服务状态
        if pgrep -f '$JAR_NAME' > /dev/null; then
            echo '✅ 服务启动成功'
            echo '最新日志:'
            tail -n 10 app.log
        else
            echo '❌ 服务启动失败'
            echo '错误日志:'
            tail -n 20 app.log
            exit 1
        fi
    "
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "================================================"
    echo "        🚀 JAR部署脚本 v2.0"
    echo "================================================"
    echo -e "${NC}"
    
    check_dependencies
    check_local_files
    test_connection
    backup_remote
    sync_files
    restart_service
    
    log_success "🎉 部署完成！"
}

# 执行主函数
main "$@" 