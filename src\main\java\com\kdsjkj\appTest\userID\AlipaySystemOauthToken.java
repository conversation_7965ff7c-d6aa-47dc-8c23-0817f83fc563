package com.kdsjkj.appTest.userID;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.kdsjkj.entity.GetUserId;
import com.kdsjkj.mapper.GetUserIdMapper;
import com.kdsjkj.config.AlipayMiniProgramConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AlipaySystemOauthToken {

    @Autowired
    private GetUserIdMapper getUserIdMapper;

    /**
     * 通过授权码获取访问令牌（使用统一配置）
     * @param authCode 授权码
     * @return 访问令牌响应
     * @throws AlipayApiException
     */
    public AlipaySystemOauthTokenResponse getAccessToken(String authCode) throws AlipayApiException {
        log.info("=== 开始获取访问令牌 ===");
        log.info("授权码: {}", authCode);
        
        // 使用统一配置类，确保签名配置正确
        AlipayConfig alipayConfig = AlipayMiniProgramConfig.getAlipayConfig();
        String appAuthToken = AlipayMiniProgramConfig.getAppAuthToken();
        
        log.info("配置信息:");
        log.info("- AppId: {}", alipayConfig.getAppId());
        log.info("- 网关地址: {}", alipayConfig.getServerUrl());
        log.info("- 签名类型: {}", alipayConfig.getSignType());
        log.info("- 字符编码: {}", alipayConfig.getCharset());
        log.info("- app_auth_token: {}", appAuthToken);
        log.info("- 私钥长度: {}", alipayConfig.getPrivateKey() != null ? alipayConfig.getPrivateKey().length() : "null");
        log.info("- 支付宝公钥长度: {}", alipayConfig.getAlipayPublicKey() != null ? alipayConfig.getAlipayPublicKey().length() : "null");
        
        // 构造请求参数
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);
        request.setGrantType("authorization_code");
        
        // 设置第三方应用授权令牌
        // request.putOtherTextParam("app_auth_token", appAuthToken);
        
        log.info("请求参数:");
        log.info("- 授权码: {}", authCode);
        log.info("- 授权类型: authorization_code");
        log.info("- app_auth_token: {}", appAuthToken);

        try {
            // 使用统一配置初始化客户端
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            
            log.info("发送请求到支付宝...");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
            
            log.info("支付宝响应:");
            log.info("- 完整响应: {}", response.getBody());
            log.info("- 是否成功: {}", response.isSuccess());
            log.info("- 错误码: {}", response.getCode());
            log.info("- 错误信息: {}", response.getMsg());
            log.info("- 子错误码: {}", response.getSubCode());
            log.info("- 子错误信息: {}", response.getSubMsg());
            
            if (response.isSuccess()) {
                log.info("✅ 获取访问令牌成功");
                log.info("- 用户ID: {}", response.getUserId());
                log.info("- 访问令牌: {}", response.getAccessToken());
                log.info("- 刷新令牌: {}", response.getRefreshToken());
                return response;
            } else {
                log.error("❌ 获取访问令牌失败");
                
                // 分析具体错误
                if ("40004".equals(response.getCode())) {
                    log.error("业务处理失败，可能原因:");
                    log.error("1. app_auth_token 无效或过期");
                    log.error("2. 授权码已过期或已使用");
                    log.error("3. 小程序与第三方应用关联不完整");
                } else if ("40001".equals(response.getCode())) {
                    log.error("缺少必选参数");
                } else if ("40002".equals(response.getCode())) {
                    log.error("非法的参数");
                }
                
                throw new AlipayApiException("获取访问令牌失败: " + response.getSubMsg());
            }
            
        } catch (AlipayApiException e) {
            log.error("❌ 调用支付宝API异常", e);
            
            // 检查是否是签名错误
            if (e.getMessage().contains("sign check fail")) {
                log.error("🔧 签名验证失败，可能原因:");
                log.error("1. 应用私钥配置错误");
                log.error("2. 支付宝公钥配置错误");
                log.error("3. 签名类型不匹配");
                log.error("4. 字符编码不正确");
                log.error("5. 小程序未正确继承第三方应用的签名配置");
                
                log.error("💡 建议解决方案:");
                log.error("1. 检查支付宝开放平台的应用配置");
                log.error("2. 确认小程序已正确关联到第三方应用");
                log.error("3. 验证 app_auth_token 的有效性");
                log.error("4. 重新构建小程序以继承配置");
            }
            
            throw e;
        }
    }
    
    /**
     * 通过授权码获取访问令牌（指定app_auth_token）
     * @param authCode 授权码
     * @param appAuthToken 应用授权令牌
     * @return 访问令牌响应
     * @throws AlipayApiException
     */
    public AlipaySystemOauthTokenResponse getAccessToken(String authCode, String appAuthToken) throws AlipayApiException {
        log.info("=== 开始获取访问令牌（指定token） ===");
        log.info("授权码: {}", authCode);
        log.info("指定的app_auth_token: {}", appAuthToken);
        
        // 使用统一配置类
        AlipayConfig alipayConfig = AlipayMiniProgramConfig.getAlipayConfig();
        
        // 构造请求参数
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);
        request.setGrantType("authorization_code");
        
        // 设置指定的应用授权令牌
        request.putOtherTextParam("app_auth_token", appAuthToken);
        
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
            
            log.info("支付宝响应: {}", response.getBody());
            
            if (response.isSuccess()) {
                log.info("✅ 获取访问令牌成功，用户ID: {}", response.getUserId());
                return response;
            } else {
                log.error("❌ 获取访问令牌失败: {}, {}", response.getSubCode(), response.getSubMsg());
                throw new AlipayApiException("获取访问令牌失败: " + response.getSubMsg());
            }
        } catch (AlipayApiException e) {
            log.error("❌ 调用支付宝API异常", e);
            throw e;
        }
    }
    
    /**
     * 从数据库获取app_auth_token（备用方案）
     * @return app_auth_token
     */
    private String getAppAuthTokenFromDatabase() {
        try {
            GetUserId config = getUserIdMapper.getAnyValidConfig();
            if (config != null && config.getAppAuthToken() != null) {
                log.info("从数据库获取到app_auth_token: {}", config.getAppAuthToken());
                return config.getAppAuthToken();
            }
            
            log.error("数据库中没有找到有效的app_auth_token");
            return null;
        } catch (Exception e) {
            log.error("从数据库获取app_auth_token失败", e);
            return null;
        }
    }

    /**
     * 获取支付宝配置（备用方案，优先使用统一配置）
     */
    private AlipayConfig getAlipayConfig() {
        try {
            // 优先使用统一配置
            log.info("使用统一配置类");
            return AlipayMiniProgramConfig.getAlipayConfig();
        } catch (Exception e) {
            log.warn("统一配置获取失败，尝试从数据库获取配置", e);
            return getConfigFromDatabase();
        }
    }
    
    /**
     * 从数据库获取配置（备用方案）
     */
    private AlipayConfig getConfigFromDatabase() {
        try {
            GetUserId config = getUserIdMapper.getAnyValidConfig();
            if (config == null) {
                throw new RuntimeException("数据库中没有找到有效的支付宝配置");
            }

            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl(config.getServerUrl());
            alipayConfig.setAppId(config.getAppId());
            alipayConfig.setPrivateKey(config.getPrivateKey());
            alipayConfig.setFormat(config.getFormat());
            alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
            alipayConfig.setCharset(config.getCharset());
            alipayConfig.setSignType(config.getSignType());
            
            log.info("从数据库加载支付宝配置成功，AppId: {}", config.getAppId());
            return alipayConfig;
        } catch (Exception e) {
            log.error("从数据库加载支付宝配置失败，使用默认配置", e);
            return getDefaultAlipayConfig();
        }
    }
    
    /**
     * 获取默认的支付宝配置（最后备用方案）
     */
    private AlipayConfig getDefaultAlipayConfig() {
        log.warn("使用默认支付宝配置");
        return AlipayMiniProgramConfig.getAlipayConfig();
    }
} 