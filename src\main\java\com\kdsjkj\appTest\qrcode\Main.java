package com.kdsjkj.appTest.qrcode;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;

import com.google.zxing.WriterException;
import com.kdsjkj.appTest.qrcode.将二维码或条形码贴到指定图片上.PosterGenerator;
import com.kdsjkj.appTest.qrcode.生成二维码.QRCodeGenerator;

public class Main {
    public static void main(String[] args) {
        try {
            // 支付宝二维码图片链接
            String qrCodeUrl = "https://mass.alipay.com//wsdk//img?fileid=A*XP8lSqm-iiQAAAAAAAAAAAAAAQAAAQ&bz=am_afts_openhome&zoom=295w_310h";
            
            // 下载二维码图片
            System.out.println("正在下载二维码图片...");
            BufferedImage qrCodeImage = downloadImage(qrCodeUrl);
            
            if (qrCodeImage == null) {
                System.err.println("无法下载二维码图片，尝试生成二维码...");
                // 如果下载失败，则生成二维码
                qrCodeImage = QRCodeGenerator.generateQRCode(qrCodeUrl, 150, 150);
            } else {
                System.out.println("二维码图片下载成功！尺寸: " + qrCodeImage.getWidth() + "x" + qrCodeImage.getHeight());
                
                // 保存下载的二维码图片
                String qrCodeSavePath = "E:\\A帮人开发\\支付宝小程序\\前后端\\shouquanceshi\\shouquanceshi\\src\\main\\java\\com\\kdsjkj\\appTest\\qrcode\\下载的二维码.png";
                saveImage(qrCodeImage, qrCodeSavePath);
                System.out.println("二维码图片已保存到: " + qrCodeSavePath);
            }

            // 海报图片的路径（微信图片）
            String posterPath = "E:\\A帮人开发\\支付宝小程序\\前后端\\shouquanceshi\\shouquanceshi\\src\\main\\java\\com\\kdsjkj\\appTest\\qrcode\\微信图片_20250706164802.jpg";
            // 生成的海报保存路径
            String outputPath = "E:\\A帮人开发\\支付宝小程序\\前后端\\shouquanceshi\\shouquanceshi\\src\\main\\java\\com\\kdsjkj\\appTest\\qrcode\\带二维码的海报.png";
            
            // 计算二维码居中位置
            int x, y;
            try {
                // 读取海报图片获取尺寸
                BufferedImage posterImage = ImageIO.read(new File(posterPath));
                int posterWidth = posterImage.getWidth();
                int posterHeight = posterImage.getHeight();
                int qrWidth = qrCodeImage.getWidth();
                int qrHeight = qrCodeImage.getHeight();
                
                // 计算居中位置
                x = (posterWidth - qrWidth) / 2;
                y = (posterHeight - qrHeight) / 2;
                
                System.out.println("海报尺寸: " + posterWidth + "x" + posterHeight);
                System.out.println("二维码尺寸: " + qrWidth + "x" + qrHeight);
                System.out.println("计算的居中位置: (" + x + ", " + y + ")");
            } catch (IOException e) {
                System.err.println("无法读取海报图片，使用默认居中位置");
                // 使用默认居中位置
                x = 200;
                y = 200;
            }

            // 将二维码贴到海报上
            PosterGenerator.pasteCodeOnPoster(posterPath, outputPath, qrCodeImage, x, y);

            System.out.println("海报生成成功！输出路径：" + outputPath);
            System.out.println("二维码来源：" + qrCodeUrl);
            System.out.println("二维码位置（居中）：(" + x + ", " + y + ")");
        } catch (WriterException | IOException e) {
            System.err.println("生成海报时发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 下载网络图片
     */
    private static BufferedImage downloadImage(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            URLConnection connection = url.openConnection();
            // 设置User-Agent避免被拒绝
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(10000); // 10秒读取超时
            
            try (InputStream inputStream = connection.getInputStream()) {
                BufferedImage image = ImageIO.read(inputStream);
                return image;
            }
        } catch (Exception e) {
            System.err.println("下载图片失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 保存图片到本地
     */
    private static void saveImage(BufferedImage image, String filePath) throws IOException {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        ImageIO.write(image, "png", file);
    }
}