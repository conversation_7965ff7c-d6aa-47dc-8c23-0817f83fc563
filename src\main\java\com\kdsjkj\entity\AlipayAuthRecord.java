package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

/**
 * 支付宝授权记录表实体
 */
@Data
@TableName("alipay_auth_record")
public class AlipayAuthRecord {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键ID
    @TableField("notify_id")
    private String notifyId; // 通知ID
    @TableField("app_id")
    private String appId; // 第三方应用APPID
    @TableField("auth_app_id")
    private String authAppId; // 授权小程序APPID
    @TableField("app_auth_token")
    private String appAuthToken; // 授权令牌
    @TableField("app_refresh_token")
    private String appRefreshToken; // 刷新令牌
    @TableField("app_auth_code")
    private String appAuthCode; // 授权码
    @TableField("user_id")
    private String userId; // 授权商家用户ID
    @TableField("auth_time")
    private Long authTime; // 授权时间
    @TableField("expires_in")
    private Long expiresIn; // 令牌过期时间（秒）
    @TableField("re_expires_in")
    private Long reExpiresIn; // 刷新令牌过期时间（秒）
    @TableField("trigger_type")
    private String triggerType; // 授权触发者
    @TableField("notify_status")
    private String notifyStatus; // 通知状态
    @TableField("biz_status")
    private String bizStatus; // 业务状态
    @TableField("raw_notify_data")
    private String rawNotifyData; // 原始通知内容
    @TableField("process_status")
    private String processStatus; // 处理状态：SUCCESS-成功，FAILED-失败，PROCESSING-处理中
    @TableField("error_msg")
    private String errorMsg; // 错误信息
    @TableField("create_time")
    private Timestamp createTime; // 创建时间
    @TableField("update_time")
    private Timestamp updateTime; // 更新时间
} 