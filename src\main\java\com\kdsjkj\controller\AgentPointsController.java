package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.AgentPoints;
import com.kdsjkj.service.IAgentPointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 代理积分表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/agent-points")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class AgentPointsController {

    @Autowired
    private IAgentPointsService agentPointsService;

    /**
     * 根据代理UID查询积分
     * @param uid 代理UID
     * @return 积分信息
     */
    @GetMapping("/get-by-uid/{uid}")
    public Result getAgentPointsByUid(@PathVariable("uid") String uid) {
        try {
            if (uid == null || uid.trim().isEmpty()) {
                return Result.paramError("代理UID不能为空");
            }

            log.info("查询代理积分 - UID: {}", uid);

            // 查询代理积分
            AgentPoints agentPoints = agentPointsService.lambdaQuery()
                    .eq(AgentPoints::getUid, uid)
                    .one();

            if (agentPoints == null) {
                log.info("未找到代理积分记录 - UID: {}", uid);
                return Result.success("未找到代理积分记录", null);
            }

            log.info("查询代理积分成功 - UID: {}, 积分: {}", uid, agentPoints.getPoints());
            return Result.success("查询成功", agentPoints);

        } catch (Exception e) {
            log.error("查询代理积分异常 - UID: {}", uid, e);
            return Result.error("查询代理积分异常: " + e.getMessage());
        }
    }

}
