package com.kdsjkj.service.impl;

import com.kdsjkj.entity.AchievementRewards;
import com.kdsjkj.mapper.AchievementRewardsMapper;
import com.kdsjkj.service.IAchievementRewardsService;
import com.kdsjkj.dto.UpdateAchievementRewardRequest;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;

/**
 * <p>
 * 达标奖励表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
public class AchievementRewardsServiceImpl extends ServiceImpl<AchievementRewardsMapper, AchievementRewards> implements IAchievementRewardsService {

    @Override
    public AchievementRewards getAchievementReward() {
        log.info("开始获取达标奖励配置");
        
        // 获取唯一的配置记录
        AchievementRewards reward = this.lambdaQuery()
                .orderByDesc(AchievementRewards::getUpdatedAt)
                .last("LIMIT 1")
                .one();
        
        if (reward == null) {
            log.warn("未找到达标奖励配置，将创建默认配置");
            // 如果没有配置，创建默认配置
            reward = createDefaultReward();
        }
        
        log.info("获取达标奖励配置成功 - ID: {}", reward.getId());
        return reward;
    }
    
    @Override
    public AchievementRewards updateAchievementReward(Long id, UpdateAchievementRewardRequest request) {
        log.info("开始更新达标奖励配置 - ID: {}", id);
        
        // 1. 验证配置是否存在
        AchievementRewards reward = this.getById(id);
        if (reward == null) {
            log.error("达标奖励配置不存在 - ID: {}", id);
            throw new RuntimeException("达标奖励配置不存在");
        }
        
        // 2. 更新配置
        reward.setMinMerchantCount(request.getMinMerchantCount());
        reward.setMinMonthlyIncome(request.getMinMonthlyIncome());
        reward.setRewardAmount(request.getRewardAmount());
        reward.setUpdatedAt(LocalDateTime.now());
        
        // 3. 保存更新
        boolean success = this.updateById(reward);
        if (!success) {
            log.error("更新达标奖励配置失败 - ID: {}", id);
            throw new RuntimeException("更新达标奖励配置失败");
        }
        
        log.info("更新达标奖励配置成功 - ID: {}", id);
        return reward;
    }
    
    /**
     * 创建默认的达标奖励配置
     */
    private AchievementRewards createDefaultReward() {
        AchievementRewards reward = new AchievementRewards();
        reward.setMinMerchantCount(5);  // 默认最少5个商户
        reward.setMinMonthlyIncome(new BigDecimal("1000.00"));  // 默认每月最低收入1000元
        reward.setRewardAmount(new BigDecimal("100.00"));  // 默认奖励100元
        reward.setCreatedAt(LocalDateTime.now());
        reward.setUpdatedAt(LocalDateTime.now());
        
        boolean success = this.save(reward);
        if (!success) {
            log.error("创建默认达标奖励配置失败");
            throw new RuntimeException("创建默认达标奖励配置失败");
        }
        
        log.info("创建默认达标奖励配置成功 - ID: {}", reward.getId());
        return reward;
    }
}
