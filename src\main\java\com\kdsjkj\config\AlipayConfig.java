package com.kdsjkj.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付宝配置类
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayConfig {
    
    /**
     * 应用APPID
     */
    private String appId;
    
    /**
     * 应用私钥
     */
    private String privateKey;
    
    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;
    
    /**
     * 签名类型
     */
    private String signType = "RSA2";
    
    /**
     * 字符集
     */
    private String charset = "UTF-8";
    
    /**
     * 数据格式
     */
    private String format = "json";
    
    /**
     * 支付宝网关地址
     */
    private String gatewayUrl = "https://openapi.alipay.com/gateway.do";
    
    /**
     * 应用网关地址
     */
    private String appGatewayUrl;
    
    /**
     * 应用网关验证Token
     */
    private String appGatewayToken;
    
    /**
     * 应用网关AES密钥
     */
    private String appGatewayAesKey;
    
    /**
     * 创建支付宝客户端
     */
    @Bean
    public AlipayClient alipayClient() {
        return new DefaultAlipayClient(
            gatewayUrl,
            appId,
            privateKey,
            format,
            charset,
            alipayPublicKey,
            signType
        );
    }
}
