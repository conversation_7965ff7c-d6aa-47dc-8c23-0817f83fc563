package com.kdsjkj.appTest.转账.沙箱.单笔转账接口;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayFundTransUniTransferRequest;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.alipay.api.domain.AlipayFundTransUniTransferModel;
import com.alipay.api.domain.Participant;
import com.kdsjkj.appTest.转账.沙箱.AlipaySandboxConfig;

public class AlipayFundTransUniTransfer {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK
        AlipayClient alipayClient = new DefaultAlipayClient(AlipaySandboxConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        AlipayFundTransUniTransferModel model = new AlipayFundTransUniTransferModel();

        // 设置商家侧唯一订单号
        model.setOutBizNo("************");

        // 设置订单总金额
        model.setTransAmount("2300.00");

        // 设置描述特定的业务场景
        model.setBizScene("DIRECT_TRANSFER");

        // 设置业务产品码
        model.setProductCode("TRANS_ACCOUNT_NO_PWD");

        // 设置转账业务的标题
        model.setOrderTitle("沙箱环境测试");

        // 设置收款方信息
        Participant payeeInfo = new Participant();
        // 设置收款方支付宝用户ID
        payeeInfo.setIdentity("****************");
        payeeInfo.setIdentityType("ALIPAY_USER_ID");
        payeeInfo.setName("twhsni2312");
        model.setPayeeInfo(payeeInfo);

        // 设置业务备注
        model.setRemark("测试");

        // 设置转账业务请求的扩展参数
        model.setBusinessParams("{\"payer_show_name_use_alias\":\"true\"}");

        request.setBizModel(model);
        // 使用普通execute方法而不是certificateExecute
        AlipayFundTransUniTransferResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}