package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.dto.ServiceMarketOrderDTO;
import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 服务市场订单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/service-market-orders")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class ServiceMarketOrderController {

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IAgentService agentService;

    /**
     * 根据激活码查询订单记录
     *
     * @param activeCode 激活码
     * @return 订单记录DTO列表
     */
    @GetMapping("/by-active-code/{activeCode}")
    public Result<List<ServiceMarketOrderDTO>> getOrdersByActiveCode(@PathVariable String activeCode) {
        try {
            log.info("查询激活码[{}]的订单记录", activeCode);
            List<ServiceMarketOrderDTO> orders = serviceMarketOrderService.getOrdersByActiveCode(activeCode);
            return Result.success(orders);
        } catch (Exception e) {
            log.error("查询订单记录异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据appid设置费率
     *
     * @param appid 小程序APPID (对应consumer_miniAppId)
     * @param rate 要设置的费率
     * @return 更新结果
     */
    @PutMapping("/set-rate-by-appid")
    public Result setRateByAppid(@RequestParam("appid") String appid, 
                                @RequestParam("rate") BigDecimal rate) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            if (rate == null) {
                return Result.paramError("rate参数不能为空");
            }
            if (rate.compareTo(BigDecimal.ZERO) < 0) {
                return Result.paramError("费率不能为负数");
            }
            
            log.info("开始为appid[{}]设置费率: {}", appid, rate);

            // 查询该appid对应的订单
            List<ServiceMarketOrder> orders = serviceMarketOrderService.lambdaQuery()
                    .eq(ServiceMarketOrder::getConsumerMiniappid, appid)
                    .list();

            if (orders.isEmpty()) {
                log.warn("未找到appid[{}]对应的订单记录", appid);
                return Result.error("未找到该appid对应的订单记录");
            }

            // 批量更新费率
            int updateCount = 0;
            for (ServiceMarketOrder order : orders) {
                boolean success = serviceMarketOrderService.lambdaUpdate()
                        .eq(ServiceMarketOrder::getId, order.getId())
                        .set(ServiceMarketOrder::getRate, rate)
                        .update();
                if (success) {
                    updateCount++;
                }
            }

            log.info("appid[{}]费率设置完成，共更新{}条订单记录，费率: {}", appid, updateCount, rate);
            
            return Result.success(String.format("成功更新%d条订单记录的费率", updateCount), 
                                 updateCount);

        } catch (Exception e) {
            log.error("设置费率异常 - appid: {}, rate: {}", appid, rate, e);
            return Result.error("设置费率异常: " + e.getMessage());
        }
    }

    /**
     * 根据appid查询费率
     *
     * @param appid 小程序APPID (对应consumer_miniAppId)
     * @return 费率信息
     */
    @GetMapping("/get-rate-by-appid")
    public Result getRateByAppid(@RequestParam("appid") String appid) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            
            log.info("查询appid[{}]的费率信息", appid);

            // 查询该appid对应的订单（取最新的一条）
            ServiceMarketOrder order = serviceMarketOrderService.lambdaQuery()
                    .eq(ServiceMarketOrder::getConsumerMiniappid, appid)
                    .select(ServiceMarketOrder::getRate, ServiceMarketOrder::getConsumerMiniappid, 
                           ServiceMarketOrder::getConsumerAppname, ServiceMarketOrder::getAgentUid)
                    .orderByDesc(ServiceMarketOrder::getId)
                    .last("LIMIT 1")
                    .one();

            if (order == null) {
                log.warn("未找到appid[{}]对应的订单记录", appid);
                return Result.error("未找到该appid对应的订单记录");
            }

            // 构建返回数据
            java.util.Map<String, Object> data = new java.util.HashMap<>();
            data.put("appid", order.getConsumerMiniappid());
            data.put("appName", order.getConsumerAppname());
            data.put("rate", order.getRate());
            data.put("agentUid", order.getAgentUid());

            log.info("查询appid[{}]费率成功: {}", appid, order.getRate());
            
            return Result.success("查询成功", data);

        } catch (Exception e) {
            log.error("查询费率异常 - appid: {}", appid, e);
            return Result.error("查询费率异常: " + e.getMessage());
        }
    }

    /**
     * 根据appid查询代理费率
     * 先根据appid在service_market_order表中查agent_uid，再根据agent_uid在agent表查费率
     *
     * @param appid 小程序APPID (对应consumer_miniAppId)
     * @return 代理费率信息
     */
    @GetMapping("/get-agent-rate-by-appid")
    public Result getAgentRateByAppid(@RequestParam("appid") String appid) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            
            log.info("开始根据appid[{}]查询代理费率", appid);

            // 第一步：根据appid查询对应的agent_uid
            String agentUid = serviceMarketOrderService.getAgentUidByAppid(appid);
            if (agentUid == null) {
                log.warn("未找到appid[{}]对应的订单记录或agent_uid为空", appid);
                return Result.error("未找到该appid对应的代理信息");
            }

            log.info("appid[{}]对应的agent_uid: {}", appid, agentUid);

            // 第二步：根据agent_uid查询代理费率
            BigDecimal agentRate = agentService.getAgentRateByUid(agentUid);
            if (agentRate == null) {
                log.warn("未找到agent_uid[{}]对应的代理费率", agentUid);
                return Result.error("未找到该代理的费率信息");
            }

            // 构建返回数据
            java.util.Map<String, Object> data = new java.util.HashMap<>();
            data.put("appid", appid);
            data.put("agentUid", agentUid);
            data.put("agentRate", agentRate);

            log.info("查询代理费率成功 - appid[{}], agentUid[{}], rate[{}]", appid, agentUid, agentRate);
            
            return Result.success("查询代理费率成功", data);

        } catch (Exception e) {
            log.error("查询代理费率异常 - appid: {}", appid, e);
            return Result.error("查询代理费率异常: " + e.getMessage());
        }
    }
}
