package com.kdsjkj.appTest.xiaochengxu.C提审.小程序退回开发;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.response.AlipayOpenMiniVersionAuditedCancelResponse;
import com.alipay.api.request.AlipayOpenMiniVersionAuditedCancelRequest;
import com.alipay.api.domain.AlipayOpenMiniVersionAuditedCancelModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

//退回开发
public class AlipayOpenMiniVersionAuditedCancel {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniVersionAuditedCancelRequest request = new AlipayOpenMiniVersionAuditedCancelRequest();
        AlipayOpenMiniVersionAuditedCancelModel model = new AlipayOpenMiniVersionAuditedCancelModel();
        
        // 设置小程序版本
        model.setAppVersion("0.0.1");
        
        // 设置小程序端
        model.setBundleId("com.alipay.alipaywallet");
        
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenMiniVersionAuditedCancelResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }

    // 移除原有的getAlipayConfig方法，改为使用统一配置类
}