<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.ActivationRewardsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.ActivationRewards">
        <id column="id" property="id" />
        <result column="min_payment_amount" property="minPaymentAmount" />
        <result column="reward_amount" property="rewardAmount" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, min_payment_amount, reward_amount, created_at, updated_at
    </sql>

</mapper>
