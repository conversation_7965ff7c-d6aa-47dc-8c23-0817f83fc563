package com.kdsjkj.service;

import com.kdsjkj.entity.ActivationRewards;
import com.kdsjkj.dto.UpdateActivationRewardRequest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 激活奖励表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface IActivationRewardsService extends IService<ActivationRewards> {
    
    /**
     * 获取激活奖励配置
     * 
     * @return 激活奖励配置
     */
    ActivationRewards getActivationReward();
    
    /**
     * 更新激活奖励配置
     * 
     * @param id 配置ID
     * @param request 更新请求
     * @return 更新后的配置
     */
    ActivationRewards updateActivationReward(Long id, UpdateActivationRewardRequest request);
}
