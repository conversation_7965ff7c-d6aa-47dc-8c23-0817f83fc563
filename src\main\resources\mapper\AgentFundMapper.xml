<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.AgentFundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.AgentFund">
        <id column="id" property="id" />
        <result column="agent_uid" property="agentUid" />
        <result column="amount" property="amount" />
        <result column="obtain_type" property="obtainType" />
        <result column="obtain_time" property="obtainTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, agent_uid, amount, obtain_type, obtain_time
    </sql>

</mapper>
