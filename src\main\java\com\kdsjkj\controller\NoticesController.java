package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.Notices;
import com.kdsjkj.service.INoticesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 公告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/notices")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class NoticesController {

    @Autowired
    private INoticesService noticesService;

    /**
     * 新增公告
     * 
     * @param notices 公告信息
     * @return 创建结果
     */
    @PostMapping("/add")
    public Result<Notices> addNotice(@Valid @RequestBody Notices notices) {
        try {
            log.info("收到新增公告请求，标题：{}", notices.getTitle());
            
            // 设置创建时间和发布时间
            LocalDateTime now = LocalDateTime.now();
            notices.setCreatedAt(now);
            if (notices.getPublishTime() == null) {
                notices.setPublishTime(now);
            }
            
            // 保存公告
            boolean success = noticesService.save(notices);
            if (success) {
                log.info("公告新增成功，ID：{}", notices.getId());
                return Result.success("公告新增成功", notices);
            } else {
                return Result.error("公告新增失败");
            }
            
        } catch (Exception e) {
            log.error("新增公告系统异常", e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 查询公告列表
     * 
     * @return 公告列表
     */
    @GetMapping("/list")
    public Result<List<Notices>> getNoticeList() {
        try {
            log.info("查询公告列表");
            
            List<Notices> noticesList = noticesService.lambdaQuery()
                    .orderByDesc(Notices::getPublishTime)
                    .orderByDesc(Notices::getCreatedAt)
                    .list();
            
            log.info("查询公告列表成功，共{}条记录", noticesList.size());
            return Result.success("查询成功", noticesList);
            
        } catch (Exception e) {
            log.error("查询公告列表系统异常", e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询公告详情
     * 
     * @param id 公告ID
     * @return 公告详情
     */
    @GetMapping("/{id}")
    public Result<Notices> getNoticeById(@PathVariable Integer id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("公告ID不能为空");
            }
            
            log.info("查询公告详情，ID：{}", id);
            
            Notices notice = noticesService.getById(id);
            if (notice != null) {
                log.info("查询公告详情成功，ID：{}", id);
                return Result.success("查询成功", notice);
            } else {
                return Result.notFound("公告不存在");
            }
            
        } catch (Exception e) {
            log.error("查询公告详情系统异常，ID：{}", id, e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 更新公告
     * 
     * @param id 公告ID
     * @param notices 更新的公告信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Result<Notices> updateNotice(@PathVariable Integer id, @Valid @RequestBody Notices notices) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("公告ID不能为空");
            }
            
            log.info("更新公告，ID：{}", id);
            
            // 检查公告是否存在
            Notices existingNotice = noticesService.getById(id);
            if (existingNotice == null) {
                return Result.notFound("公告不存在");
            }
            
            // 设置ID和更新时间
            notices.setId(id);
            notices.setCreatedAt(existingNotice.getCreatedAt()); // 保持原创建时间
            
            // 保存更新
            boolean success = noticesService.updateById(notices);
            if (success) {
                log.info("公告更新成功，ID：{}", id);
                return Result.success("公告更新成功", notices);
            } else {
                return Result.error("公告更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新公告系统异常，ID：{}", id, e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 删除公告
     * 
     * @param id 公告ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteNotice(@PathVariable Integer id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("公告ID不能为空");
            }
            
            log.info("删除公告，ID：{}", id);
            
            // 检查公告是否存在
            Notices existingNotice = noticesService.getById(id);
            if (existingNotice == null) {
                return Result.notFound("公告不存在");
            }
            
            // 删除公告
            boolean success = noticesService.removeById(id);
            if (success) {
                log.info("公告删除成功，ID：{}", id);
                return Result.success("公告删除成功");
            } else {
                return Result.error("公告删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除公告系统异常，ID：{}", id, e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }
}
