package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.dto.AddAgentRequest;
import com.kdsjkj.dto.AddTopAgentRequest;
import com.kdsjkj.dto.UpdateAgentRequest;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.service.IAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 代理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/agent")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class AgentController {
    
    private final IAgentService agentService;

    @Autowired
    public AgentController(IAgentService agentService) {
        this.agentService = agentService;
    }

    /**
     * 添加代理人
     * 
     * @param request 添加代理人请求
     * @return 创建结果
     */
    @PostMapping("/add")
    public Result<Agent> addAgent(@Valid @RequestBody AddAgentRequest request) {
        try {
            log.info("收到添加代理人请求，姓名：{}, 手机号：{}, UID：{}, 费率：{}, 上级代理：{}", 
                    request.getName(), request.getPhone(), request.getUid(), 
                    request.getRate(), request.getParentUid());
            
            Agent agent = agentService.createAgent(request);
            
            log.info("代理人添加成功，UID：{}", agent.getUid());
            return Result.success("代理人添加成功", agent);
            
        } catch (RuntimeException e) {
            log.warn("添加代理人失败：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("添加代理人系统异常", e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 查询指定代理下的所有子代理
     * 
     * @param parentUid 上级代理UID
     * @return 子代理列表
     */
    @GetMapping("/sub-agents/{parentUid}")
    public Result<List<Agent>> getSubAgents(@PathVariable String parentUid) {
        try {
            log.info("收到查询子代理请求，上级代理UID：{}", parentUid);
            
            List<Agent> subAgents = agentService.getSubAgents(parentUid);
            
            log.info("查询子代理成功，上级代理UID：{}，子代理数量：{}", parentUid, subAgents.size());
            return Result.success("查询子代理成功", subAgents);
            
        } catch (RuntimeException e) {
            log.warn("查询子代理失败：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询子代理系统异常", e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }
    
    /**
     * 修改代理人信息
     * 
     * @param uid 代理人UID
     * @param request 修改信息请求
     * @return 修改结果
     */
    @PutMapping("/{uid}")
    public Result<Agent> updateAgent(@PathVariable String uid, @Valid @RequestBody UpdateAgentRequest request) {
        try {
            log.info("收到修改代理人请求，UID：{}，新姓名：{}，新手机号：{}，新费率：{}", 
                    uid, request.getName(), request.getPhone(), request.getRate());
            
            Agent agent = agentService.updateAgent(uid, request);
            
            log.info("代理人信息修改成功，UID：{}", uid);
            return Result.success("代理人信息修改成功", agent);
            
        } catch (RuntimeException e) {
            log.warn("修改代理人信息失败：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改代理人信息系统异常", e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 创建顶级代理人
     * 
     * @param request 添加顶级代理人请求
     * @return 创建结果
     */
    @PostMapping("/top")
    public Result<Agent> addTopAgent(@Valid @RequestBody AddTopAgentRequest request) {
        try {
            log.info("收到添加顶级代理人请求，姓名：{}，手机号：{}，UID：{}", 
                    request.getName(), request.getPhone(), request.getUid());
            
            Agent agent = agentService.createTopAgent(request);
            
            log.info("顶级代理人添加成功，UID：{}", agent.getUid());
            return Result.success("顶级代理人添加成功", agent);
            
        } catch (RuntimeException e) {
            log.warn("添加顶级代理人失败：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("添加顶级代理人系统异常", e);
            return Result.error("系统异常：" + e.getMessage());
        }
    }
}
