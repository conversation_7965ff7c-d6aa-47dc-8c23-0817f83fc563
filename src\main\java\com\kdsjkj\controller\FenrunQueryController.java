package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.FenrunQuery;
import com.kdsjkj.service.IFenrunQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 分账查询表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@RestController
@RequestMapping("/fenrun-query")
@CrossOrigin(origins = "*", allowedHeaders = "*")  // 允许跨域访问
public class FenrunQueryController {

    @Autowired
    private IFenrunQueryService fenrunQueryService;

    /**
     * 根据appid查询分账绑定记录
     * 
     * @param appid 小程序APPID
     * @return 查询结果
     */
    @GetMapping("/checkBind")
    public Result checkBindRecord(@RequestParam("appid") String appid) {
        
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }

            log.info("查询分账绑定记录 - appid: {}", appid);

            // 查询数据库中是否有该appid的记录
            FenrunQuery existingQuery = fenrunQueryService.lambdaQuery()
                    .eq(FenrunQuery::getAppid, appid)
                    .one();
            
            if (existingQuery != null) {
                log.info("找到分账绑定记录 - appid: {}, outRequestNo: {}", appid, existingQuery.getOutRequestNo());
                return Result.success("该appid已绑定分账关系", existingQuery);
            } else {
                log.info("未找到分账绑定记录 - appid: {}", appid);
                return Result.error("请绑分账关系还没绑定，请联系代理绑定");
            }

        } catch (Exception e) {
            log.error("查询分账绑定记录异常 - appid: {}", appid, e);
            return Result.error("查询分账绑定记录异常: " + e.getMessage());
        }
    }

}
