package com.kdsjkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kdsjkj.entity.AlipayConfigBackend;
import com.kdsjkj.mapper.AlipayConfigBackendMapper;
import com.kdsjkj.service.IAlipayConfigBackendService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 后台支付宝配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class AlipayConfigBackendServiceImpl extends ServiceImpl<AlipayConfigBackendMapper, AlipayConfigBackend> implements IAlipayConfigBackendService {

    /**
     * 获取最新的支付宝配置
     * @return 支付宝配置
     */
    @Override
    public AlipayConfigBackend  getLatestConfig() {
        QueryWrapper<AlipayConfigBackend> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("id").last("LIMIT 1");
        return this.getOne(queryWrapper);
    }
}
