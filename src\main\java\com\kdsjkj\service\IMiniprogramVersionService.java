package com.kdsjkj.service;

import com.kdsjkj.entity.MiniprogramVersion;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface IMiniprogramVersionService extends IService<MiniprogramVersion> {
    
    /**
     * 获取小程序的下一个版本号
     * 如果存在记录则version+1，不存在则创建新记录version=1
     * @param appid 小程序APPID
     * @return 格式化的版本号字符串 (0.0.version)
     */
    String getNextVersionAndIncrement(String appid);

    String getVersionByAppid(String appid);
    
    /**
     * 保存或更新小程序版本信息
     * @param appid 小程序APPID
     * @param version 版本号
     * @param status 状态
     * @return 是否保存成功
     */
    boolean saveVersionInfo(String appid, String version, String status);

    int getValidMerchantCountByAppIdsAndStatus(List<String> merchantAppIds, String 已上架);

    /**
     * 根据appid列表和状态获取有效商户个数
     * @param appIds 小程序appid列表
     * @param status 状态（如"已上架"）
     * @return 有效商户个数
     */
  
}
