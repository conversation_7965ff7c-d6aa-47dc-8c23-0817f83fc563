<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.AlipayConfigBackendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.AlipayConfigBackend">
        <id column="id" property="id" />
        <result column="server_url" property="serverUrl" />
        <result column="app_id" property="appId" />
        <result column="private_key" property="privateKey" />
        <result column="alipay_public_key" property="alipayPublicKey" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, server_url, app_id, private_key, alipay_public_key
    </sql>

</mapper>
