package com.kdsjkj.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 支付宝授权数据模型（不使用JPA）
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class AlipayAuthData {
    
    /**
     * 通知ID
     */
    private String notifyId;
    
    /**
     * 第三方应用APPID
     */
    private String appId;
    
    /**
     * 授权小程序APPID
     */
    private String authAppId;
    
    /**
     * 授权令牌
     */
    private String appAuthToken;
    
    /**
     * 刷新令牌
     */
    private String appRefreshToken;
    
    /**
     * 授权码
     */
    private String appAuthCode;
    
    /**
     * 授权商家用户ID
     */
    private String userId;
    
    /**
     * 授权时间
     */
    private Long authTime;
    
    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;
    
    /**
     * 刷新令牌过期时间（秒）
     */
    private Long reExpiresIn;
    
    /**
     * 授权触发者
     */
    private String triggerType;
    
    /**
     * 通知状态
     */
    private String notifyStatus;
    
    /**
     * 业务状态
     */
    private String bizStatus;
    
    /**
     * 原始通知内容
     */
    private String rawNotifyData;
    
    /**
     * 处理状态：SUCCESS-成功，FAILED-失败，PROCESSING-处理中
     */
    private String processStatus;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 构造方法
     */
    public AlipayAuthData() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 创建授权数据的便捷构造方法
     */
    public AlipayAuthData(String notifyId, String appId, String authAppId) {
        this();
        this.notifyId = notifyId;
        this.appId = appId;
        this.authAppId = authAppId;
        this.processStatus = "PROCESSING";
    }
    
    /**
     * 格式化输出授权数据
     */
    public String toFormattedString() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 支付宝授权数据 ===\n");
        sb.append("通知ID: ").append(notifyId).append("\n");
        sb.append("第三方应用APPID: ").append(appId).append("\n");
        sb.append("授权小程序APPID: ").append(authAppId).append("\n");
        sb.append("授权令牌: ").append(appAuthToken != null ? appAuthToken.substring(0, Math.min(20, appAuthToken.length())) + "..." : "null").append("\n");
        sb.append("用户ID: ").append(userId).append("\n");
        sb.append("授权时间: ").append(authTime).append("\n");
        sb.append("令牌过期时间: ").append(expiresIn).append("秒\n");
        sb.append("处理状态: ").append(processStatus).append("\n");
        sb.append("业务状态: ").append(bizStatus).append("\n");
        sb.append("触发类型: ").append(triggerType).append("\n");
        sb.append("创建时间: ").append(createTime).append("\n");
        if (errorMsg != null) {
            sb.append("错误信息: ").append(errorMsg).append("\n");
        }
        sb.append("原始数据: ").append(rawNotifyData).append("\n");
        sb.append("========================\n");
        return sb.toString();
    }
} 