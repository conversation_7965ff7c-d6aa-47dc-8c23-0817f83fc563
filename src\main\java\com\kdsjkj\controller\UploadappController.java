package com.kdsjkj.controller;


import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayOpenMiniVersionUploadModel;
import com.alipay.api.request.AlipayOpenMiniVersionUploadRequest;
import com.alipay.api.response.AlipayOpenMiniVersionUploadResponse;
import com.alipay.api.request.AlipayOpenAppinfoModifyRequest;
import com.alipay.api.response.AlipayOpenAppinfoModifyResponse;
import com.alipay.api.domain.AlipayOpenAppinfoModifyModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;
import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.entity.Uploadapp;
import com.kdsjkj.service.IAlipayConfigFrontService;
import com.kdsjkj.service.IMiniprogramVersionService;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IUploadappService;
import com.kdsjkj.service.AlipayAuthRecordService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;

import org.springframework.web.bind.annotation.RestController;
import com.alipay.api.request.AlipayOpenAppinfoQueryRequest;
import com.alipay.api.response.AlipayOpenAppinfoQueryResponse;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 支付宝小程序上传表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@EnableAsync
@RestController
@RequestMapping("/uploadapp")
public class UploadappController {
    private static final Logger logger = LoggerFactory.getLogger(UploadappController.class);

    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;
    
    @Autowired
    private IUploadappService uploadappService;
    
    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;
    
    @Autowired
    private IMiniprogramVersionService miniprogramVersionService;
    
    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;

    /**
     * 获取上传应用信息的示例接口
     * @param appid 小程序APPID
     * @param version 版本号 (格式: 0.0.%d)
     * @return 返回Result对象，包含上传应用信息
     */
    @RequestMapping("/get")
    public Result getUploadAppInfo(String appid, String version) throws AlipayApiException {
        // 参数校验
        if (appid == null || appid.trim().isEmpty()) {
            return Result.paramError("appid参数不能为空");
        }
        
        if (version == null || version.trim().isEmpty()) {
            return Result.paramError("version参数不能为空");
        }

        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 从数据库获取上传参数
        Uploadapp uploadapp = uploadappService.lambdaQuery()
                .last("LIMIT 1")
                .one();
        
        if (uploadapp == null) {
            return Result.error("未找到小程序上传配置信息");
        }

        // 根据appid从alipay_auth_record表中查询app_auth_token
        String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
        
        if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
            return Result.error("未找到对应appid的授权令牌信息");
        }

        try {
        // 构造请求参数以调用接口
        AlipayOpenMiniVersionUploadRequest request = new AlipayOpenMiniVersionUploadRequest();
        AlipayOpenMiniVersionUploadModel model = new AlipayOpenMiniVersionUploadModel();
        
        // 设置小程序模板版本号 - 从数据库获取
//            model.setTemplateVersion(uploadapp.getTemplateVersion());
        
        // 设置小程序模板 APPID - 从数据库获取
        model.setTemplateId(uploadapp.getTemplateId());
            // 从数据库读取ext配置模板，并动态替换appid
            String extTemplate = uploadapp.getExt();
            if (extTemplate != null && !extTemplate.trim().isEmpty()) {
                // 将模板中的占位符替换为实际的appid
                String dynamicExt = extTemplate.replace("${appid}", appid);
                logger.info("使用数据库配置的ext模板: {}", extTemplate);
                logger.info("替换appid后的ext配置: {}", dynamicExt);
                model.setExt(dynamicExt);
            } else {
                // 如果数据库中没有配置，使用默认配置
                String defaultExt = "{\"extEnable\": true, \"ext\": {\"appid\": \"" + appid + "\"}, \"appid\": \"" + appid + "\"}";
                logger.info("数据库中未找到ext配置，使用默认配置: {}", defaultExt);
                model.setExt(defaultExt);
            }
            // 设置商家小程序版本号
        model.setAppVersion(version);
            // 设置小程序模板版本号
            model.setTemplateVersion(uploadapp.getTemplateVersion());
        
        // 设置小程序投放的端参数 - 从数据库获取
        model.setBundleId(uploadapp.getBundleId());

        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 从service_market_order表查询获取
        request.putOtherTextParam("app_auth_token", appAuthToken);
            System.out.println("------------------");
            System.out.println("开始打印app_auth_token");
            System.out.println(appAuthToken);
            // 执行上传操作
            AlipayOpenMiniVersionUploadResponse response = alipayClient.execute(request);
            System.out.println("------------------");
            System.out.println(response);
            
            if (response.isSuccess()) {
                // 上传成功，保存版本信息到数据库
                boolean saveResult = miniprogramVersionService.saveVersionInfo(appid, version, "上传成功");
                
                if (saveResult) {
                    // 异步调用修改OpenID配置（30秒后执行）
                    asyncModifyOpenIdConfig(appid);
                    return Result.success("小程序上传成功，版本信息已保存", response.getBody());
                } else {
                    return Result.success("小程序上传成功，但版本信息保存失败", response.getBody());
                }
            } else {
                // 上传失败，仍然保存版本信息但状态为失败
                miniprogramVersionService.saveVersionInfo(appid, version, "上传失败");
                return Result.error("小程序上传失败: " + response.getMsg() + " - " + response.getSubMsg());
            }
            
        } catch (AlipayApiException e) {
            // 异常情况，保存版本信息但状态为异常
            miniprogramVersionService.saveVersionInfo(appid, version, "上传异常");
            e.printStackTrace();
            return Result.error("上传过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 异步执行OpenID配置修改
     * 等待30秒后执行，避免配置未生效
     */
    @Async
    public void asyncModifyOpenIdConfig(String appid) {
        try {
            logger.info("准备执行OpenID配置修改，等待30秒...");
            // 等待30秒
            TimeUnit.SECONDS.sleep(30);
            
            logger.info("开始执行OpenID配置修改");
            Result result = checkAndModifyOpenIdConfig(appid);
            
            if (result.getCode() == 200) {
                logger.info("OpenID配置修改成功: {}", result.getMessage());
            } else {
                logger.error("OpenID配置修改失败: {}", result.getMessage());
            }
        } catch (InterruptedException e) {
            logger.error("OpenID配置修改异步执行被中断", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("OpenID配置修改异步执行异常", e);
        }
    }

    /**
     * 查询并按需修改商户小程序的用户标识获取方式
     * @param appid 小程序appid
     * @return 修改结果
     */
    @PostMapping("/checkAndModifyOpenIdConfig")
    public Result checkAndModifyOpenIdConfig(@RequestParam("appid") String appid) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }

            // 1. 先查询当前配置
            String currentConfig = queryOpenIdConfig(appid);
            if (currentConfig == null) {
                return Result.error("查询OpenID配置失败");
            }

            logger.info("当前OpenID配置: {}", currentConfig);

            // 2. 如果已经是USER_ID，则不需要修改
            if ("USER_ID".equals(currentConfig)) {
                return Result.success("当前已经是USER_ID配置，无需修改");
            }

            // 3. 如果不是USER_ID，则进行修改
            return modifyOpenIdConfig(appid);

        } catch (Exception e) {
            logger.error("检查并修改OpenID配置异常", e);
            return Result.error("检查并修改OpenID配置异常: " + e.getMessage());
        }
    }

    /**
     * 查询商户小程序的用户标识获取方式
     * @param appid 小程序appid
     * @return 当前的OpenID配置，如果查询失败返回null
     */
    private String queryOpenIdConfig(String appid) throws AlipayApiException {
        // 初始化SDK
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenAppinfoQueryRequest request = new AlipayOpenAppinfoQueryRequest();
        
        // 获取app_auth_token
        String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
        if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
            logger.error("未找到对应appid的授权令牌信息");
            return null;
        }

        // 设置第三方应用授权令牌
        request.putOtherTextParam("app_auth_token", appAuthToken);

        AlipayOpenAppinfoQueryResponse response = alipayClient.execute(request);
        logger.info("查询OpenID配置响应: {}", response.getBody());

        if (response.isSuccess()) {
            return response.getOpenIdConfig();
        } else {
            logger.error("查询OpenID配置失败: {}", response.getSubMsg());
            return null;
        }
    }

    /**
     * 修改商户小程序的用户标识获取方式为USER_ID
     * @param appid 小程序appid
     * @return 修改结果
     */
    private Result modifyOpenIdConfig(String appid) {
        try {
            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数以调用接口
            AlipayOpenAppinfoModifyRequest request = new AlipayOpenAppinfoModifyRequest();
            AlipayOpenAppinfoModifyModel model = new AlipayOpenAppinfoModifyModel();
            
            // 设置应用openid配置为USER_ID
            model.setOpenIdConfig("USER_ID");
            
            request.setBizModel(model);

            // 获取app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appid的授权令牌信息");
            }

            // 设置第三方应用授权令牌
            request.putOtherTextParam("app_auth_token", appAuthToken);

            AlipayOpenAppinfoModifyResponse response = alipayClient.execute(request);
            logger.info("修改OpenID配置响应: {}", response.getBody());

            if (response.isSuccess()) {
                return Result.success("OpenID配置修改成功", response.getBody());
            } else {
                return Result.error("OpenID配置修改失败: " + response.getSubMsg());
            }
        } catch (Exception e) {
            logger.error("修改OpenID配置异常", e);
            return Result.error("修改OpenID配置异常: " + e.getMessage());
        }
    }





















        private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();
        
        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }

}
