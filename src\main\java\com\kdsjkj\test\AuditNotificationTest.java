package com.kdsjkj.test;

import com.alibaba.fastjson.JSON;
import java.util.Map;

/**
 * Audit notification test
 */
public class AuditNotificationTest {
    
    /**
     * Extract version number from version string
     */
    private static int extractVersionNumber(String version) {
        if (version == null || version.trim().isEmpty()) {
            return 0;
        }
        
        int lastDotIndex = version.lastIndexOf('.');
        if (lastDotIndex == -1) {
            try {
                return Integer.parseInt(version.trim());
            } catch (NumberFormatException e) {
                System.out.println("Cannot parse version: " + version);
                return 0;
            }
        }
        
        String lastPart = version.substring(lastDotIndex + 1);
        try {
            return Integer.parseInt(lastPart.trim());
        } catch (NumberFormatException e) {
            System.out.println("Cannot parse last part: " + lastPart);
            return 0;
        }
    }
    
    public static void main(String[] args) {
        // Test audit passed notification
        String passedBizContent = "{\"mini_app_id\":\"2021005177614267\",\"bundle_id\":\"com.alipay.alipaywallet\",\"mini_app_version\":\"0.0.5\"}";
        
        // Test audit rejected notification  
        String rejectedBizContent = "{\"mini_app_id\":\"2021005175641065\",\"bundle_id\":\"com.alipay.alipaywallet\",\"base_audit_reason\":{\"audit_images\":[],\"memos\":[{\"memo\":\"该小程序与您名下已有小程序：只能助手或其他小程序，存在同质化问题\",\"memo_image_list\":[]}]},\"audit_reason\":\"审核被拒绝的原因\",\"base_audit\":\"REJECT\",\"mini_app_version\":\"0.0.1\"}";
        
        System.out.println("=== Audit Notification Test ===");
        
        // Test passed notification
        System.out.println("\n1. Testing audit passed notification:");
        try {
            Map<String, Object> passedData = JSON.parseObject(passedBizContent, Map.class);
            String appId = (String) passedData.get("mini_app_id");
            String version = (String) passedData.get("mini_app_version");
            int versionNumber = extractVersionNumber(version);
            
            System.out.println("AppId: " + appId);
            System.out.println("Version: " + version);
            System.out.println("Version Number: " + versionNumber);
            System.out.println("Status: 已上架");
        } catch (Exception e) {
            System.out.println("Error parsing passed notification: " + e.getMessage());
        }
        
        // Test rejected notification
        System.out.println("\n2. Testing audit rejected notification:");
        try {
            Map<String, Object> rejectedData = JSON.parseObject(rejectedBizContent, Map.class);
            String appId = (String) rejectedData.get("mini_app_id");
            String version = (String) rejectedData.get("mini_app_version");
            String reason = (String) rejectedData.get("audit_reason");
            int versionNumber = extractVersionNumber(version);
            
            System.out.println("AppId: " + appId);
            System.out.println("Version: " + version);
            System.out.println("Version Number: " + versionNumber);
            System.out.println("Reason: " + reason);
            System.out.println("Status: 审核未通过");
        } catch (Exception e) {
            System.out.println("Error parsing rejected notification: " + e.getMessage());
        }
        
        System.out.println("\nTest completed!");
    }
}
