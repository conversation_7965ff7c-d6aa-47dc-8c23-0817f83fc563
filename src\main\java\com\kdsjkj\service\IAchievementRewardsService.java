package com.kdsjkj.service;

import com.kdsjkj.entity.AchievementRewards;
import com.kdsjkj.dto.UpdateAchievementRewardRequest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 达标奖励表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface IAchievementRewardsService extends IService<AchievementRewards> {
    
    /**
     * 获取达标奖励配置
     * 
     * @return 达标奖励配置
     */
    AchievementRewards getAchievementReward();
    
    /**
     * 更新达标奖励配置
     * 
     * @param id 配置ID
     * @param request 更新请求
     * @return 更新后的配置
     */
    AchievementRewards updateAchievementReward(Long id, UpdateAchievementRewardRequest request);
}
