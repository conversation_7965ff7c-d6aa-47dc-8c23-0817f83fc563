package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 前端支付宝配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("alipay_config_front")
public class AlipayConfigFront implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付宝网关地址
     */
    @TableField("server_url")
    private String serverUrl;

    /**
     * 应用ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 数据格式
     */
    private String format;

    /**
     * 字符编码
     */
    private String charset;

    /**
     * 签名类型
     */
    @TableField("sign_type")
    private String signType;

    /**
     * 应用私钥
     */
    @TableField("private_key")
    private String privateKey;

    /**
     * 支付宝公钥
     */
    @TableField("alipay_public_key")
    private String alipayPublicKey;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
