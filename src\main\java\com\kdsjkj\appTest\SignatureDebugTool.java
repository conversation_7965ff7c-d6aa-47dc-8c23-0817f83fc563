package com.kdsjkj.appTest;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.kdsjkj.config.AlipayMiniProgramConfig;

/**
 * 签名验证调试工具
 * 用于诊断和解决签名验证失败的问题
 */
public class SignatureDebugTool {

    public static void main(String[] args) {
        System.out.println("=== 支付宝签名验证调试工具 ===");
        
        SignatureDebugTool tool = new SignatureDebugTool();
        tool.debugSignature();
    }

    /**
     * 调试签名配置
     */
    public void debugSignature() {
        try {
            System.out.println("\n🔍 检查签名配置...");
            
            // 1. 检查配置信息
            checkConfiguration();
            
            // 2. 测试签名验证
            testSignature();
            
        } catch (Exception e) {
            System.err.println("❌ 调试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查配置信息
     */
    private void checkConfiguration() {
        System.out.println("\n📋 当前配置信息:");
        System.out.println("======================================");
        
        AlipayConfig config = AlipayMiniProgramConfig.getAlipayConfig();
        String appAuthToken = AlipayMiniProgramConfig.getAppAuthToken();
        
        System.out.println("应用ID: " + config.getAppId());
        System.out.println("网关地址: " + config.getServerUrl());
        System.out.println("签名类型: " + config.getSignType());
        System.out.println("字符编码: " + config.getCharset());
        System.out.println("数据格式: " + config.getFormat());
        System.out.println("app_auth_token: " + appAuthToken);
        
        // 检查密钥配置
        String privateKey = config.getPrivateKey();
        String alipayPublicKey = config.getAlipayPublicKey();
        
        System.out.println("\n🔐 密钥配置检查:");
        System.out.println("应用私钥: " + (privateKey != null ? "✅ 已配置 (长度: " + privateKey.length() + ")" : "❌ 未配置"));
        System.out.println("支付宝公钥: " + (alipayPublicKey != null ? "✅ 已配置 (长度: " + alipayPublicKey.length() + ")" : "❌ 未配置"));
        
        // 检查密钥格式
        if (privateKey != null) {
            if (privateKey.startsWith("-----BEGIN") || privateKey.startsWith("MII")) {
                System.out.println("应用私钥格式: ✅ 格式正确");
            } else {
                System.out.println("应用私钥格式: ❌ 格式可能有问题");
            }
        }
        
        if (alipayPublicKey != null) {
            if (alipayPublicKey.startsWith("-----BEGIN") || alipayPublicKey.startsWith("MII")) {
                System.out.println("支付宝公钥格式: ✅ 格式正确");
            } else {
                System.out.println("支付宝公钥格式: ❌ 格式可能有问题");
            }
        }
        
        System.out.println("======================================");
    }

    /**
     * 测试签名验证
     */
    private void testSignature() {
        System.out.println("\n🧪 测试签名验证...");
        System.out.println("======================================");
        
        try {
            // 使用一个测试用的授权码（这个会失败，但可以看到具体的错误信息）
            String testAuthCode = "test_auth_code_for_signature_debug";
            
            AlipayConfig config = AlipayMiniProgramConfig.getAlipayConfig();
            AlipayClient alipayClient = new DefaultAlipayClient(config);
            
            AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
            request.setCode(testAuthCode);
            request.setGrantType("authorization_code");
            request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
            
            System.out.println("发送测试请求...");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
            
            System.out.println("响应结果:");
            System.out.println("- 是否成功: " + response.isSuccess());
            System.out.println("- 错误码: " + response.getCode());
            System.out.println("- 错误信息: " + response.getMsg());
            System.out.println("- 子错误码: " + response.getSubCode());
            System.out.println("- 子错误信息: " + response.getSubMsg());
            
            // 分析错误类型
            analyzeError(response);
            
        } catch (AlipayApiException e) {
            System.out.println("API调用异常: " + e.getMessage());
            
            if (e.getMessage().contains("sign check fail")) {
                System.out.println("\n❌ 签名验证失败！");
                provideSignatureSolutions();
            } else if (e.getMessage().contains("invalid_grant")) {
                System.out.println("\n✅ 签名验证通过，但授权码无效（这是预期的）");
                System.out.println("说明签名配置正确，问题可能在于:");
                System.out.println("1. 授权码已过期或已使用");
                System.out.println("2. app_auth_token 无效");
                System.out.println("3. 小程序关联绑定问题");
            } else {
                System.out.println("\n❓ 其他类型的错误: " + e.getMessage());
            }
        }
        
        System.out.println("======================================");
    }

    /**
     * 分析错误
     */
    private void analyzeError(AlipaySystemOauthTokenResponse response) {
        System.out.println("\n🔍 错误分析:");
        
        String errorCode = response.getCode();
        String errorMsg = response.getMsg();
        String subCode = response.getSubCode();
        String subMsg = response.getSubMsg();
        
        if ("40004".equals(errorCode)) {
            System.out.println("业务处理失败:");
            if ("invalid_grant".equals(subCode)) {
                System.out.println("✅ 签名验证通过，但授权授权无效");
                System.out.println("这说明签名配置是正确的！");
            } else if ("isv.invalid-app-auth-token".equals(subCode)) {
                System.out.println("❌ app_auth_token 无效");
                System.out.println("需要重新获取授权令牌");
            } else {
                System.out.println("其他业务错误: " + subMsg);
            }
        } else if ("20000".equals(errorCode)) {
            System.out.println("系统繁忙，建议稍后重试");
        } else {
            System.out.println("未知错误: " + errorMsg);
        }
    }

    /**
     * 提供签名问题解决方案
     */
    private void provideSignatureSolutions() {
        System.out.println("\n💡 签名验证失败解决方案:");
        System.out.println("======================================");
        
        System.out.println("1. 检查应用私钥:");
        System.out.println("   - 确保私钥完整，没有缺少字符");
        System.out.println("   - 私钥格式应该是 PKCS#8 格式");
        System.out.println("   - 私钥应该以 MII 开头或包含 BEGIN PRIVATE KEY");
        
        System.out.println("\n2. 检查支付宝公钥:");
        System.out.println("   - 使用支付宝提供的公钥，不是自己生成的");
        System.out.println("   - 公钥应该以 MII 开头或包含 BEGIN PUBLIC KEY");
        
        System.out.println("\n3. 检查签名类型:");
        System.out.println("   - 确保使用 RSA2 签名类型");
        System.out.println("   - 与支付宝开放平台配置保持一致");
        
        System.out.println("\n4. 检查字符编码:");
        System.out.println("   - 使用 UTF-8 编码");
        System.out.println("   - 确保没有特殊字符问题");
        
        System.out.println("\n5. 检查小程序关联:");
        System.out.println("   - 确保小程序已正确关联到第三方应用");
        System.out.println("   - 确保继承了第三方应用的签名配置");
        System.out.println("   - 重新构建小程序以更新配置");
        
        System.out.println("\n6. 检查 app_auth_token:");
        System.out.println("   - 确保 token 未过期");
        System.out.println("   - 确保 token 具有足够的权限");
        
        System.out.println("======================================");
    }
} 