package com.kdsjkj.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kdsjkj.constant.Result;
import com.kdsjkj.dto.EcommerceActivationCodeRequest;
import com.kdsjkj.entity.EcommerceActivationCodes;
import com.kdsjkj.service.IEcommerceActivationCodesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 电商激活码表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/activation-codes")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class EcommerceActivationCodesController {

    private final IEcommerceActivationCodesService activationCodesService;

    @Autowired
    public EcommerceActivationCodesController(IEcommerceActivationCodesService activationCodesService) {
        this.activationCodesService = activationCodesService;
    }

    /**
     * 添加激活码
     */
    @PostMapping("/add")
    public Result<EcommerceActivationCodes> addActivationCode(@Valid @RequestBody EcommerceActivationCodeRequest request) {
        try {
            log.info("添加激活码，激活码：{}", request.getActiveCode());
            // 检查激活码是否已存在
            boolean exists = activationCodesService.lambdaQuery()
                .eq(EcommerceActivationCodes::getActiveCode, request.getActiveCode())
                .count() > 0;
            if (exists) {
                return Result.error("激活码已存在");
            }


            // 创建激活码对象
            EcommerceActivationCodes code = new EcommerceActivationCodes();
            code.setActiveCode(request.getActiveCode());
            code.setActivationLink(request.getActivationLink());
            code.setStatus(request.getStatus());
            code.setCreatedAt(LocalDateTime.now());
            code.setUpdatedAt(LocalDateTime.now());

            // 保存激活码
            activationCodesService.save(code);
            
            return Result.success("添加成功", code);
        } catch (Exception e) {
            log.error("添加激活码异常", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 删除激活码
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteActivationCode(@PathVariable Long id) {
        try {
            log.info("删除激活码，ID：{}", id);
            
            boolean removed = activationCodesService.removeById(id);
            
            if (removed) {
                return Result.success("删除成功");
            } else {
                return Result.error("激活码不存在");
            }
        } catch (Exception e) {
            log.error("删除激活码异常", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 修改激活码
     */
    @PutMapping("/{id}")
    public Result<EcommerceActivationCodes> updateActivationCode(
            @PathVariable Long id,
            @Valid @RequestBody EcommerceActivationCodeRequest request) {
        try {
            log.info("修改激活码，ID：{}", id);
            // 检查激活码是否存在
            EcommerceActivationCodes code = activationCodesService.getById(id);
            if (code == null) {
                return Result.error("激活码不存在");
            }
            // 检查新的激活码是否与其他记录重复
            boolean exists = activationCodesService.lambdaQuery()
                .eq(EcommerceActivationCodes::getActiveCode, request.getActiveCode())
                .ne(EcommerceActivationCodes::getId, id)
                .count() > 0;
            if (exists) {
                return Result.error("激活码已被使用");
            }


            // 更新激活码信息
            code.setActiveCode(request.getActiveCode());
            code.setActivationLink(request.getActivationLink());
            code.setStatus(request.getStatus());
            code.setUpdatedAt(LocalDateTime.now());

            activationCodesService.updateById(code);
            
            return Result.success("修改成功", code);
        } catch (Exception e) {
            log.error("修改激活码异常", e);
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 获取激活码详情
     */
    @GetMapping("/{id}")
    public Result<EcommerceActivationCodes> getActivationCode(@PathVariable Long id) {
        try {
            log.info("获取激活码详情，ID：{}", id);
            
            EcommerceActivationCodes code = activationCodesService.getById(id);
            
            if (code != null) {
                return Result.success(code);
            } else {
                return Result.error("激活码不存在");
            }
        } catch (Exception e) {
            log.error("获取激活码详情异常", e);
            return Result.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询激活码列表
     */
    @GetMapping("/list")
    public Result<Page<EcommerceActivationCodes>> listActivationCodes(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String activeCode,
            @RequestParam(required = false) Boolean status) {
        try {
            log.info("查询激活码列表，页码：{}，每页数量：{}", current, size);
            
            // 创建分页对象
            Page<EcommerceActivationCodes> page = new Page<>(current, size);
            
            // 构建查询条件
            LambdaQueryWrapper<EcommerceActivationCodes> queryWrapper = new LambdaQueryWrapper<>();
            
            // 添加查询条件
            if (activeCode != null && !activeCode.trim().isEmpty()) {
                queryWrapper.like(EcommerceActivationCodes::getActiveCode, activeCode.trim());
            }
            if (status != null) {
                queryWrapper.eq(EcommerceActivationCodes::getStatus, status);
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(EcommerceActivationCodes::getCreatedAt);
            
            // 执行查询
            Page<EcommerceActivationCodes> result = activationCodesService.page(page, queryWrapper);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询激活码列表异常", e);
            return Result.error("查询列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有激活码列表
     */
    @GetMapping("/all")
    public Result<List<EcommerceActivationCodes>> getAllActivationCodes(
            @RequestParam(required = false) Boolean status) {
        try {
            log.info("查询所有激活码列表");
            
            // 构建查询条件
            LambdaQueryWrapper<EcommerceActivationCodes> queryWrapper = new LambdaQueryWrapper<>();
            
            // 如果指定了状态，则按状态筛选
            if (status != null) {
                queryWrapper.eq(EcommerceActivationCodes::getStatus, status);
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(EcommerceActivationCodes::getCreatedAt);
            
            // 执行查询
            List<EcommerceActivationCodes> list = activationCodesService.list(queryWrapper);
            
            return Result.success(list);
        } catch (Exception e) {
            log.error("查询所有激活码列表异常", e);
            return Result.error("查询列表失败：" + e.getMessage());
        }
    }

    /**
     * 启用指定激活码（其他全部禁用）
     */
    @PostMapping("/enable/{id}")
    public Result<Void> enableActivationCode(@PathVariable Long id) {
        try {
            // 1. 全部禁用
            EcommerceActivationCodes updateAll = new EcommerceActivationCodes();
            updateAll.setStatus(false);
            activationCodesService.update(updateAll, new LambdaQueryWrapper<>());

            // 2. 启用指定id
            EcommerceActivationCodes code = activationCodesService.getById(id);
            if (code == null) {
                return Result.error("激活码不存在");
            }
            code.setStatus(true);
            activationCodesService.updateById(code);
            return Result.success("启用成功");
        } catch (Exception e) {
            log.error("启用激活码异常", e);
            return Result.error("启用失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有激活码（active_code）列表
     */
    @GetMapping("/codes")
    public Result<List<String>> getAllActiveCodes() {
        try {
            log.info("获取所有激活码（active_code）");
            // 查询所有激活码的active_code字段
            List<String> codes = activationCodesService.lambdaQuery()
                    .select(EcommerceActivationCodes::getActiveCode)
                    .list()
                    .stream()
                    .map(EcommerceActivationCodes::getActiveCode)
                    .collect(Collectors.toList());
            return Result.success(codes);
        } catch (Exception e) {
            log.error("获取所有激活码异常", e);
            return Result.error("获取激活码失败：" + e.getMessage());
        }
    }
}
