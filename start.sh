#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# JAR包名称
JAR_NAME="alipay-auth-callback.jar"

# 指定日志文件
LOG_FILE="$SCRIPT_DIR/app.log"

# JVM参数
JAVA_OPTS="-Xms256m -Xmx512m -XX:+HeapDumpOnOutOfMemoryError"

# 检查是否已经在运行
PID=$(pgrep -f $JAR_NAME)
if [ ! -z "$PID" ]; then
    echo "程序已经在运行，PID: $PID"
    exit 1
fi

# 运行JAR包
echo "开始启动程序..."
nohup java $JAVA_OPTS -jar \
    -Dspring.profiles.active=prod \
    -Dserver.port=8080 \
    $SCRIPT_DIR/$JAR_NAME > $LOG_FILE 2>&1 &

# 等待几秒检查是否启动成功
sleep 5
NEW_PID=$(pgrep -f $JAR_NAME)
if [ ! -z "$NEW_PID" ]; then
    echo "程序启动成功，PID: $NEW_PID"
    echo "日志文件：$LOG_FILE"
else
    echo "程序启动失败，请检查日志文件：$LOG_FILE"
fi 