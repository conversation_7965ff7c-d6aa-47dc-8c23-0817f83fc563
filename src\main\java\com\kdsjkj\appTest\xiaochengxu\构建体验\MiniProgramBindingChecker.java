//package com.kdsjkj.appTest.xiaochengxu.构建体验;
//
//import com.alipay.api.AlipayApiException;
//import com.alipay.api.AlipayClient;
//import com.alipay.api.DefaultAlipayClient;
//import com.alipay.api.diagnosis.DiagnosisUtils;
//import com.alipay.api.request.AlipayOpenMiniBaseinfoQueryRequest;
//import com.alipay.api.response.AlipayOpenMiniBaseinfoQueryResponse;
//import com.alipay.api.domain.AlipayOpenMiniBaseinfoQueryModel;
//import com.kdsjkj.config.AlipayMiniProgramConfig;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//
///**
// * 小程序关联绑定状态检查工具
// * 用于检查商家小程序是否正确继承了第三方应用的配置
// */
//public class MiniProgramBindingChecker {
//
//    public static void main(String[] args) {
//        System.out.println("=== 小程序关联绑定状态检查 ===");
//
//        MiniProgramBindingChecker checker = new MiniProgramBindingChecker();
//        checker.checkBindingStatus();
//    }
//
//    /**
//     * 检查小程序关联绑定状态
//     */
//    public void checkBindingStatus() {
//        try {
//            System.out.println("\n🔍 检查小程序基本信息和绑定状态...");
//
//            // 初始化SDK
//            AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());
//
//            // 构造请求参数
//            AlipayOpenMiniBaseinfoQueryRequest request = new AlipayOpenMiniBaseinfoQueryRequest();
//            AlipayOpenMiniBaseinfoQueryModel model = new AlipayOpenMiniBaseinfoQueryModel();
//
//            request.setBizModel(model);
//            request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
//
//            // 执行请求
//            System.out.println("📤 发送小程序基本信息查询请求...");
//            AlipayOpenMiniBaseinfoQueryResponse response = alipayClient.execute(request);
//
//            System.out.println("\n📥 服务器响应:");
//            System.out.println("======================================");
//            System.out.println(response.getBody());
//            System.out.println("======================================");
//
//            if (response.isSuccess()) {
//                System.out.println("\n✅ 小程序基本信息查询成功");
//                analyzeBindingStatus(response.getBody());
//            } else {
//                System.out.println("\n❌ 小程序基本信息查询失败");
//                System.out.println("错误码: " + response.getCode());
//                System.out.println("错误信息: " + response.getMsg());
//
//                // 分析错误原因
//                analyzeError(response.getCode(), response.getMsg());
//
//                // 获取诊断链接
//                try {
//                    String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
//                    System.out.println("🔍 诊断链接: " + diagnosisUrl);
//                } catch (Exception e) {
//                    System.out.println("⚠️ 无法获取诊断链接");
//                }
//            }
//
//        } catch (AlipayApiException e) {
//            System.err.println("❌ API调用异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 分析绑定状态
//     */
//    private void analyzeBindingStatus(String responseBody) {
//        try {
//            JSONObject rootNode = JSON.parseObject(responseBody);
//            JSONObject responseNode = rootNode.getJSONObject("alipay_open_mini_baseinfo_query_response");
//
//            if (responseNode != null) {
//                System.out.println("\n📊 小程序配置分析:");
//                System.out.println("======================================");
//
//                // 检查关键配置字段
//                String appName = responseNode.getString("app_name");
//                String appStatus = responseNode.getString("app_status");
//                String appSlogan = responseNode.getString("app_slogan");
//                String appDesc = responseNode.getString("app_desc");
//
//                System.out.println("📱 小程序名称: " + (appName != null ? appName : "未设置"));
//                System.out.println("📊 小程序状态: " + (appStatus != null ? appStatus : "未知"));
//                System.out.println("🏷️ 小程序标语: " + (appSlogan != null ? appSlogan : "未设置"));
//                System.out.println("📝 小程序描述: " + (appDesc != null ? appDesc : "未设置"));
//
//                // 检查是否继承了第三方应用配置
//                checkInheritedConfig(responseNode);
//
//                System.out.println("======================================");
//
//                // 提供解决方案
//                provideSolutions();
//
//            } else {
//                System.out.println("⚠️ 无法解析响应数据");
//            }
//
//        } catch (Exception e) {
//            System.err.println("❌ 分析绑定状态失败: " + e.getMessage());
//            System.out.println("原始响应: " + responseBody);
//        }
//    }
//
//    /**
//     * 检查继承的配置
//     */
//    private void checkInheritedConfig(JSONObject responseNode) {
//        System.out.println("\n🔧 配置继承检查:");
//
//        // 检查接口权限
//        if (responseNode.containsKey("api_scope")) {
//            System.out.println("✅ 接口权限配置已继承");
//        } else {
//            System.out.println("❌ 接口权限配置未继承");
//        }
//
//        // 检查域名白名单
//        if (responseNode.containsKey("domain_list")) {
//            System.out.println("✅ 域名白名单已继承");
//        } else {
//            System.out.println("❌ 域名白名单未继承");
//        }
//
//        // 检查其他配置
//        String[] configKeys = {"service_phone", "service_email", "app_logo"};
//        for (String key : configKeys) {
//            if (responseNode.containsKey(key)) {
//                System.out.println("✅ " + key + " 配置已设置");
//            } else {
//                System.out.println("⚠️ " + key + " 配置未设置");
//            }
//        }
//    }
//
//    /**
//     * 分析错误原因
//     */
//    private void analyzeError(String errorCode, String errorMsg) {
//        System.out.println("\n🔍 错误分析:");
//        System.out.println("======================================");
//
//        if ("40004".equals(errorCode)) {
//            System.out.println("❌ 业务处理失败 - 可能原因:");
//            System.out.println("   1. app_auth_token 无效或过期");
//            System.out.println("   2. 小程序与第三方应用关联绑定不完整");
//            System.out.println("   3. 小程序状态异常");
//        } else if ("20000".equals(errorCode)) {
//            System.out.println("❌ 系统繁忙 - 建议稍后重试");
//        } else if ("40001".equals(errorCode)) {
//            System.out.println("❌ 缺少必选参数");
//        } else if ("40002".equals(errorCode)) {
//            System.out.println("❌ 非法的参数");
//        } else {
//            System.out.println("❌ 未知错误码: " + errorCode);
//        }
//
//        System.out.println("======================================");
//    }
//
//    /**
//     * 提供解决方案
//     */
//    private void provideSolutions() {
//        System.out.println("\n💡 解决方案建议:");
//        System.out.println("======================================");
//        System.out.println("1. 检查第三方应用授权:");
//        System.out.println("   - 确保 app_auth_token 有效且未过期");
//        System.out.println("   - 当前token: " + AlipayMiniProgramConfig.getAppAuthToken());
//
//        System.out.println("\n2. 完善小程序关联绑定:");
//        System.out.println("   - 登录支付宝开放平台");
//        System.out.println("   - 进入第三方应用管理");
//        System.out.println("   - 确保小程序已正确关联到第三方应用");
//
//        System.out.println("\n3. 检查小程序配置:");
//        System.out.println("   - 确保小程序基本信息完整");
//        System.out.println("   - 检查接口权限配置");
//        System.out.println("   - 验证域名白名单设置");
//
//        System.out.println("\n4. 重新构建小程序:");
//        System.out.println("   - 在确认关联绑定完成后");
//        System.out.println("   - 重新执行构建流程");
//        System.out.println("   - 检查构建日志");
//
//        System.out.println("======================================");
//    }
//}