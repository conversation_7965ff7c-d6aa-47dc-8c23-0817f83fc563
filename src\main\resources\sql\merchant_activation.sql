-- 商户激活状态记录表
CREATE TABLE merchant_activation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    app_id VARCHAR(64) NOT NULL COMMENT '商户APPID',
    agent_uid VARCHAR(64) NOT NULL COMMENT '代理UID',
    activation_amount DECIMAL(10,2) NOT NULL COMMENT '激活金额',
    activation_time DATETIME NOT NULL COMMENT '激活时间',
    reward_amount DECIMAL(10,2) NOT NULL COMMENT '奖励金额',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_app_id (app_id) COMMENT '商户APPID唯一索引',
    INDEX idx_agent_uid (agent_uid) COMMENT '代理UID索引',
    INDEX idx_activation_time (activation_time) COMMENT '激活时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户激活状态记录表';
