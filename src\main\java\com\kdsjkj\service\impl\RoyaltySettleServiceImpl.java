package com.kdsjkj.service.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeOrderSettleModel;
import com.alipay.api.domain.OpenApiRoyaltyDetailInfoPojo;
import com.alipay.api.request.AlipayTradeOrderSettleRequest;
import com.alipay.api.response.AlipayTradeOrderSettleResponse;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.service.*;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IAlipayConfigFrontService;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.kdsjkj.service.IAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 分账结算服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
@Slf4j
public class RoyaltySettleServiceImpl implements IRoyaltySettleService {
    
    @Autowired
    private IOrderService orderService;
    
    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;
    
    @Autowired
    private IRoyaltyRelationService royaltyRelationService;
    
    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;
    
    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;
    
    @Autowired
    private IAgentService agentService;

    @Override
    public boolean executeRoyaltySettle(String tradeNo, String totalAmount) {
        try {
            log.info("开始执行分账 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);

            // 1. 根据tradeNo查询appId
            String appId = orderService.getAppIdByTradeNo(tradeNo);
            if (appId == null || appId.trim().isEmpty()) {
                log.error("未找到对应交易号的appId - 交易号: {}", tradeNo);
                return false;
            }
            log.info("查询到appId: {}", appId);


            // 2. 根据appId查询代理UID
            String agentUid = serviceMarketOrderService.getAgentUidByAppid(appId);
            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.error("未找到对应的代理UID - appId: {}", appId);
                return false;
            }
            log.info("查询到代理UID: {}", agentUid);
            
            // 3. 根据代理UID查询费率
            BigDecimal rate = getRateByAgentUid(agentUid);
            if (rate == null) {
                log.error("未找到对应代理的费率 - agentUid: {}", agentUid);
                return false;
            }
            // 3.5. 根据appId查询商户费率并进行比较
            BigDecimal merchantRate = serviceMarketOrderService.getMerchantRateByAppid(appId);
            BigDecimal finalRate;
            if (merchantRate != null) {
                log.info("查询到商户费率: {}%", merchantRate);
                // 计算费率差值：商户费率 - 0.3%
                finalRate = merchantRate.subtract(new BigDecimal("0.3"));
                log.info("计算费率差值: 商户费率{}% - 0.3% = {}%", merchantRate, finalRate);
            } else {
                log.info("未找到商户费率，不进行分账");
                // 如果没有商户费率，不分账
                finalRate = BigDecimal.ZERO;
                log.info("没有商户费率，设置分账费率为0");
            }
            
            // 检查费率是否大于0
            if (finalRate.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("费率为{}%，不进行分账", finalRate);
                return true; // 返回成功，但不执行分账
            }
            log.info("最终分账费率: {}%", finalRate);
            
            // 4. 计算分账金额 = totalAmount * rate / 100 并截取小数点后2位
            BigDecimal totalAmountDecimal = new BigDecimal(totalAmount);
            BigDecimal royaltyAmount = totalAmountDecimal
                    .multiply(finalRate)
                    .divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
            
            log.info("计算分账金额: {} * {} / 100 = {}", totalAmount, finalRate, royaltyAmount);

            // 5. 查询分账接收方用户ID
            String royaltyUserId = royaltyRelationService.getRoyaltyUserId();
            if (royaltyUserId == null || royaltyUserId.trim().isEmpty()) {
                log.error("未找到分账接收方用户ID");
                return false;
            }
            log.info("分账接收方用户ID: {}", royaltyUserId);

            // 6. 执行分账请求
            return performRoyaltySettle(tradeNo, royaltyAmount.toString(), royaltyUserId, appId);

        } catch (Exception e) {
            log.error("执行分账异常 - 交易号: {}", tradeNo, e);
            return false;
        }
    }

    /**
     * 根据代理UID获取费率
     */
    private BigDecimal getRateByAgentUid(String agentUid) {
        try {
            log.info("开始查询代理费率 - agentUid: {}", agentUid);
            
            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.error("代理UID为空，无法查询费率");
                return null;
            }
            
            // 使用AgentService查询代理费率
            BigDecimal rate = agentService.getAgentRateByUid(agentUid);
            
            if (rate == null) {
                log.error("未找到代理费率 - agentUid: {}", agentUid);
                return null;
            }
            
            // Agent表中的rate是小数形式（如0.05表示5%），需要转换为百分比形式
            BigDecimal ratePercent = rate;
            log.info("查询到代理费率: {} (原值: {})", ratePercent, rate);
            
            return ratePercent;
            
        } catch (Exception e) {
            log.error("查询代理费率异常 - agentUid: {}", agentUid, e);
            return null;
        }
    }

    /**
     * 执行分账请求
     */
    private boolean performRoyaltySettle(String tradeNo, String amount, String royaltyUserId, String appId) {
        try {
            log.info("开始调用支付宝分账接口 - 交易号: {}, 分账金额: {}, 接收方: {}", tradeNo, amount, royaltyUserId);

            // 获取支付宝配置
            AlipayConfig alipayConfig = getAlipayConfig();
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);

            // 获取app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appId);
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                log.error("未找到对应appId的授权令牌信息 - appId: {}", appId);
                return false;
            }

            // 构造分账请求
            AlipayTradeOrderSettleRequest request = new AlipayTradeOrderSettleRequest();
            AlipayTradeOrderSettleModel model = new AlipayTradeOrderSettleModel();

            // 设置结算请求流水号（使用时间戳保证唯一性）
            String outRequestNo = "SETTLE_" + System.currentTimeMillis();
            model.setOutRequestNo(outRequestNo);

            // 设置支付宝交易号
            model.setTradeNo(tradeNo);

            // 设置分账明细信息
            List<OpenApiRoyaltyDetailInfoPojo> royaltyParameters = new ArrayList<>();
            OpenApiRoyaltyDetailInfoPojo royaltyDetail = new OpenApiRoyaltyDetailInfoPojo();
            royaltyDetail.setRoyaltyType("transfer");           // 分账类型
            royaltyDetail.setTransInType("userId");             // 转入方类型
            royaltyDetail.setTransIn(royaltyUserId);             // 转入方账户
            royaltyDetail.setAmount(amount);                     // 分账金额
            royaltyDetail.setDesc("分账" + amount);              // 分账描述

            royaltyParameters.add(royaltyDetail);
            model.setRoyaltyParameters(royaltyParameters);

            request.setBizModel(model);

            // 设置第三方应用授权令牌
            request.putOtherTextParam("app_auth_token", appAuthToken);

            log.info("分账请求参数: outRequestNo={}, tradeNo={}, amount={}, royaltyUserId={}", 
                    outRequestNo, tradeNo, amount, royaltyUserId);

            // 执行分账请求
            AlipayTradeOrderSettleResponse response = alipayClient.execute(request);
            log.info("分账响应: {}", response.getBody());

            if (response.isSuccess()) {
                log.info("✅ 分账成功 - 交易号: {}, 分账金额: {}", tradeNo, amount);
                return true;
            } else {
                log.error("❌ 分账失败 - 交易号: {}, 错误: {}", tradeNo, response.getSubMsg());
                return false;
            }

        } catch (AlipayApiException e) {
            log.error("调用支付宝分账接口异常 - 交易号: {}", tradeNo, e);
            return false;
        } catch (Exception e) {
            log.error("分账处理异常 - 交易号: {}", tradeNo, e);
            return false;
        }
    }

    /**
     * 获取支付宝配置
     */
    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();

        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }

} 