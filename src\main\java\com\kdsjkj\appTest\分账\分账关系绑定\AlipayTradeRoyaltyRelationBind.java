package com.kdsjkj.appTest.分账.分账关系绑定;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.response.AlipayTradeRoyaltyRelationBindResponse;
import com.alipay.api.domain.AlipayTradeRoyaltyRelationBindModel;
import com.alipay.api.request.AlipayTradeRoyaltyRelationBindRequest;
import com.alipay.api.domain.RoyaltyEntity;
import com.kdsjkj.config.AlipayMiniProgramConfig;

import java.util.ArrayList;
import java.util.List;

public class AlipayTradeRoyaltyRelationBind {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeRoyaltyRelationBindRequest request = new AlipayTradeRoyaltyRelationBindRequest();
        AlipayTradeRoyaltyRelationBindModel model = new AlipayTradeRoyaltyRelationBindModel();
        
        // 设置分账接收方列表
        List<RoyaltyEntity> receiverList = new ArrayList<RoyaltyEntity>();
        RoyaltyEntity receiverList0 = new RoyaltyEntity();
//        receiverList0.setLoginName("<EMAIL>");
//        receiverList0.setName("吴丽华");
        receiverList0.setMemo("分账给测试");
//        receiverList0.setAccountOpenId("093PJtAPYb2UkQ0mXk_X86Z_FaMou-DtIEvERQ8X8yqKaEf");
//        receiverList0.setBindLoginName("<EMAIL>");
        receiverList0.setType("userId");
        receiverList0.setAccount("****************");
        receiverList.add(receiverList0);
        model.setReceiverList(receiverList);
        
        // 设置外部请求号
        model.setOutRequestNo("****************");
        
        request.setBizModel(model);
        
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayTradeRoyaltyRelationBindResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}