<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.AgentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.Agent">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="uid" property="uid" />
        <result column="rate" property="rate" />
        <result column="parent_uid" property="parentUid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, phone, uid, rate, parent_uid, create_time, update_time
    </sql>

</mapper>
