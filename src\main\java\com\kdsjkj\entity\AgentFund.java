package com.kdsjkj.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代理资金表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agent_fund")
public class AgentFund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代理UID
     */
    @TableField("agent_uid")
    private String agentUid;

    /**
     * 本次获得金额
     */
    private BigDecimal amount;

    /**
     * 获取方式：1达标奖励 2激活奖励 3分润奖励 4分红奖励
     */
    @TableField("obtain_type")
    private Integer obtainType;

    /**
     * 获取时间
     */
    @TableField("obtain_time")
    private LocalDateTime obtainTime;

}
