<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.Product">
        <id column="id" property="id" />
        <result column="product_name" property="productName" />
        <result column="product_image_url" property="productImageUrl" />
        <result column="product_price" property="productPrice" />
        <result column="product_appid" property="productAppid" />
        <result column="product_description" property="productDescription" />
        <result column="stock_quantity" property="stockQuantity" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_name, product_image_url, product_price, product_appid, product_description, stock_quantity, status, create_time, update_time
    </sql>

</mapper>
