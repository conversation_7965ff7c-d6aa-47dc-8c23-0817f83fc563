package com.kdsjkj.appTest.xiaochengxu.C提审.提审商家小程序;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.response.AlipayOpenMiniVersionAuditApplyResponse;
import com.alipay.api.request.AlipayOpenMiniVersionAuditApplyRequest;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.FileItem;
import java.util.Base64;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayOpenMiniVersionAuditApply {

    public static void main(String[] args) throws AlipayApiException, IOException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());
        //这是一个1x1像素的图片流，用于其他必要的图片上传
        String imageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAEUlEQVR42mP4TyRgGFVIX4UAI/uOgGWVNeQAAAAASUVORK5CYII=";

        // 构造请求参数以调用接口
        AlipayOpenMiniVersionAuditApplyRequest request = new AlipayOpenMiniVersionAuditApplyRequest();

            FileItem appLogo = new FileItem("appLogo.jpg", Base64.getDecoder().decode(imageBase64));
            request.setAppLogo(appLogo);

        

        
        // 设置小程序版本号
        request.setAppVersion("0.0.5");
        
        // 设置小程序版本描述
        request.setVersionDesc("此版本提供了线上商城购物、商品浏览等功能");
        
        // 设置小程序简介 - 更具体的服务描述（10-32个字符限制）
        request.setAppSlogan("成都3C数码产品在线购物商城");
        
        // 设置区域类型
        request.setRegionType("CHINA");
        
        // 设置新小程序前台类目
        request.setMiniCategoryIds("XS1020_XS2564");

        
        // 设置小程序名称 - 按照规范：【地域+企业关键字+服务内容】
        request.setAppName("翌梦助手");

        
        // 设置小程序客服电话
        request.setServicePhone("15908293192");
        
        // 设置小程序描述 - 更详细的功能描述
        request.setAppDesc("成都科技数码商城小程序，提供3C数码产品在线浏览、购买、订单管理、客服咨询等服务。用户可在线下单，支持线下门店自提服务。");
        

        
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenMiniVersionAuditApplyResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            System.out.println(diagnosisUrl);
        }
    }
} 