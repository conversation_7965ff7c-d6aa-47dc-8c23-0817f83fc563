package com.kdsjkj.service;

import com.kdsjkj.entity.RoyaltyRelation;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 分账关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface IRoyaltyRelationService extends IService<RoyaltyRelation> {

    /**
     * 获取分账接收方用户ID（表中只有一条记录）
     * @return 用户ID
     */
    String getRoyaltyUserId();

    /**
     * 根据APPID获取分账接收方用户ID
     * @param appid 小程序APPID
     * @return 用户ID
     */
    String getRoyaltyUserIdByAppid(String appid);

}
