package com.kdsjkj.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 商户交易统计数据传输对象
 */
@Data
public class MerchantTransactionDTO {
    /**
     * 排名序号
     */
    private Integer rank;
    
    /**
     * 商户小程序APPID
     */
    private String appId;
    
    /**
     * 联系人
     */
    private String contactor;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 交易总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 交易总笔数
     */
    private Integer transactionCount;
} 