package com.kdsjkj.dto;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 添加顶级代理人请求
 */
@Data
public class AddTopAgentRequest {
    
    /**
     * 代理姓名
     */
    @NotBlank(message = "代理人姓名不能为空")
    @Size(min = 2, max = 50, message = "代理人姓名长度必须在2-50个字符之间")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 用户唯一标识
     */
    @NotBlank(message = "UID不能为空")
    @Size(min = 1, max = 50, message = "UID长度必须在1-50个字符之间")
    private String uid;
} 