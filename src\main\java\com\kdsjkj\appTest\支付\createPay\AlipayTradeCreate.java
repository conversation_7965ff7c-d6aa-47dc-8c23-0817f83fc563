package com.kdsjkj.appTest.支付.createPay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeCreateRequest;
import com.alipay.api.response.AlipayTradeCreateResponse;
import com.alipay.api.domain.AlipayTradeCreateModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayTradeCreate {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeCreateRequest request = new AlipayTradeCreateRequest();
        AlipayTradeCreateModel model = new AlipayTradeCreateModel();

        // 设置商户订单号
        model.setOutTradeNo("202501070811500001");

        // 设置订单总金额
        model.setTotalAmount("0.01");

        // 设置订单标题
        model.setSubject("测试订单");

        // 设置用户id
        model.setBuyerId(" 2088052800156834");

        request.setBizModel(model);

        // 设置异步通知地址
//        request.setNotifyUrl("https://pay.yulinxinxi.com/api/alipay/createPay");

        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayTradeCreateResponse response = alipayClient.execute(request);
        System.out.println();
        System.out.println(response.getBody());
        System.out.println();
        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}