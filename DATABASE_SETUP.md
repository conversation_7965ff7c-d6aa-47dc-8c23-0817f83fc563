# MySQL数据库配置说明

## 数据库信息
- **服务器地址**: *************:3306
- **数据库名**: alipay_auth
- **用户名**: sft
- **密码**: 123456zxc
- **MySQL版本**: 8.0

## 初始化步骤

### 1. 创建数据库
首先需要在MySQL服务器上创建数据库，可以通过以下方式：

#### 方式一：使用MySQL命令行
```bash
mysql -h ************* -u sft -p
```
输入密码后执行：
```sql
CREATE DATABASE IF NOT EXISTS alipay_auth 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

#### 方式二：执行初始化脚本
```bash
mysql -h ************* -u sft -p < src/main/resources/sql/init.sql
```

### 2. 验证数据库连接
启动应用后，访问以下接口验证数据库连接：

- **连接测试**: `GET /api/test/db-connection`
- **查询测试**: `GET /api/test/db-query`
- **表检查**: `GET /api/test/check-tables`

### 3. 表结构说明

#### alipay_auth_record（支付宝授权记录表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID，自增 |
| notify_id | VARCHAR(100) | 通知ID，唯一索引 |
| app_id | VARCHAR(50) | 第三方应用APPID |
| auth_app_id | VARCHAR(50) | 授权小程序APPID |
| app_auth_token | VARCHAR(500) | 授权令牌 |
| app_refresh_token | VARCHAR(500) | 刷新令牌 |
| user_id | VARCHAR(50) | 授权商家用户ID |
| auth_time | BIGINT | 授权时间戳 |
| expires_in | BIGINT | 令牌过期时间（秒） |
| process_status | VARCHAR(20) | 处理状态 |
| create_time | TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | 更新时间 |

#### mini_program_version（小程序版本管理表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID，自增 |
| auth_app_id | VARCHAR(50) | 商家小程序APPID |
| template_id | VARCHAR(50) | 模板APPID |
| template_version | VARCHAR(20) | 模板版本号 |
| app_version | VARCHAR(20) | 商家小程序版本号 |
| ext_config | TEXT | 自定义配置参数 |
| status | VARCHAR(20) | 状态 |
| qr_code_url | VARCHAR(500) | 体验版二维码URL |
| merchant_name | VARCHAR(100) | 商家名称 |
| title | VARCHAR(100) | 小程序标题 |

## 环境配置

### 开发环境
启动时使用开发环境配置：
```bash
java -jar app.jar --spring.profiles.active=dev
```

### 生产环境
默认使用生产环境配置，包含SSL和完整的安全配置。

## 连接池配置说明

### HikariCP配置参数
- `minimum-idle`: 最小空闲连接数
- `maximum-pool-size`: 最大连接池大小
- `idle-timeout`: 空闲连接超时时间
- `max-lifetime`: 连接最大生存时间
- `connection-timeout`: 连接超时时间

### 性能优化建议
1. 根据并发量调整连接池大小
2. 监控连接池使用情况
3. 定期检查慢查询日志
4. 优化SQL查询和索引

## 故障排除

### 常见错误及解决方案

1. **连接被拒绝**
   ```
   Host 'xxx.xxx.xxx.xxx' is not allowed to connect to this MySQL server
   ```
   解决：需要在MySQL服务器上授权客户端IP访问权限

2. **时区问题**
   ```
   The server time zone value 'CST' is unrecognized
   ```
   解决：连接字符串中已包含 `serverTimezone=Asia/Shanghai`

3. **SSL连接问题**
   ```
   SSL connection error
   ```
   解决：连接字符串中已设置 `useSSL=false`

4. **字符编码问题**
   解决：连接字符串中已设置 `useUnicode=true&characterEncoding=utf8`

## 安全建议

1. **定期更换密码**：建议定期更换数据库密码
2. **限制访问IP**：只允许必要的IP地址访问数据库
3. **使用SSL连接**：生产环境建议启用SSL连接
4. **权限最小化**：只授予应用必要的数据库权限
5. **备份策略**：制定定期备份策略
6. **监控日志**：监控数据库访问日志，及时发现异常 