package com.kdsjkj.appTest.xiaochengxu.A生成版本.B查询小程序模板和商家小程序的版本信息;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayOpenMiniVersionListQueryRequest;
import com.alipay.api.response.AlipayOpenMiniVersionListQueryResponse;
import com.alipay.api.domain.AlipayOpenMiniVersionListQueryModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayOpenMiniVersionListQuery {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniVersionListQueryRequest request = new AlipayOpenMiniVersionListQueryRequest();
        AlipayOpenMiniVersionListQueryModel model = new AlipayOpenMiniVersionListQueryModel();
        
        // 设置端参数
        model.setBundleId("com.alipay.alipaywallet");
        
        // 设置版本状态列表
        model.setVersionStatus("AUDIT_REJECT");
        
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenMiniVersionListQueryResponse response = alipayClient.execute(request);
        System.out.println("-----------------------");
        System.out.println(response.getBody());
        System.out.println("-----------------------");

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}