package com.kdsjkj.constant;

/**
 * 交易状态枚举
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
public class TradeStatus {
    
    /** 等待买家付款 */
    public static final String WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
    
    /** 交易成功 */
    public static final String TRADE_SUCCESS = "TRADE_SUCCESS";
    
    /** 交易完成 */
    public static final String TRADE_FINISHED = "TRADE_FINISHED";
    
    /** 交易关闭 */
    public static final String TRADE_CLOSED = "TRADE_CLOSED";
    
    /** 交易退款 */
    public static final String TRADE_REFUND = "TRADE_REFUND";
    
    /** 部分退款 */
    public static final String TRADE_PARTIAL_REFUND = "TRADE_PARTIAL_REFUND";
    
    private TradeStatus() {
        // 工具类，禁止实例化
    }
} 