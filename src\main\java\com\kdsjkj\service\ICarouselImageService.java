package com.kdsjkj.service;

import com.kdsjkj.entity.CarouselImage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 轮播图表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface ICarouselImageService extends IService<CarouselImage> {

    /**
     * 根据appid查询轮播图列表，按排序字段和创建时间排序
     * @param appid 小程序appid
     * @return 轮播图列表
     */
    List<CarouselImage> getCarouselImagesByAppid(String appid);
}
