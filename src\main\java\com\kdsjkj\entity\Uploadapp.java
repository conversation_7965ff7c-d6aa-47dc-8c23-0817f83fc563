package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 支付宝小程序上传表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Uploadapp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 授权令牌
     */
    @TableField("app_auth_token")
    private String appAuthToken;

    /**
     * 小程序模板版本号
     */
    @TableField("template_version")
    private String templateVersion;

    /**
     * 小程序模板APPID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 商家小程序版本号
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 小程序投放的端参数
     */
    @TableField("bundle_id")
    private String bundleId;

    /**
     * 扩展配置参数(JSON格式)
     */
    @TableField("ext")
    private String ext;

}
