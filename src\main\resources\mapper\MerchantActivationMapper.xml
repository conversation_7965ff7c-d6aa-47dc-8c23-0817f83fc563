<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.MerchantActivationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.MerchantActivation">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="agent_uid" property="agentUid" />
        <result column="activation_amount" property="activationAmount" />
        <result column="activation_time" property="activationTime" />
        <result column="reward_amount" property="rewardAmount" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, agent_uid, activation_amount, activation_time, reward_amount, created_at, updated_at
    </sql>

</mapper>