package com.kdsjkj.service;

import com.kdsjkj.entity.AgentPoints;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 代理积分表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface IAgentPointsService extends IService<AgentPoints> {

    /**
     * 为代理添加积分
     *
     * @param agentUid 代理UID
     * @param points 要添加的积分数量
     * @return 操作是否成功
     */
    boolean addPointsToAgent(String agentUid, Integer points);

    /**
     * 获取代理的当前积分
     *
     * @param agentUid 代理UID
     * @return 当前积分数量，如果代理不存在返回0
     */
    Integer getAgentPoints(String agentUid);

    /**
     * 设置代理的积分
     *
     * @param agentUid 代理UID
     * @param points 积分数量
     * @return 操作是否成功
     */
    boolean setAgentPoints(String agentUid, Integer points);

}
