<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.ProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.ProductInfo">
        <id column="id" property="id" />
        <result column="image_url" property="imageUrl" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="required_points" property="requiredPoints" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, image_url, name, description, required_points, create_time, update_time
    </sql>

</mapper>
