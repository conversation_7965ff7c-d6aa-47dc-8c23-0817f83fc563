package com.kdsjkj.appTest.qrcode.将二维码或条形码贴到指定图片上;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class PosterGenerator {

    /**
     * 获取Linux兼容的中文字体
     */
    private static Font getLinuxCompatibleFont(int fontSize) {
        // Linux系统最常见且稳定的中文字体列表
        String[] linuxFonts = {
            // 优先使用开源字体，这些在大多数Linux发行版中都有
            "DejaVu Sans",           // 最通用的字体，支持Unicode
            "Liberation Sans",       // Red Hat开发的开源字体
            "Noto Sans CJK SC",      // Google开发的中文字体
            "WenQuanYi Micro Hei",   // 文泉驿微米黑
            "WenQuanYi Zen Hei",     // 文泉驿正黑
            "Droid Sans Fallback",   // Android的后备字体
            "AR PL UMing CN",        // 文鼎字体
            // Java默认字体
            "SansSerif",
            "Dialog",
            "Monospaced"
        };
        
        System.out.println("🔍 正在查找Linux兼容的中文字体...");
        
        for (String fontName : linuxFonts) {
            try {
                Font font = new Font(fontName, Font.BOLD, fontSize);
                // 测试字体是否能显示中文
                if (font.canDisplay('中') && font.canDisplay('文')) {
                    System.out.println("✅ 使用字体: " + fontName + " (大小: " + fontSize + "px)");
                    return font;
                } else {
                    System.out.println("❌ 字体 " + fontName + " 不支持中文");
                }
            } catch (Exception e) {
                System.out.println("❌ 字体 " + fontName + " 创建失败: " + e.getMessage());
            }
        }
        
        // 如果所有字体都不可用，使用Java默认字体
        System.out.println("⚠️ 使用Java默认字体 SansSerif");
        return new Font(Font.SANS_SERIF, Font.BOLD, fontSize);
    }

    /**
     * 将二维码贴到海报上并替换文字
     */
    public static void pasteCodeOnPoster(String posterPath, String outputPath, BufferedImage qrCodeImage, int x, int y, String appName) throws IOException {
        // 读取海报图片
        BufferedImage posterImage = ImageIO.read(new File(posterPath));
        Graphics2D g2d = posterImage.createGraphics();
        
        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 贴上二维码
        g2d.drawImage(qrCodeImage, x, y, null);
        
        // 设置字体 - Linux兼容的中文字体
        int fontSize = Math.max(posterImage.getWidth() / 15, 24); // 最小24px
        java.awt.Font font = getLinuxCompatibleFont(fontSize);
        g2d.setFont(font);
        
        // 获取文字尺寸
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(appName);
        int textHeight = fm.getHeight();
        
        // 计算文字位置 - 放在二维码上方
        int textX = x + (qrCodeImage.getWidth() - textWidth) / 2; // 与二维码水平居中对齐
        int textY = y - 20; // 在二维码上方20像素
        
        // 如果文字位置太靠上，则放在二维码下方
        if (textY - textHeight < 0) {
            textY = y + qrCodeImage.getHeight() + textHeight + 20; // 在二维码下方
        }
        
        // 增强文字渲染质量
        g2d.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);

        // 验证文字内容
        if (appName != null && !appName.trim().isEmpty()) {
            // 绘制主文字（白色，清晰渲染）
            g2d.setColor(Color.WHITE);
            System.out.println("---------------------------------");
        g2d.drawString(appName, textX, textY);
            System.out.println("✓ 文字绘制完成: " + appName + " 位置: (" + textX + ", " + textY + ")");
        } else {
            System.out.println("⚠ 警告: 文字内容为空");
        }
        
        // 输出调试信息
        System.out.println("文字信息:");
        System.out.println("- 文字内容: " + appName);
        System.out.println("- 字体大小: " + fontSize + "px");
        System.out.println("- 文字位置: (" + textX + ", " + textY + ")");
        System.out.println("- 文字尺寸: " + textWidth + "x" + textHeight);
        
        g2d.dispose();
        
        // 保存图片
        ImageIO.write(posterImage, "PNG", new File(outputPath));
    }

    /**
     * 将二维码贴到海报上并替换文字 - 接受BufferedImage作为背景图片
     */
    public static void pasteCodeOnPoster(BufferedImage posterImage, String outputPath, BufferedImage qrCodeImage, int x, int y, String appName) throws IOException {
        // 创建副本避免修改原图
        BufferedImage resultImage = new BufferedImage(posterImage.getWidth(), posterImage.getHeight(), posterImage.getType());
        Graphics2D g2d = resultImage.createGraphics();

        // 绘制背景图片
        g2d.drawImage(posterImage, 0, 0, null);

        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 贴上二维码
        g2d.drawImage(qrCodeImage, x, y, null);

        // 设置字体 - Linux兼容的中文字体
        int fontSize = Math.max(posterImage.getWidth() / 15, 24); // 最小24px
        java.awt.Font font = getLinuxCompatibleFont(fontSize);
        g2d.setFont(font);
        
        // 获取文字尺寸
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(appName);
        int textHeight = fm.getHeight();
        
        // 计算文字位置 - 放在二维码上方
        int textX = x + (qrCodeImage.getWidth() - textWidth) / 2; // 与二维码水平居中对齐
        int textY = y - 20; // 在二维码上方20像素
        
        // 如果文字位置太靠上，则放在二维码下方
        if (textY - textHeight < 0) {
            textY = y + qrCodeImage.getHeight() + textHeight + 20; // 在二维码下方
        }
        
        // 增强文字渲染质量
        g2d.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        
        // 验证文字内容
        if (appName != null && !appName.trim().isEmpty()) {
            // 绘制主文字（白色，清晰渲染）
            g2d.setColor(Color.WHITE);
            g2d.drawString(appName, textX, textY);
            System.out.println("✓ 文字绘制完成: " + appName + " 位置: (" + textX + ", " + textY + ")");
        } else {
            System.out.println("⚠ 警告: 文字内容为空");
        }
        
        // 输出调试信息
        System.out.println("文字信息:");
        System.out.println("- 文字内容: " + appName);
        System.out.println("- 字体大小: " + fontSize + "px");
        System.out.println("- 文字位置: (" + textX + ", " + textY + ")");
        System.out.println("- 文字尺寸: " + textWidth + "x" + textHeight);
        
        g2d.dispose();
        
        // 保存图片
        ImageIO.write(resultImage, "PNG", new File(outputPath));
    }

    /**
     * 原有方法保持不变，为了向后兼容
     */
    public static void pasteCodeOnPoster(String posterPath, String outputPath, BufferedImage qrCodeImage, int x, int y) throws IOException {
        pasteCodeOnPoster(posterPath, outputPath, qrCodeImage, x, y, "百货店");  // 使用默认文字
    }
}