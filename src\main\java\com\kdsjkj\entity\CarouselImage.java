package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 轮播图表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("carousel_image")
public class CarouselImage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 轮播图URL地址
     */
    @TableField("carousel_url")
    private String carouselUrl;

    /**
     * 小程序APPID
     */
    private String appid;

    /**
     * 排序顺序，数字越小越靠前
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 状态：1启用，0禁用
     */
    private Boolean status;

    /**
     * 轮播图标题
     */
    private String title;

    /**
     * 轮播图描述
     */
    private String description;

    /**
     * 点击跳转链接
     */
    @TableField("link_url")
    private String linkUrl;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
