package com.kdsjkj.service.impl;

import com.kdsjkj.entity.ActivationRewards;
import com.kdsjkj.mapper.ActivationRewardsMapper;
import com.kdsjkj.service.IActivationRewardsService;
import com.kdsjkj.dto.UpdateActivationRewardRequest;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * <p>
 * 激活奖励表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
public class ActivationRewardsServiceImpl extends ServiceImpl<ActivationRewardsMapper, ActivationRewards> implements IActivationRewardsService {

    @Override
    public ActivationRewards getActivationReward() {
        log.info("开始获取激活奖励配置");
        
        // 获取唯一的配置记录
        ActivationRewards reward = this.lambdaQuery()
                .orderByDesc(ActivationRewards::getUpdatedAt)
                .last("LIMIT 1")
                .one();
        
        if (reward == null) {
            log.warn("未找到激活奖励配置，将创建默认配置");
            // 如果没有配置，创建默认配置
            reward = createDefaultReward();
        }
        
        log.info("获取激活奖励配置成功 - ID: {}", reward.getId());
        return reward;
    }
    
    @Override
    public ActivationRewards updateActivationReward(Long id, UpdateActivationRewardRequest request) {
        log.info("开始更新激活奖励配置 - ID: {}", id);
        
        // 1. 验证配置是否存在
        ActivationRewards reward = this.getById(id);
        if (reward == null) {
            log.error("激活奖励配置不存在 - ID: {}", id);
            throw new RuntimeException("激活奖励配置不存在");
        }
        
        // 2. 更新配置
        reward.setMinPaymentAmount(request.getMinPaymentAmount());
        reward.setRewardAmount(request.getRewardAmount());
        reward.setUpdatedAt(LocalDateTime.now());
        
        // 3. 保存更新
        boolean success = this.updateById(reward);
        if (!success) {
            log.error("更新激活奖励配置失败 - ID: {}", id);
            throw new RuntimeException("更新激活奖励配置失败");
        }
        
        log.info("更新激活奖励配置成功 - ID: {}", id);
        return reward;
    }
    
    /**
     * 创建默认的激活奖励配置
     */
    private ActivationRewards createDefaultReward() {
        ActivationRewards reward = new ActivationRewards();
        reward.setMinPaymentAmount(new BigDecimal("100.00"));  // 默认最低收款100元
        reward.setRewardAmount(new BigDecimal("10.00"));  // 默认奖励10元
        reward.setCreatedAt(LocalDateTime.now());
        reward.setUpdatedAt(LocalDateTime.now());
        
        boolean success = this.save(reward);
        if (!success) {
            log.error("创建默认激活奖励配置失败");
            throw new RuntimeException("创建默认激活奖励配置失败");
        }
        
        log.info("创建默认激活奖励配置成功 - ID: {}", reward.getId());
        return reward;
    }
}
