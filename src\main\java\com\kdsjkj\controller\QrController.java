package com.kdsjkj.controller;

import com.kdsjkj.entity.URLParameters;
import com.kdsjkj.获取推广码.DirectQRExtractor;
import com.kdsjkj.service.IEcommerceActivationCodesService;
import com.kdsjkj.entity.EcommerceActivationCodes;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.http.HttpHeaders.USER_AGENT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-09 12:52
 */
@Slf4j
@RestController
@RequestMapping("/api/qrcode")
@CrossOrigin(origins = "*", allowedHeaders = "*")  // 允许跨域访问
public class QrController {
    
    @Autowired
    private IEcommerceActivationCodesService ecommerceActivationCodesService;
    
    //
    @GetMapping("/getqrcode")
    public String getqrcode(String agentUID) {
        System.out.println(agentUID);
        
        // 从数据库获取status为1的activation_link
        QueryWrapper<EcommerceActivationCodes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", true);
        EcommerceActivationCodes activationCode = ecommerceActivationCodesService.getOne(queryWrapper);
        
        if (activationCode == null || activationCode.getActivationLink() == null) {
            System.err.println("未找到有效的激活链接");
            return null;
        }
        
        String originalUrl = activationCode.getActivationLink();
        System.out.println("从数据库获取的激活链接: " + originalUrl);
        
        try {
            URL parsedUrl = new URL(originalUrl);
            String query = parsedUrl.getQuery();

            DirectQRExtractor.URLParameters params = new DirectQRExtractor.URLParameters();
            String[] pairs = query.split("&");

            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0];
                    String value = URLDecoder.decode(keyValue[1], "UTF-8");
                    // 生成随机唯一值
                    String uniqueId = UUID.randomUUID().toString().replace("-", "");
                    switch (key) {
                        case "merchandiseId":
                            params.merchandiseId = value;
                            break;
                        case "marketCode":
                            params.marketCode = value;
                            break;
                        case "ticket":
                            params.ticket = agentUID+"_"+activationCode.getActiveCode()+"_"+uniqueId;
                            break;
                        case "callback":
                            params.callback = "https://pay.yulinxinxi.com/api/alipay/health";
                            break;
                        case "appName":
                            params.appName = value;
                            break;
                    }

                }
            }



                // 构建API URL
                String apiUrl = "https://app.alipay.com/alipaymarket/itemQrCodeToIsv.json" +
                        "?_input_charset=utf-8" +
                        "&merchandiseId=" + URLEncoder.encode(params.merchandiseId, "UTF-8") +
                        "&marketCode=" + URLEncoder.encode(params.marketCode, "UTF-8") +
                        "&ticket=" + URLEncoder.encode(params.ticket, "UTF-8") +
                        "&callback=" + URLEncoder.encode(params.callback, "UTF-8") +
                        "&appName=" + URLEncoder.encode(params.appName, "UTF-8");

                System.out.println("调用API: " + apiUrl);

                // 发送HTTP请求
                HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("User-Agent", USER_AGENT);
                connection.setRequestProperty("Accept", "application/json, text/plain, */*");
                connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                connection.setRequestProperty("Referer", "https://b.alipay.com/");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);

                int responseCode = connection.getResponseCode();
                System.out.println("📡 API响应码: " + responseCode);
            if (responseCode == 200) {
                // 读取响应
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                String jsonResponse = response.toString();
                System.out.println("API响应: " + jsonResponse);

                // 从JSON响应中提取二维码
                String qrCode = extractQRCodeFromResponse(jsonResponse);
                if (qrCode == null) {
                    System.err.println("无法从API获取二维码");
                    return null;
                }

                System.out.println("获取到二维码: " + qrCode);

                // 构建二维码图片URL
                String qrImageUrl = "https://mobilecodec.alipay.com/show.htm?code=" + qrCode + "&trans=false";
                System.out.println("二维码图片URL: " + qrImageUrl);
                return  qrImageUrl;
            } else {
                System.err.println("API调用失败，响应码: " + responseCode);
                return null;
            }


        } catch (Exception e) {
            System.err.println("解析URL参数失败: " + e.getMessage());

        }
        return null;
    }

    private String extractQRCodeFromResponse(String jsonResponse) {
        try {
            // 查找qrCodeUrl中的code参数
            // 模式: "qrCodeUrl":"https://mobilecodec.alipay.com/show.htm?code=ulx13218z2qn59rylvdgae7&amp;trans=false"
            Pattern pattern = Pattern.compile("\"qrCodeUrl\"\\s*:\\s*\"[^\"]*code=([^&\"]+)");
            Matcher matcher = pattern.matcher(jsonResponse);

            if (matcher.find()) {
                String code = matcher.group(1);
                System.out.println("从qrCodeUrl提取到二维码: " + code);
                return code;
            }

            // 备用模式: 直接查找code参数
            pattern = Pattern.compile("code=([a-z0-9]{15,30})");
            matcher = pattern.matcher(jsonResponse);

            if (matcher.find()) {
                String code = matcher.group(1);
                System.out.println("找到二维码: " + code);
                return code;
            }

            System.err.println("无法从响应中提取二维码");
            System.err.println("响应内容: " + jsonResponse);
            return null;

        } catch (Exception e) {
            System.err.println("提取二维码失败: " + e.getMessage());
            return null;
        }
    }


}
