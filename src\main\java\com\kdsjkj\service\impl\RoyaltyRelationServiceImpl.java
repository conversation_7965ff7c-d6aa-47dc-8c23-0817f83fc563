package com.kdsjkj.service.impl;

import com.kdsjkj.entity.RoyaltyRelation;
import com.kdsjkj.mapper.RoyaltyRelationMapper;
import com.kdsjkj.service.IRoyaltyRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 分账关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class RoyaltyRelationServiceImpl extends ServiceImpl<RoyaltyRelationMapper, RoyaltyRelation> implements IRoyaltyRelationService {

    @Override
    public String getRoyaltyUserId() {
        // 查询表中的第一条记录（表中只有一条数据）
        RoyaltyRelation relation = this.lambdaQuery()
                .last("LIMIT 1")
                .one();
        
        return relation != null ? relation.getUserId() : null;
    }

    @Override
    public String getRoyaltyUserIdByAppid(String appid) {
        // 根据APPID查询对应的分账接收方用户ID
        RoyaltyRelation relation = this.lambdaQuery()
                .last("LIMIT 1")
                .one();
        
        return relation != null ? relation.getUserId() : null;
    }

}
