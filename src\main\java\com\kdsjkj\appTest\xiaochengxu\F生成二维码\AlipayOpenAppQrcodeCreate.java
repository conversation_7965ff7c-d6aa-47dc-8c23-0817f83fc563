package com.kdsjkj.appTest.xiaochengxu.F生成二维码;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.response.AlipayOpenAppQrcodeCreateResponse;
import com.alipay.api.request.AlipayOpenAppQrcodeCreateRequest;
import com.alipay.api.domain.AlipayOpenAppQrcodeCreateModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayOpenAppQrcodeCreate {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenAppQrcodeCreateRequest request = new AlipayOpenAppQrcodeCreateRequest();
        AlipayOpenAppQrcodeCreateModel model = new AlipayOpenAppQrcodeCreateModel();
        
        // 设置跳转小程序的页面路径
        model.setUrlParam("pages/index/index");
        
        // 设置小程序的启动参数
        model.setQueryParam("x=1");
        
        // 设置码描述
        model.setDescribe("商户首页");
        
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenAppQrcodeCreateResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            System.out.println(diagnosisUrl);
        }
    }
}