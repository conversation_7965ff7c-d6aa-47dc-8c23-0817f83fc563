package com.kdsjkj.test;

/**
 * Version number extractor test
 */
public class VersionExtractorTest {

    /**
     * Extract the number after the last dot from version string
     * Example: 0.0.25 -> 25
     */
    private static int extractVersionNumber(String version) {
        if (version == null || version.trim().isEmpty()) {
            return 0;
        }

        // Find the last dot position
        int lastDotIndex = version.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // No dot, try to parse the whole string
            try {
                return Integer.parseInt(version.trim());
            } catch (NumberFormatException e) {
                System.out.println("Cannot parse version: " + version);
                return 0;
            }
        }

        // Extract the part after the last dot
        String lastPart = version.substring(lastDotIndex + 1);
        try {
            return Integer.parseInt(lastPart.trim());
        } catch (NumberFormatException e) {
            System.out.println("Cannot parse last part: " + lastPart);
            return 0;
        }
    }

    public static void main(String[] args) {
        // Test cases
        String[] testVersions = {
            "0.0.5",
            "0.0.25",
            "1.2.3",
            "0.0.100",
            "5",
            "invalid",
            "",
            null
        };

        System.out.println("Version extraction test:");
        System.out.println("========================");

        for (String version : testVersions) {
            int result = extractVersionNumber(version);
            System.out.printf("Input: %-10s -> Output: %d%n",
                version == null ? "null" : "\"" + version + "\"", result);
        }

        System.out.println("\nTest completed!");
    }
}
