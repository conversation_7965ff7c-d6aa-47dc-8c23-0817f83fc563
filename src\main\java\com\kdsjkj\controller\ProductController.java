package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.Product;
import com.kdsjkj.service.IProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import javax.annotation.PostConstruct;

/**
 * <p>
 * 商品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@RestController
@RequestMapping("/product")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class ProductController {

    @Autowired
    private IProductService productService;

    // 图片保存路径
    private static final String UPLOAD_PATH = "upload/products/";
    
    // 上传目录的绝对路径
    private String absoluteUploadPath;
    
    @PostConstruct
    public void init() {
        // 获取项目根目录的绝对路径
        String projectRoot = System.getProperty("user.dir");
        // 确保路径分隔符正确
        absoluteUploadPath = projectRoot + File.separator + UPLOAD_PATH.replace("/", File.separator);
        
        // 初始化上传目录，确保目录存在
        File uploadDir = new File(absoluteUploadPath);
        if (!uploadDir.exists()) {
            boolean created = uploadDir.mkdirs();
            log.info("初始化商品图片上传目录: {}, 创建结果: {}", uploadDir.getAbsolutePath(), created);
            
            if (!created) {
                log.error("无法创建上传目录: {}", uploadDir.getAbsolutePath());
            }
        } else {
            log.info("商品图片上传目录已存在: {}", uploadDir.getAbsolutePath());
        }
        
        // 验证目录权限
        if (uploadDir.exists()) {
            log.info("目录权限检查 - 可读: {}, 可写: {}", uploadDir.canRead(), uploadDir.canWrite());
        }
        
        log.info("项目根目录: {}", projectRoot);
        log.info("上传目录绝对路径: {}", absoluteUploadPath);
    }

    /**
     * 添加商品
     */
    @PostMapping("/add")
    public Result addProduct(
            @RequestParam("productImage") MultipartFile productImage,
            @RequestParam("productPrice") BigDecimal productPrice,
            @RequestParam("productName") String productName,
            @RequestParam("appid") String appid) {
        
        try {
            // 参数校验
            if (productImage == null || productImage.isEmpty()) {
                return Result.paramError("商品图片不能为空");
            }
            if (productPrice == null || productPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.paramError("商品价格必须大于0");
            }
            if (productName == null || productName.trim().isEmpty()) {
                return Result.paramError("商品名称不能为空");
            }
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid不能为空");
            }

            log.info("添加商品 - 商品名称: {}, 价格: {}, appid: {}", productName, productPrice, appid);

            // 保存图片文件
            String imageUrl = saveProductImage(productImage);
            if (imageUrl == null) {
                return Result.error("图片上传失败");
            }

            // 创建商品对象
            Product product = new Product();
            product.setProductName(productName.trim());
            product.setProductImageUrl(imageUrl);
            product.setProductPrice(productPrice);
            product.setProductAppid(appid.trim());
            product.setStockQuantity(0);
            product.setStatus(1); // 默认上架
            product.setCreateTime(LocalDateTime.now());
            product.setUpdateTime(LocalDateTime.now());

            // 保存到数据库
            boolean success = productService.save(product);
            if (success) {
                log.info("商品添加成功 - ID: {}, 商品名称: {}", product.getId(), productName);
                return Result.success("商品添加成功", product);
            } else {
                return Result.error("商品添加失败");
            }

        } catch (Exception e) {
            log.error("添加商品异常 - 商品名称: {}", productName, e);
            return Result.error("添加商品异常: " + e.getMessage());
        }
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteProduct(@PathVariable("id") Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("商品ID不能为空");
            }

            log.info("删除商品 - ID: {}", id);

            // 先查询商品是否存在
            Product product = productService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }

            // 删除商品
            boolean success = productService.removeById(id);
            if (success) {
                log.info("商品删除成功 - ID: {}, 商品名称: {}", id, product.getProductName());
                return Result.success("商品删除成功");
            } else {
                return Result.error("商品删除失败");
            }

        } catch (Exception e) {
            log.error("删除商品异常 - ID: {}", id, e);
            return Result.error("删除商品异常: " + e.getMessage());
        }
    }

    /**
     * 修改商品
     */
    @PutMapping("/update/{id}")
    public Result updateProduct(
            @PathVariable("id") Long id,
            @RequestParam(value = "productImage", required = false) MultipartFile productImage,
            @RequestParam(value = "productPrice", required = false) BigDecimal productPrice,
            @RequestParam(value = "productName", required = false) String productName,
            @RequestParam(value = "appid", required = false) String appid) {
        
        try {
            if (id == null || id <= 0) {
                return Result.paramError("商品ID不能为空");
            }

            log.info("修改商品 - ID: {}", id);

            // 先查询商品是否存在
            Product product = productService.getById(id);
            if (product == null) {
                return Result.error("商品不存在");
            }

            // 更新字段
            boolean hasUpdate = false;
            
            if (productName != null && !productName.trim().isEmpty()) {
                product.setProductName(productName.trim());
                hasUpdate = true;
            }
            
            if (productPrice != null && productPrice.compareTo(BigDecimal.ZERO) > 0) {
                product.setProductPrice(productPrice);
                hasUpdate = true;
            }
            
            if (appid != null && !appid.trim().isEmpty()) {
                product.setProductAppid(appid.trim());
                hasUpdate = true;
            }
            
            if (productImage != null && !productImage.isEmpty()) {
                String imageUrl = saveProductImage(productImage);
                if (imageUrl != null) {
                    product.setProductImageUrl(imageUrl);
                    hasUpdate = true;
                }
            }

            if (!hasUpdate) {
                return Result.paramError("至少需要更新一个字段");
            }

            product.setUpdateTime(LocalDateTime.now());

            // 更新到数据库
            boolean success = productService.updateById(product);
            if (success) {
                log.info("商品修改成功 - ID: {}, 商品名称: {}", id, product.getProductName());
                return Result.success("商品修改成功", product);
            } else {
                return Result.error("商品修改失败");
            }

        } catch (Exception e) {
            log.error("修改商品异常 - ID: {}", id, e);
            return Result.error("修改商品异常: " + e.getMessage());
        }
    }

    /**
     * 根据appid查询商品列表
     */
    @GetMapping("/list")
    public Result getProductList(@RequestParam("appid") String appid) {
        try {
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid不能为空");
            }

            log.info("查询商品列表 - appid: {}", appid);

            // 查询该appid下的所有商品
            List<Product> productList = productService.getProductsByAppid(appid);
            
            log.info("查询到商品数量: {} - appid: {}", productList.size(), appid);
            return Result.success("查询成功", productList);

        } catch (Exception e) {
            log.error("查询商品列表异常 - appid: {}", appid, e);
            return Result.error("查询商品列表异常: " + e.getMessage());
        }
    }

    /**
     * 保存商品图片
     */
    private String saveProductImage(MultipartFile file) {
        try {
            log.info("开始保存商品图片，原文件名: {}, 文件大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());

            // 验证文件
            if (file.isEmpty()) {
                throw new IOException("上传文件为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new IOException("只支持图片文件上传，当前文件类型: " + contentType);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = ".jpg"; // 默认扩展名
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = "product_" + UUID.randomUUID().toString() + extension;
            
            // 构建完整的文件路径 - 使用绝对路径
            File targetFile = new File(absoluteUploadPath, fileName);
            
            log.info("目标文件路径: {}", targetFile.getAbsolutePath());
            
            // 确保父目录存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                log.info("创建父目录: {}, 结果: {}", parentDir.getAbsolutePath(), created);
                
                if (!created && !parentDir.exists()) {
                    throw new IOException("无法创建上传目录: " + parentDir.getAbsolutePath());
                }
            }

            // 验证目录权限
            if (parentDir != null && (!parentDir.exists() || !parentDir.canWrite())) {
                throw new IOException("上传目录不可写: " + parentDir.getAbsolutePath());
            }
            
            // 保存文件
            file.transferTo(targetFile);
            
            // 验证文件是否保存成功
            if (!targetFile.exists() || targetFile.length() == 0) {
                throw new IOException("文件保存失败，目标文件不存在或大小为0");
            }
            
            // 构建用于Web访问的相对路径
            String webAccessPath = UPLOAD_PATH + fileName;
            
            log.info("图片保存成功 - 文件路径: {}, 文件大小: {} bytes, 访问路径: {}", 
                    targetFile.getAbsolutePath(), targetFile.length(), webAccessPath);
            
            // 返回相对路径用于Web访问
            return webAccessPath;
            
        } catch (IOException e) {
            log.error("保存图片失败: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("保存图片发生未知异常: {}", e.getMessage(), e);
            return null;
        }
    }

}
