package com.kdsjkj.task;

import com.kdsjkj.entity.BonusRewards;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.entity.Order;
import com.kdsjkj.entity.AgentFund;
import com.kdsjkj.service.IBonusRewardsService;
import com.kdsjkj.service.IAgentService;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.service.IAgentFundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 分红奖励计算定时任务
 */
@Slf4j
@Component
public class BonusRewardTask {

    @Autowired
    private IBonusRewardsService bonusRewardsService;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IAgentFundService agentFundService;

    // 分红奖励类型：4分红奖励
    private static final Integer BONUS_REWARD_TYPE = 4;

    /**
     * 每月1号凌晨2点执行分红奖励计算
     */
    @Scheduled(cron = "0 0 2 1 * ?")
    @Transactional(rollbackFor = Exception.class)
    public void calculateBonusRewards() {
        log.info("开始执行分红奖励计算任务");

        try {
            // 1. 获取上个月的时间范围
            YearMonth lastMonth = YearMonth.now().minusMonths(1);
            LocalDateTime startTime = lastMonth.atDay(1).atStartOfDay();
            LocalDateTime endTime = lastMonth.atEndOfMonth().atTime(23, 59, 59);
            String rewardMonth = lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            
            log.info("计算{}月分红奖励，时间范围：{} 至 {}", rewardMonth, startTime, endTime);

            // 2. 获取分红奖励规则
            BonusRewards rewardRule = bonusRewardsService.getBonusReward();
            if (rewardRule == null) {
                log.error("未找到分红奖励规则配置");
                return;
            }
            log.info("获取到分红奖励规则：流水金额阈值={}, 奖励金额={}", 
                    rewardRule.getTransactionAmount(), rewardRule.getRewardAmount());

            // 3. 获取所有代理
            List<Agent> allAgents = agentService.list();
            log.info("获取到代理总数：{}", allAgents.size());

            // 4. 遍历每个代理进行分红检查
            for (Agent agent : allAgents) {
                try {
                    processAgentBonusReward(agent, startTime, endTime, rewardMonth, rewardRule);
                } catch (Exception e) {
                    log.error("处理代理{}分红奖励时发生错误", agent.getUid(), e);
                }
            }

            log.info("分红奖励计算任务执行完成");
        } catch (Exception e) {
            log.error("分红奖励计算任务执行失败", e);
            throw e;
        }
    }

    /**
     * 处理单个代理的分红奖励
     */
    private void processAgentBonusReward(Agent agent, LocalDateTime startTime, LocalDateTime endTime, 
                                       String rewardMonth, BonusRewards rewardRule) {
        String agentUid = agent.getUid();
        log.info("开始处理代理{}的{}月分红奖励", agentUid, rewardMonth);

        // 1. 检查该代理该月是否已发放过分红奖励
        if (hasReceivedBonusReward(agentUid, rewardMonth)) {
            log.info("代理{}的{}月分红奖励已发放，跳过", agentUid, rewardMonth);
            return;
        }

        // 2. 获取代理的商户appid列表
        List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUid(agentUid);
        
        if (merchantAppIds.isEmpty()) {
            log.info("代理{}暂无商户，跳过分红奖励检查", agentUid);
            return;
        }
        
        log.info("代理{}共有{}个商户", agentUid, merchantAppIds.size());

        // 3. 计算代理手下所有商户的月流水总和
        BigDecimal totalTransactionAmount = calculateAgentMonthlyTransaction(merchantAppIds, startTime, endTime);
        log.info("代理{}的{}月总流水：{}", agentUid, rewardMonth, totalTransactionAmount);

        // 4. 检查是否达到分红阈值
        if (totalTransactionAmount.compareTo(rewardRule.getTransactionAmount()) >= 0) {
            log.info("代理{}达到分红阈值，月流水：{}，阈值：{}", agentUid, totalTransactionAmount, rewardRule.getTransactionAmount());
            
            // 5. 发放分红奖励
            grantBonusReward(agentUid, rewardMonth, totalTransactionAmount, merchantAppIds.size(), rewardRule);
        } else {
            log.info("代理{}未达到分红阈值，月流水：{}，阈值：{}", agentUid, totalTransactionAmount, rewardRule.getTransactionAmount());
        }
    }

    /**
     * 检查代理该月是否已收到分红奖励
     */
    private boolean hasReceivedBonusReward(String agentUid, String rewardMonth) {
        // 查询AgentFund表中该代理该月的分红奖励记录
        List<AgentFund> bonusRecords = agentFundService.lambdaQuery()
                .eq(AgentFund::getAgentUid, agentUid)
                .eq(AgentFund::getObtainType, BONUS_REWARD_TYPE)
                .ge(AgentFund::getObtainTime, YearMonth.parse(rewardMonth).atDay(1).atStartOfDay())
                .le(AgentFund::getObtainTime, YearMonth.parse(rewardMonth).atEndOfMonth().atTime(23, 59, 59))
                .list();
        
        return !bonusRecords.isEmpty();
    }

    /**
     * 计算代理手下所有商户的月流水总和
     */
    private BigDecimal calculateAgentMonthlyTransaction(List<String> merchantAppIds, LocalDateTime startTime, LocalDateTime endTime) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (String appId : merchantAppIds) {
            BigDecimal merchantAmount = calculateMerchantMonthlyTransaction(appId, startTime, endTime);
            totalAmount = totalAmount.add(merchantAmount);
            log.debug("商户{}月流水：{}", appId, merchantAmount);
        }
        
        return totalAmount;
    }

    /**
     * 计算单个商户的月流水
     */
    private BigDecimal calculateMerchantMonthlyTransaction(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getAppId, appId)
                .between(Order::getCreateTime, startTime, endTime)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS")  // 只统计成功的订单
                .list();

        return orders.stream()
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 发放分红奖励
     */
    private void grantBonusReward(String agentUid, String rewardMonth, BigDecimal totalTransactionAmount, 
                                int merchantCount, BonusRewards rewardRule) {
        log.info("开始发放分红奖励 - 代理：{}，月份：{}，流水：{}，商户数：{}，奖励金额：{}", 
                agentUid, rewardMonth, totalTransactionAmount, merchantCount, rewardRule.getRewardAmount());

        try {
            // 在AgentFund表中记录分红奖励
            boolean success = agentFundService.saveAgentFund(agentUid, rewardRule.getRewardAmount(), BONUS_REWARD_TYPE);
            if (!success) {
                log.error("分红奖励发放失败 - 代理：{}，月份：{}", agentUid, rewardMonth);
                throw new RuntimeException("分红奖励发放失败");
            }

            log.info("分红奖励发放成功 - 代理：{}，月份：{}，奖励金额：{}", agentUid, rewardMonth, rewardRule.getRewardAmount());
        } catch (Exception e) {
            log.error("发放分红奖励时发生异常 - 代理：{}，月份：{}", agentUid, rewardMonth, e);
            throw e;
        }
    }
}