package com.kdsjkj.appTest.xiaochengxu.类目.查询小程序类目;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayOpenMiniCategoryQueryRequest;
import com.alipay.api.response.AlipayOpenMiniCategoryQueryResponse;
import com.alipay.api.domain.AlipayOpenMiniCategoryQueryModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

//类目查询
public class AlipayOpenMiniCategoryQuery {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniCategoryQueryRequest request = new AlipayOpenMiniCategoryQueryRequest();
        AlipayOpenMiniCategoryQueryModel model = new AlipayOpenMiniCategoryQueryModel();
        
        // 设置是否过滤不可选类目
        model.setIsFilter(true);
        
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        AlipayOpenMiniCategoryQueryResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}