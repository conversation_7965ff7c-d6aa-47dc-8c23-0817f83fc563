<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.Order">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="out_trade_no" property="outTradeNo" />
        <result column="amount" property="amount" />
        <result column="trade_no" property="tradeNo" />
        <result column="trade_status" property="tradeStatus" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, out_trade_no, amount, trade_no, trade_status, create_time
    </sql>

</mapper>
