package com.kdsjkj.service;

import com.kdsjkj.entity.MerchantActivation;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户激活状态记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface IMerchantActivationService extends IService<MerchantActivation> {
    
    /**
     * 检查商户是否已激活
     * 
     * @param appId 商户APPID
     * @return 是否已激活
     */
    boolean isActivated(String appId);
    
    /**
     * 记录商户激活
     * 
     * @param appId 商户APPID
     * @param agentUid 代理UID
     * @param activationAmount 激活金额
     * @param rewardAmount 奖励金额
     * @return 激活记录
     */
    MerchantActivation recordActivation(String appId, String agentUid, BigDecimal activationAmount, BigDecimal rewardAmount);
    
    /**
     * 根据代理UID获取激活的商户数量
     * 
     * @param agentUid 代理UID
     * @return 激活商户数量
     */
    int getActivatedMerchantCount(String agentUid);
    
    /**
     * 根据代理UID和时间范围获取激活的商户数量
     * 
     * @param agentUid 代理UID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 激活商户数量
     */
    int getActivatedMerchantCountByTimeRange(String agentUid, LocalDateTime startTime, LocalDateTime endTime);
}
