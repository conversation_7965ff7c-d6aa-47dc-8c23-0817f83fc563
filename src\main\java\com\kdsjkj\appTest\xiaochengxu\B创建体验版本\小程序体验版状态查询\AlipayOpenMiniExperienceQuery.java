package com.kdsjkj.appTest.xiaochengxu.B创建体验版本.小程序体验版状态查询;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.response.AlipayOpenMiniExperienceQueryResponse;
import com.alipay.api.request.AlipayOpenMiniExperienceQueryRequest;
import com.alipay.api.domain.AlipayOpenMiniExperienceQueryModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;

//查询体验
public class AlipayOpenMiniExperienceQuery {

    public static void main(String[] args) throws AlipayApiException {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniExperienceQueryRequest request = new AlipayOpenMiniExperienceQueryRequest();
        AlipayOpenMiniExperienceQueryModel model = new AlipayOpenMiniExperienceQueryModel();
        
        // 设置商家小程序版本号
        model.setAppVersion("0.0.1");
        
        // 设置小程序端
        model.setBundleId("com.alipay.alipaywallet");
        
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
        AlipayOpenMiniExperienceQueryResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}