package com.kdsjkj.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;

/**
 * 支付宝授权服务接口
 */
public interface AlipayOauthService {
    
    /**
     * 通过授权码获取访问令牌
     * 
     * @param authCode 授权码
     * @return 访问令牌响应
     * @throws AlipayApiException 支付宝API异常
     */
    AlipaySystemOauthTokenResponse getAccessToken(String authCode) throws AlipayApiException;
} 