package com.kdsjkj.controller;

import com.kdsjkj.constant.Result;
import com.kdsjkj.dto.ProductExchangeRequest;
import com.kdsjkj.entity.AgentPoints;
import com.kdsjkj.entity.ProductExchange;
import com.kdsjkj.entity.ProductInfo;
import com.kdsjkj.service.IAgentPointsService;
import com.kdsjkj.service.IProductExchangeService;
import com.kdsjkj.service.IProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品兑换表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/product-exchange")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class ProductExchangeController {

    @Autowired
    private IProductExchangeService productExchangeService;

    @Autowired
    private IAgentPointsService agentPointsService;

    @Autowired
    private IProductInfoService productInfoService;

    /**
     * 兑换商品
     */
    @PostMapping("/exchange")
    @Transactional
    public Result exchangeProduct(@RequestBody ProductExchangeRequest request) {
        try {
            // 参数校验
            if (request.getUid() == null || request.getUid().trim().isEmpty()) {
                return Result.paramError("代理UID不能为空");
            }
            if (request.getProductId() == null || request.getProductId() <= 0) {
                return Result.paramError("商品ID不能为空");
            }
            if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                return Result.paramError("收货手机号不能为空");
            }
            if (request.getAddress() == null || request.getAddress().trim().isEmpty()) {
                return Result.paramError("收货地址不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.paramError("收货方姓名不能为空");
            }
            if (request.getRequiredPoints() == null || request.getRequiredPoints() <= 0) {
                return Result.paramError("所需积分不能为空且必须大于0");
            }

            log.info("开始兑换商品 - UID: {}, 商品ID: {}, 所需积分: {}", 
                    request.getUid(), request.getProductId(), request.getRequiredPoints());

            // 1. 验证商品是否存在
            ProductInfo productInfo = productInfoService.getById(request.getProductId());
            if (productInfo == null) {
                return Result.error("商品不存在");
            }

            // 2. 验证商品所需积分是否匹配
            if (!request.getRequiredPoints().equals(productInfo.getRequiredPoints())) {
                return Result.error("商品所需积分不匹配");
            }

            // 3. 查询代理积分
            AgentPoints agentPoints = agentPointsService.lambdaQuery()
                    .eq(AgentPoints::getUid, request.getUid())
                    .one();

            if (agentPoints == null) {
                return Result.error("代理积分不足，当前积分：0，所需积分：" + request.getRequiredPoints());
            }

            // 4. 验证积分是否足够
            if (agentPoints.getPoints() < request.getRequiredPoints()) {
                return Result.error("代理积分不足，当前积分：" + agentPoints.getPoints() + 
                        "，所需积分：" + request.getRequiredPoints());
            }

            // 5. 扣除积分
            int newPoints = agentPoints.getPoints() - request.getRequiredPoints();
            agentPoints.setPoints(newPoints);
            boolean updatePointsResult = agentPointsService.updateById(agentPoints);
            
            if (!updatePointsResult) {
                log.error("扣除代理积分失败 - UID: {}, 扣除积分: {}", request.getUid(), request.getRequiredPoints());
                return Result.error("扣除积分失败");
            }

            log.info("扣除代理积分成功 - UID: {}, 扣除积分: {}, 剩余积分: {}", 
                    request.getUid(), request.getRequiredPoints(), newPoints);

            // 6. 创建兑换记录
            ProductExchange productExchange = new ProductExchange();
            productExchange.setUid(request.getUid().trim());
            productExchange.setProductId(request.getProductId());
            productExchange.setPhone(request.getPhone().trim());
            productExchange.setAddress(request.getAddress().trim());
            productExchange.setName(request.getName().trim());
            productExchange.setStatus(0); // 0-待处理
            productExchange.setExchangeTime(LocalDateTime.now());

            boolean saveResult = productExchangeService.save(productExchange);
            
            if (!saveResult) {
                log.error("创建兑换记录失败 - UID: {}, 商品ID: {}", request.getUid(), request.getProductId());
                return Result.error("创建兑换记录失败");
            }

            log.info("商品兑换成功 - 兑换ID: {}, UID: {}, 商品ID: {}, 商品名称: {}, 扣除积分: {}", 
                    productExchange.getId(), request.getUid(), request.getProductId(), 
                    productInfo.getName(), request.getRequiredPoints());

            return Result.success("商品兑换成功", productExchange);

        } catch (Exception e) {
            log.error("兑换商品异常 - UID: {}, 商品ID: {}", request.getUid(), request.getProductId(), e);
            return Result.error("兑换商品异常: " + e.getMessage());
        }
    }

    /**
     * 根据UID查询兑换记录
     */
    @GetMapping("/list-by-uid/{uid}")
    public Result getExchangeListByUid(@PathVariable("uid") String uid) {
        try {
            if (uid == null || uid.trim().isEmpty()) {
                return Result.paramError("代理UID不能为空");
            }

            log.info("查询兑换记录 - UID: {}", uid);

            // 查询该代理的所有兑换记录
            List<ProductExchange> exchangeList = productExchangeService.lambdaQuery()
                    .eq(ProductExchange::getUid, uid)
                    .orderByDesc(ProductExchange::getExchangeTime)
                    .list();

            log.info("查询到兑换记录数量: {} - UID: {}", exchangeList.size(), uid);
            return Result.success("查询成功", exchangeList);

        } catch (Exception e) {
            log.error("查询兑换记录异常 - UID: {}", uid, e);
            return Result.error("查询兑换记录异常: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询兑换记录详情
     */
    @GetMapping("/get/{id}")
    public Result getExchangeById(@PathVariable("id") Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.paramError("兑换记录ID不能为空");
            }

            log.info("查询兑换记录详情 - ID: {}", id);

            ProductExchange productExchange = productExchangeService.getById(id);
            if (productExchange == null) {
                return Result.error("兑换记录不存在");
            }

            log.info("查询兑换记录详情成功 - ID: {}, UID: {}", id, productExchange.getUid());
            return Result.success("查询成功", productExchange);

        } catch (Exception e) {
            log.error("查询兑换记录详情异常 - ID: {}", id, e);
            return Result.error("查询兑换记录详情异常: " + e.getMessage());
        }
    }

    /**
     * 查询所有兑换记录（包含商品名称）
     */
    @GetMapping("/list")
    public Result getAllExchangeList() {
        try {
            log.info("查询所有兑换记录");

            // 查询所有兑换记录
            List<ProductExchange> exchangeList = productExchangeService.list();

            // 获取所有商品ID
            List<Long> productIds = exchangeList.stream()
                    .map(ProductExchange::getProductId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询商品信息
            Map<Long, String> productNameMap = new HashMap<>();
            if (!productIds.isEmpty()) {
                List<ProductInfo> productInfoList = productInfoService.listByIds(productIds);
                for (ProductInfo productInfo : productInfoList) {
                    productNameMap.put(productInfo.getId(), productInfo.getName());
                }
            }

            // 构建返回结果，包含商品名称
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (ProductExchange exchange : exchangeList) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("id", exchange.getId());
                resultMap.put("uid", exchange.getUid());
                resultMap.put("productId", exchange.getProductId());
                resultMap.put("productName", productNameMap.get(exchange.getProductId()));
                resultMap.put("phone", exchange.getPhone());
                resultMap.put("address", exchange.getAddress());
                resultMap.put("name", exchange.getName());
                resultMap.put("status", exchange.getStatus());
                resultMap.put("exchangeTime", exchange.getExchangeTime());
                resultList.add(resultMap);
            }

            log.info("查询到兑换记录数量: {}", resultList.size());
            return Result.success("查询成功", resultList);

        } catch (Exception e) {
            log.error("查询所有兑换记录异常", e);
            return Result.error("查询所有兑换记录异常: " + e.getMessage());
        }
    }

    /**
     * 根据UID查询兑换记录（包含商品名称）
     */
    @GetMapping("/list-by-uid-with-product/{uid}")
    public Result getExchangeListByUidWithProduct(@PathVariable("uid") String uid) {
        try {
            if (uid == null || uid.trim().isEmpty()) {
                return Result.paramError("代理UID不能为空");
            }

            log.info("查询兑换记录（包含商品名称） - UID: {}", uid);

            // 查询该代理的所有兑换记录
            List<ProductExchange> exchangeList = productExchangeService.lambdaQuery()
                    .eq(ProductExchange::getUid, uid)
                    .orderByDesc(ProductExchange::getExchangeTime)
                    .list();

            // 获取所有商品ID
            List<Long> productIds = exchangeList.stream()
                    .map(ProductExchange::getProductId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询商品信息
            Map<Long, String> productNameMap = new HashMap<>();
            if (!productIds.isEmpty()) {
                List<ProductInfo> productInfoList = productInfoService.listByIds(productIds);
                for (ProductInfo productInfo : productInfoList) {
                    productNameMap.put(productInfo.getId(), productInfo.getName());
                }
            }

            // 构建返回结果，包含商品名称
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (ProductExchange exchange : exchangeList) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("id", exchange.getId());
                resultMap.put("uid", exchange.getUid());
                resultMap.put("productId", exchange.getProductId());
                resultMap.put("productName", productNameMap.get(exchange.getProductId()));
                resultMap.put("phone", exchange.getPhone());
                resultMap.put("address", exchange.getAddress());
                resultMap.put("name", exchange.getName());
                resultMap.put("status", exchange.getStatus());
                resultMap.put("exchangeTime", exchange.getExchangeTime());
                resultList.add(resultMap);
            }

            log.info("查询到兑换记录数量: {} - UID: {}", resultList.size(), uid);
            return Result.success("查询成功", resultList);

        } catch (Exception e) {
            log.error("查询兑换记录异常 - UID: {}", uid, e);
            return Result.error("查询兑换记录异常: " + e.getMessage());
        }
    }

}
