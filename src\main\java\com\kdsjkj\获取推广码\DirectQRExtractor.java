package com.kdsjkj.获取推广码;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class DirectQRExtractor {
    
    private static final String OUTPUT_DIR = "qr_images";
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
    
    public static void main(String[] args) {
        // 从原始URL中提取参数
        String originalUrl = "https://b.alipay.com/page/fw-portal/itemQrCodeToIsv?marketCode=OFFLINE_PROMOTION&merchandiseId=CM010301000000292210&ticket=2052zvb&callback=https://pay.yulinxinxi.com/api/alipay/health&appName=";
        
        DirectQRExtractor extractor = new DirectQRExtractor();
        try {
            String qrImagePath = extractor.extractQRCodeDirect(originalUrl);
            if (qrImagePath != null) {
                System.out.println("二维码提取成功！");
                System.out.println("保存路径: " + qrImagePath);
            } else {
                System.out.println("二维码提取失败");
            }
        } catch (Exception e) {
            System.err.println("程序执行出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 直接通过API提取二维码
     * @param originalUrl 原始的支付宝页面URL
     * @return 保存的二维码图片路径
     */
    public String extractQRCodeDirect(String originalUrl) {
        try {
            // 创建输出目录
            createOutputDirectory();
            
            // 解析URL参数
            URLParameters params = parseUrlParameters(originalUrl);
            if (params == null) {
                System.err.println("无法解析URL参数");
                return null;
            }
            
//            System.out.println("   解析到的参数:");
//            System.out.println("   merchandiseId: " + params.merchandiseId);
//            System.out.println("   marketCode: " + params.marketCode);
//            System.out.println("   ticket: " + params.ticket);
//            System.out.println("   callback: " + params.callback);
//            System.out.println("   appName: " + params.appName);
            
            // 调用支付宝API获取二维码信息
            String qrCode = getQRCodeFromAPI(params);
            if (qrCode == null) {
                System.err.println("无法从API获取二维码");
                return null;
            }
            
            System.out.println("获取到二维码: " + qrCode);
            
            // 构建二维码图片URL
            String qrImageUrl = "https://mobilecodec.alipay.com/show.htm?code=" + qrCode + "&trans=false";
            System.out.println("二维码图片URL: " + qrImageUrl);
            
            // 下载二维码图片
            return downloadQRImage(qrImageUrl);
            
        } catch (Exception e) {
            System.err.println(" 直接提取过程中出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 解析URL参数
     */
    private URLParameters parseUrlParameters(String url) {
        try {
            URL parsedUrl = new URL(url);
            String query = parsedUrl.getQuery();
            
            URLParameters params = new URLParameters();
            String[] pairs = query.split("&");
            
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0];
                    String value = URLDecoder.decode(keyValue[1], "UTF-8");
                    
                    switch (key) {
                        case "merchandiseId":
                            params.merchandiseId = value;
                            break;
                        case "marketCode":
                            params.marketCode = value;
                            break;
                        case "ticket":
                            params.ticket = value;
                            break;
                        case "callback":
                            params.callback = value;
                            break;
                        case "appName":
                            params.appName = value;
                            break;
                    }
                }
            }
            
            return params;
        } catch (Exception e) {
            System.err.println("解析URL参数失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 调用支付宝API获取二维码
     */
    private String getQRCodeFromAPI(URLParameters params) {
        try {
            // 构建API URL
            String apiUrl = "https://app.alipay.com/alipaymarket/itemQrCodeToIsv.json" +
                    "?_input_charset=utf-8" +
                    "&merchandiseId=" + URLEncoder.encode(params.merchandiseId, "UTF-8") +
                    "&marketCode=" + URLEncoder.encode(params.marketCode, "UTF-8") +
                    "&ticket=" + URLEncoder.encode(params.ticket, "UTF-8") +
                    "&callback=" + URLEncoder.encode(params.callback, "UTF-8") +
                    "&appName=" + URLEncoder.encode(params.appName, "UTF-8");
            
            System.out.println("调用API: " + apiUrl);
            
            // 发送HTTP请求
            HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", USER_AGENT);
            connection.setRequestProperty("Accept", "application/json, text/plain, */*");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            connection.setRequestProperty("Referer", "https://b.alipay.com/");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("📡 API响应码: " + responseCode);
            
            if (responseCode == 200) {
                // 读取响应
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String jsonResponse = response.toString();
                System.out.println("API响应: " + jsonResponse);
                
                // 从JSON响应中提取二维码
                return extractQRCodeFromResponse(jsonResponse);
            } else {
                System.err.println("API调用失败，响应码: " + responseCode);
                return null;
            }
            
        } catch (Exception e) {
            System.err.println("API调用出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 从API响应中提取二维码
     */
    private String extractQRCodeFromResponse(String jsonResponse) {
        try {
            // 查找qrCodeUrl中的code参数
            // 模式: "qrCodeUrl":"https://mobilecodec.alipay.com/show.htm?code=ulx13218z2qn59rylvdgae7&amp;trans=false"
            Pattern pattern = Pattern.compile("\"qrCodeUrl\"\\s*:\\s*\"[^\"]*code=([^&\"]+)");
            Matcher matcher = pattern.matcher(jsonResponse);

            if (matcher.find()) {
                String code = matcher.group(1);
                System.out.println("从qrCodeUrl提取到二维码: " + code);
                return code;
            }

            // 备用模式: 直接查找code参数
            pattern = Pattern.compile("code=([a-z0-9]{15,30})");
            matcher = pattern.matcher(jsonResponse);

            if (matcher.find()) {
                String code = matcher.group(1);
                System.out.println("找到二维码: " + code);
                return code;
            }

            System.err.println("无法从响应中提取二维码");
            System.err.println("响应内容: " + jsonResponse);
            return null;

        } catch (Exception e) {
            System.err.println("提取二维码失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 下载二维码图片
     */
    private String downloadQRImage(String qrImageUrl) {
        try {
            System.out.println("开始下载二维码图片...");
            
            // 首先访问二维码页面，可能会重定向到实际图片
            HttpURLConnection connection = (HttpURLConnection) new URL(qrImageUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", USER_AGENT);
            connection.setInstanceFollowRedirects(true);
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("图片请求响应码: " + responseCode);
            
            if (responseCode == 200) {
                String contentType = connection.getContentType();
                System.out.println("内容类型: " + contentType);
                
                // 生成文件名
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                String fileName = "direct_qr_" + timestamp + ".png";
                String filePath = OUTPUT_DIR + File.separator + fileName;
                
                // 下载文件
                try (InputStream in = connection.getInputStream()) {
                    Files.copy(in, Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);
                }
                
                System.out.println("二维码图片下载完成: " + filePath);
                return filePath;
            } else {
                System.err.println("下载图片失败，响应码: " + responseCode);
                return null;
            }
            
        } catch (Exception e) {
            System.err.println("下载图片出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 创建输出目录
     */
    private void createOutputDirectory() {
        try {
            Files.createDirectories(Paths.get(OUTPUT_DIR));
        } catch (IOException e) {
            System.err.println("创建输出目录失败: " + e.getMessage());
        }
    }
    
    /**
     * URL参数类
     */
    public static class URLParameters {
        public String merchandiseId;
        public String marketCode;
        public String ticket;
        public String callback;
        public String appName;
    }
}
