package com.kdsjkj.controller;

import com.kdsjkj.entity.AchievementRewards;
import com.kdsjkj.service.IAchievementRewardsService;
import com.kdsjkj.dto.UpdateAchievementRewardRequest;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * <p>
 * 达标奖励表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/api/achievement-rewards")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class AchievementRewardsController {

    @Autowired
    private IAchievementRewardsService achievementRewardsService;
    
    /**
     * 获取达标奖励配置
     */
    @GetMapping("/config")
    public ResponseEntity<HashMap<String, Object>> getConfig() {
        HashMap<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始获取达标奖励配置");
            
            AchievementRewards config = achievementRewardsService.getAchievementReward();
            
            response.put("success", true);
            response.put("data", config);
            response.put("message", "获取达标奖励配置成功");
            
            log.info("获取达标奖励配置成功");
            
        } catch (Exception e) {
            log.error("获取达标奖励配置失败", e);
            response.put("success", false);
            response.put("message", "获取达标奖励配置失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 更新达标奖励配置
     */
    @PutMapping("/config/{id}")
    public ResponseEntity<HashMap<String, Object>> updateConfig(
            @PathVariable Long id,
            @RequestBody UpdateAchievementRewardRequest request) {
        
        HashMap<String, Object> response = new HashMap<>();
        
        try {
            log.info("开始更新达标奖励配置 - ID: {}", id);
            
            // 参数验证
            if (request.getMinMerchantCount() == null || request.getMinMerchantCount() < 1) {
                response.put("success", false);
                response.put("message", "最低商户数量必须大于0");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (request.getMinMonthlyIncome() == null || request.getMinMonthlyIncome().compareTo(BigDecimal.ZERO) <= 0) {
                response.put("success", false);
                response.put("message", "每月最低收入必须大于0");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (request.getRewardAmount() == null || request.getRewardAmount().compareTo(BigDecimal.ZERO) <= 0) {
                response.put("success", false);
                response.put("message", "奖励金额必须大于0");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 更新配置
            AchievementRewards updatedConfig = achievementRewardsService.updateAchievementReward(id, request);
            
            response.put("success", true);
            response.put("data", updatedConfig);
            response.put("message", "更新达标奖励配置成功");
            
            log.info("更新达标奖励配置成功 - ID: {}", id);
            
        } catch (Exception e) {
            log.error("更新达标奖励配置失败 - ID: {}", id, e);
            response.put("success", false);
            response.put("message", "更新达标奖励配置失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
}
