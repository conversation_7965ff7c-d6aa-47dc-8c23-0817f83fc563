package com.kdsjkj.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kdsjkj.entity.GetUserId;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 获取用户ID相关配置Mapper接口
 */
@Mapper
public interface GetUserIdMapper extends BaseMapper<GetUserId> {
    
    /**
     * 根据应用ID获取最新的配置
     */
    @Select("SELECT * FROM getuserid WHERE app_id = #{appId} AND status = 'ACTIVE' ORDER BY create_time DESC LIMIT 1")
    GetUserId getLatestByAppId(@Param("appId") String appId);
    
    /**
     * 根据授权令牌获取配置
     */
    @Select("SELECT * FROM getuserid WHERE app_auth_token = #{appAuthToken} AND status = 'ACTIVE' LIMIT 1")
    GetUserId getByAppAuthToken(@Param("appAuthToken") String appAuthToken);
    
    /**
     * 根据用户ID获取配置
     */
    @Select("SELECT * FROM getuserid WHERE user_id = #{userId} AND status = 'ACTIVE' ORDER BY create_time DESC LIMIT 1")
    GetUserId getByUserId(@Param("userId") String userId);
    
    /**
     * 更新访问令牌相关信息
     */
    @Update("UPDATE getuserid SET access_token = #{accessToken}, user_id = #{userId}, " +
            "expires_in = #{expiresIn}, refresh_token = #{refreshToken}, re_expires_in = #{reExpiresIn}, " +
            "response_body = #{responseBody}, update_time = NOW() WHERE id = #{id}")
    int updateTokenInfo(@Param("id") Long id, 
                       @Param("accessToken") String accessToken,
                       @Param("userId") String userId,
                       @Param("expiresIn") Long expiresIn,
                       @Param("refreshToken") String refreshToken,
                       @Param("reExpiresIn") Long reExpiresIn,
                       @Param("responseBody") String responseBody);
    
    /**
     * 更新错误信息
     */
    @Update("UPDATE getuserid SET error_code = #{errorCode}, error_msg = #{errorMsg}, " +
            "status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateErrorInfo(@Param("id") Long id,
                       @Param("errorCode") String errorCode,
                       @Param("errorMsg") String errorMsg,
                       @Param("status") String status);
    
    /**
     * 获取任何有效的app_auth_token配置（备用方法）
     */
    @Select("SELECT * FROM getuserid WHERE app_auth_token IS NOT NULL AND status = 'ACTIVE' ORDER BY create_time DESC LIMIT 1")
    GetUserId getAnyValidConfig();
} 