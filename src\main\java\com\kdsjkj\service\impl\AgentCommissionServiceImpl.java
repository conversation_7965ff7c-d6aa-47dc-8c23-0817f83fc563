package com.kdsjkj.service.impl;

import com.kdsjkj.entity.Agent;
import com.kdsjkj.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 代理分润计算服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class AgentCommissionServiceImpl implements IAgentCommissionService {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private IAgentFundService agentFundService;

    // 分润奖励类型：3分润奖励
    private static final Integer COMMISSION_TYPE = 3;

    @Override
    @Transactional
    public boolean calculateAndSaveAgentCommission(String tradeNo, String totalAmount) {
        log.info("🎯 开始计算代理分润 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);
        
        try {
            // 1. 通过tradeNo在order表中查找对应appid
            String appId = orderService.getAppIdByTradeNo(tradeNo);
            if (appId == null || appId.trim().isEmpty()) {
                log.warn("❌ 未找到对应的appId - 交易号: {}", tradeNo);
                return false;
            }
            log.info("📱 找到对应appId: {}", appId);

            // 2. 通过appid在service_market_order表中匹配consumer_miniAppId找到对应的agent_uid
            String agentUid = serviceMarketOrderService.getAgentUidByAppid(appId);
            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.warn("❌ 未找到对应的代理UID - appId: {}", appId);
                return false;
            }
            log.info("👤 找到对应代理UID: {}", agentUid);

            // 3. 通过agent_uid在agent表中匹配uid，找到这个appid的代理以及上级代理链
            List<Agent> agentChain = buildAgentChain(agentUid);
            if (agentChain.isEmpty()) {
                log.warn("❌ 未找到代理链 - 代理UID: {}", agentUid);
                return false;
            }
            log.info("🔗 构建代理链成功，共{}级代理", agentChain.size());

            // 4. 计算每级代理的分润并保存
            BigDecimal totalAmountDecimal = new BigDecimal(totalAmount);
            return calculateAndSaveCommissions(agentChain, totalAmountDecimal);

        } catch (Exception e) {
            log.error("❌ 计算代理分润异常 - 交易号: {}", tradeNo, e);
            return false;
        }
    }

    /**
     * 构建代理链（从下级到上级）
     */
    private List<Agent> buildAgentChain(String startAgentUid) {
        List<Agent> agentChain = new ArrayList<>();
        String currentUid = startAgentUid;
        
        while (currentUid != null && !currentUid.trim().isEmpty()) {
            Agent agent = agentService.getAgentByUid(currentUid);
            if (agent == null) {
                log.warn("⚠️ 代理不存在，中断链条构建 - UID: {}", currentUid);
                break;
            }
            
            agentChain.add(agent);
            log.info("🔗 添加代理到链条 - UID: {}, 姓名: {}, 费率: {}%", 
                    agent.getUid(), agent.getName(), agent.getRate());
            
            // 移动到上级代理
            currentUid = agent.getParentUid();
        }
        
        return agentChain;
    }

    /**
     * 计算并保存各级代理分润（不包括顶级代理）
     */
    private boolean calculateAndSaveCommissions(List<Agent> agentChain, BigDecimal totalAmount) {
        boolean allSuccess = true;
        
        // 如果代理链少于3级，则无需分润（至少需要3级：下级-中级-顶级，分润给中级）
        if (agentChain.size() < 3) {
            log.info("ℹ️ 代理链级数不足3级，无需分润 - 当前级数: {}", agentChain.size());
            return true;
        }
        
        // 从下级到上级计算分润，但不包括顶级代理（下一级rate - 上一级rate）
        // agentChain.size() - 2 确保不会给顶级代理分润
        for (int i = 0; i < agentChain.size() - 2; i++) {
            Agent lowerAgent = agentChain.get(i);      // 下级代理
            Agent upperAgent = agentChain.get(i + 1);  // 上级代理（非顶级）
            
            // 计算分润：(下一级rate - 上一级rate) / 100 * 本次交易金额
            BigDecimal commissionAmount = calculateCommissionAmount(
                    lowerAgent.getRate(), 
                    upperAgent.getRate(), 
                    totalAmount
            );
            
            if (commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 分润给上级代理（非顶级代理）
                boolean saveResult = agentFundService.saveAgentFund(
                        upperAgent.getUid(), 
                        commissionAmount, 
                        COMMISSION_TYPE
                );
                
                if (saveResult) {
                    log.info("✅ 代理分润保存成功 - 上级代理: {} ({}), 下级代理: {} ({}), 分润金额: {}", 
                            upperAgent.getName(), upperAgent.getUid(),
                            lowerAgent.getName(), lowerAgent.getUid(),
                            commissionAmount);
                } else {
                    log.error("❌ 代理分润保存失败 - 上级代理: {}, 分润金额: {}", 
                            upperAgent.getUid(), commissionAmount);
                    allSuccess = false;
                }
            } else {
                log.info("⚠️ 分润金额为0或负数，跳过 - 上级代理: {}, 下级代理: {}, 分润金额: {}", 
                        upperAgent.getUid(), lowerAgent.getUid(), commissionAmount);
            }
        }
        
        log.info("🔒 顶级代理不参与分润计算 - 顶级代理: {} ({})", 
                agentChain.get(agentChain.size() - 1).getName(),
                agentChain.get(agentChain.size() - 1).getUid());
        
        return allSuccess;
    }

    @Override
    public BigDecimal calculateCommissionAmount(BigDecimal lowerRate, BigDecimal upperRate, BigDecimal totalAmount) {
        if (lowerRate == null || upperRate == null || totalAmount == null) {
            return BigDecimal.ZERO;
        }
        
        // 计算费率差：下级费率 - 上级费率
        BigDecimal rateDiff = lowerRate.subtract(upperRate);
        
        // 如果费率差小于等于0，则没有分润
        if (rateDiff.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 计算分润金额：费率差 / 100 * 交易金额，保留小数点后两位，不四舍五入
        BigDecimal commissionAmount = rateDiff
                .divide(new BigDecimal("100"), 4, RoundingMode.DOWN)
                .multiply(totalAmount)
                .setScale(2, RoundingMode.DOWN);
        
        log.debug("💰 分润计算 - 下级费率: {}%, 上级费率: {}%, 费率差: {}%, 交易金额: {}, 分润金额: {}", 
                lowerRate, upperRate, rateDiff, totalAmount, commissionAmount);
        
        return commissionAmount;
    }

    @Override
    @Transactional
    public boolean calculateAndSaveBottomAgentCommission(String tradeNo, String totalAmount) {
        log.info("🎯 开始计算最底层代理分润 - 交易号: {}, 总金额: {}", tradeNo, totalAmount);
        
        try {
            // 1. 通过tradeNo在order表中查找对应appid
            String appId = orderService.getAppIdByTradeNo(tradeNo);
            if (appId == null || appId.trim().isEmpty()) {
                log.warn("❌ 未找到对应的appId - 交易号: {}", tradeNo);
                return false;
            }
            log.info("📱 找到对应appId: {}", appId);

            // 2. 通过appid获取商户费率
            BigDecimal merchantRate = serviceMarketOrderService.getMerchantRateByAppid(appId);
            if (merchantRate == null) {
                log.warn("❌ 未找到对应的商户费率 - appId: {}", appId);
                return false;
            }
            log.info("💰 找到商户费率: {}%", merchantRate);

            // 3. 通过appid在service_market_order表中匹配consumer_miniAppId找到对应的agent_uid
            String agentUid = serviceMarketOrderService.getAgentUidByAppid(appId);
            if (agentUid == null || agentUid.trim().isEmpty()) {
                log.warn("❌ 未找到对应的代理UID - appId: {}", appId);
                return false;
            }
            log.info("👤 找到最底层代理UID: {}", agentUid);

            // 4. 获取最底层代理信息
            Agent bottomAgent = agentService.getAgentByUid(agentUid);
            if (bottomAgent == null) {
                log.warn("❌ 未找到最底层代理信息 - UID: {}", agentUid);
                return false;
            }
            log.info("👤 最底层代理信息 - 姓名: {}, 费率: {}%", bottomAgent.getName(), bottomAgent.getRate());

            // 5. 计算最底层代理分润：(商户费率 - 最底层代理费率) / 100 * 交易金额
            BigDecimal totalAmountDecimal = new BigDecimal(totalAmount);
            BigDecimal commissionAmount = calculateCommissionAmount(merchantRate, bottomAgent.getRate(), totalAmountDecimal);
            
            if (commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 6. 保存最底层代理分润
                boolean saveResult = agentFundService.saveAgentFund(
                        bottomAgent.getUid(), 
                        commissionAmount, 
                        COMMISSION_TYPE
                );
                
                if (saveResult) {
                    log.info("✅ 最底层代理分润保存成功 - 代理: {} ({}), 商户费率: {}%, 代理费率: {}%, 分润金额: {}", 
                            bottomAgent.getName(), bottomAgent.getUid(), 
                            merchantRate, bottomAgent.getRate(), commissionAmount);
                    return true;
                } else {
                    log.error("❌ 最底层代理分润保存失败 - 代理: {}, 分润金额: {}", 
                            bottomAgent.getUid(), commissionAmount);
                    return false;
                }
            } else {
                log.info("⚠️ 最底层代理分润金额为0或负数，无需分润 - 商户费率: {}%, 代理费率: {}%, 分润金额: {}", 
                        merchantRate, bottomAgent.getRate(), commissionAmount);
                return true; // 返回true，因为这是正常情况
            }

        } catch (Exception e) {
            log.error("❌ 计算最底层代理分润异常 - 交易号: {}", tradeNo, e);
            return false;
        }
    }

} 