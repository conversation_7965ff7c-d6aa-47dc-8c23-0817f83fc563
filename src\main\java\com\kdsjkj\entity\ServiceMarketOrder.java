package com.kdsjkj.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务市场订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("service_market_order")
public class ServiceMarketOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订购时间
     */
    @TableField("order_time")
    private LocalDateTime orderTime;

    /**
     * 服务名称
     */
    private String title;

    /**
     * 商户名称
     */
    private String name;

    /**
     * 商户的UID
     */
    @TableField("merchant_pid")
    private String merchantPid;

    /**
     * 联系人
     */
    private String contactor;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 门店数
     */
    @TableField("order_item_num")
    private Integer orderItemNum;

    /**
     * 订单总价
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 所属业务分类
     */
    @TableField("biz_type")
    private String bizType;

    /**
     * 服务itemCode
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 规格名称
     */
    private String specifications;

    /**
     * 套餐数量
     */
    @TableField("package_count")
    private Integer packageCount;

    /**
     * 订购周期（天）
     */
    @TableField("period_day")
    private Integer periodDay;

    /**
     * 订单授权码
     */
    @TableField("order_ticket")
    private String orderTicket;

    /**
     * 服务码
     */
    @TableField("service_code")
    private String serviceCode;

    /**
     * 服务商标识的商家标识
     */
    @TableField("isv_ticket")
    private String isvTicket;

    /**
     * 小程序的APPID
     */
    @TableField("consumer_miniAppId")
    private String consumerMiniappid;

    /**
     * 小程序的名称
     */
    @TableField("consumer_appName")
    private String consumerAppname;

    /**
     * 商家门店ID
     */
    @TableField("merchant_shop_id")
    private String merchantShopId;

    /**
     * 小程序发布的端信息
     */
    @TableField("miniapp_release_bundle")
    private String miniappReleaseBundle;

    /**
     * 订购关联推广主账号名称
     */
    @TableField("promoter_master_name")
    private String promoterMasterName;

    /**
     * 订购关联推广主账号PID
     */
    @TableField("promoter_master_pid")
    private String promoterMasterPid;

    /**
     * 是否极速开通订单
     */
    @TableField("is_fast_open")
    private Boolean isFastOpen;

    /**
     * 操作代理名称
     */
    @TableField("agent_uid")
    private String agentUid;

    /**
     * 对应激活码名称
     */
    @TableField("active_code")
    private String activeCode;

    /**
     * 费率(百分比，如0.0060表示0.6%)
     */
    private BigDecimal rate;


}
