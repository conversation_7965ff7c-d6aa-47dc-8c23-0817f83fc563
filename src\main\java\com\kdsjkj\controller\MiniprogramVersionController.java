package com.kdsjkj.controller;


import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.MiniprogramVersion;
import com.kdsjkj.service.IMiniprogramVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/miniprogram-version")
public class MiniprogramVersionController {
    
    @Autowired
    private IMiniprogramVersionService miniprogramVersionService;
    
    /**
     * 通过appid获取下一个版本号
     * @param appid 小程序appid
     * @return 下一个版本号信息
     */
    @GetMapping("/getNextVersion")
    public Result getNextVersionByAppid(@RequestParam String appid) {
        // 查询当前版本记录，选出版本号最大的version
        MiniprogramVersion version = miniprogramVersionService.lambdaQuery()
                .eq(MiniprogramVersion::getAppid, appid)
                .orderByDesc(MiniprogramVersion::getVersion)
                .last("LIMIT 1")
                .one();
        
        String nextVersion;
        if (version == null) {
            // 不存在记录，下一个版本号为1
            nextVersion = "0.0.1";
        } else {
            // 存在记录，下一个版本号为当前版本+1
            nextVersion = String.format("0.0.%d", version.getVersion() + 1);
        }
        
        return Result.success(nextVersion);
    }
    
    /**
     * 获取小程序版本详情（包含状态）
     * @param appid 小程序appid
     * @return 版本详情信息
     */
    @GetMapping("/getVersionDetail")
    public Result getVersionDetailByAppid(@RequestParam String appid) {
        List<MiniprogramVersion> version = miniprogramVersionService.lambdaQuery()
                .eq(MiniprogramVersion::getAppid, appid)
                .list();

        if (version == null) {
            return Result.success("未找到版本信息");
        }
        
        return Result.success(version);
    }
    
    /**
     * 更新小程序版本状态
     * @param appid 小程序appid
     * @param status 新状态（开发中、审核中、已上架）
     * @return 更新结果
     */
    @PostMapping("/updateStatus")
    public Result updateVersionStatus(@RequestParam String appid, @RequestParam String status) {
        // 验证状态值
        if (!"开发中".equals(status) && !"审核中".equals(status) && !"已上架".equals(status)) {
            return Result.error("状态值不正确，只能是：开发中、审核中、已上架");
        }
        
        MiniprogramVersion version = miniprogramVersionService.lambdaQuery()
                .eq(MiniprogramVersion::getAppid, appid)
                .one();
        
        if (version == null) {
            return Result.error("未找到版本信息");
        }
        
        version.setStatus(status);
        boolean result = miniprogramVersionService.updateById(version);
        
        if (result) {
            return Result.success("状态更新成功");
        } else {
            return Result.error("状态更新失败");
        }
    }
    
    /**
     * 设置小程序状态为审核中
     * @param appid 小程序appid
     * @return 更新结果
     */
    @PostMapping("/setReviewing")
    public Result setReviewingStatus(@RequestParam String appid) {
        return updateVersionStatus(appid, "审核中");
    }
    
    /**
     * 设置小程序状态为已上架
     * @param appid 小程序appid
     * @return 更新结果
     */
    @PostMapping("/setOnline")
    public Result setOnlineStatus(@RequestParam String appid) {
        return updateVersionStatus(appid, "已上架");
    }
    
    /**
     * 设置小程序状态为开发中
     * @param appid 小程序appid
     * @return 更新结果
     */
    @PostMapping("/setDeveloping")
    public Result setDevelopingStatus(@RequestParam String appid) {
        return updateVersionStatus(appid, "开发中");
    }

}
