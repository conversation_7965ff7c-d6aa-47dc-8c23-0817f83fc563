package com.kdsjkj.service.impl;

import com.kdsjkj.entity.MiniprogramVersion;
import com.kdsjkj.mapper.MiniprogramVersionMapper;
import com.kdsjkj.service.IMiniprogramVersionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class MiniprogramVersionServiceImpl extends ServiceImpl<MiniprogramVersionMapper, MiniprogramVersion> implements IMiniprogramVersionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getNextVersionAndIncrement(String appid) {
        // 查询当前版本记录
        MiniprogramVersion version = this.lambdaQuery()
                .eq(MiniprogramVersion::getAppid, appid)
                .one();
        
        int nextVersion;
        
        if (version == null) {
            // 不存在记录，创建新记录，版本号为1
            version = new MiniprogramVersion();
            version.setAppid(appid);
            version.setVersion(1);
            version.setStatus("开发中"); // 默认状态为开发中
            this.save(version);
            nextVersion = 1;
        } else {
            // 存在记录，版本号+1
            nextVersion = version.getVersion() + 1;
            version.setVersion(nextVersion);
            this.updateById(version);
        }
        
        // 返回格式化的版本号 0.0.version
        return String.format("0.0.%d", nextVersion);
    }
    
    @Override
    public String getVersionByAppid(String appid) {
        // 查询当前版本记录
        MiniprogramVersion version = this.lambdaQuery()
                .eq(MiniprogramVersion::getAppid, appid)
                .one();
        
        if (version == null) {
            // 不存在记录，返回0.0.0
            return "0.0.0";
        } else {
            // 存在记录，返回当前版本号
            return String.format("0.0.%d", version.getVersion());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveVersionInfo(String appid, String version, String status) {
        try {
            // 从版本号字符串中提取数字版本号 (例如: "0.0.1" -> 1)
            int versionNumber = extractVersionNumber(version);
            
            // 创建新的版本记录
            MiniprogramVersion newVersion = new MiniprogramVersion();
            newVersion.setAppid(appid);
            newVersion.setVersion(versionNumber);
            newVersion.setStatus(status);
            return this.save(newVersion);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 从版本号字符串中提取数字版本号
     * @param version 版本号字符串 (例如: "0.0.1")
     * @return 数字版本号 (例如: 1)
     */
    private int extractVersionNumber(String version) {
        if (version == null || version.trim().isEmpty()) {
            return 0;
        }
        
        // 处理 "0.0.x" 格式的版本号
        String[] parts = version.split("\\.");
        if (parts.length >= 3) {
            try {
                return Integer.parseInt(parts[2]);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 如果不是标准格式，尝试直接解析
        try {
            return Integer.parseInt(version);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    @Override
    public int getValidMerchantCountByAppIdsAndStatus(List<String> appIds, String status) {
        if (appIds == null || appIds.isEmpty() || status == null || status.trim().isEmpty()) {
            return 0;
        }
        
        long count = this.lambdaQuery()
                .in(MiniprogramVersion::getAppid, appIds)
                .eq(MiniprogramVersion::getStatus, status)
                .count();
        
        return (int) count;
    }
}
