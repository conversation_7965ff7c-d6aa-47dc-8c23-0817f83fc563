package com.kdsjkj.service.impl;

import com.kdsjkj.entity.BonusRewards;
import com.kdsjkj.mapper.BonusRewardsMapper;
import com.kdsjkj.service.IBonusRewardsService;
import com.kdsjkj.dto.UpdateBonusRewardRequest;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * <p>
 * 分红奖励表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
public class BonusRewardsServiceImpl extends ServiceImpl<BonusRewardsMapper, BonusRewards> implements IBonusRewardsService {

    @Override
    public BonusRewards getBonusReward() {
        log.info("开始获取分红奖励配置");
        
        // 获取唯一的配置记录
        BonusRewards reward = this.lambdaQuery()
                .orderByDesc(BonusRewards::getUpdatedAt)
                .last("LIMIT 1")
                .one();
        
        if (reward == null) {
            log.warn("未找到分红奖励配置，将创建默认配置");
            // 如果没有配置，创建默认配置
            reward = createDefaultReward();
        }
        
        log.info("获取分红奖励配置成功 - ID: {}", reward.getId());
        return reward;
    }
    
    @Override
    public BonusRewards updateBonusReward(Long id, UpdateBonusRewardRequest request) {
        log.info("开始更新分红奖励配置 - ID: {}", id);
        
        // 1. 验证配置是否存在
        BonusRewards reward = this.getById(id);
        if (reward == null) {
            log.error("分红奖励配置不存在 - ID: {}", id);
            throw new RuntimeException("分红奖励配置不存在");
        }
        
        // 2. 更新配置
        reward.setTransactionAmount(request.getTransactionAmount());
        reward.setRewardAmount(request.getRewardAmount());
        reward.setUpdatedAt(LocalDateTime.now());
        
        // 3. 保存更新
        boolean success = this.updateById(reward);
        if (!success) {
            log.error("更新分红奖励配置失败 - ID: {}", id);
            throw new RuntimeException("更新分红奖励配置失败");
        }
        
        log.info("更新分红奖励配置成功 - ID: {}", id);
        return reward;
    }
    
    /**
     * 创建默认的分红奖励配置
     */
    private BonusRewards createDefaultReward() {
        BonusRewards reward = new BonusRewards();
        reward.setTransactionAmount(new BigDecimal("10000.00"));  // 默认流水金额10000元
        reward.setRewardAmount(new BigDecimal("100.00"));  // 默认奖励100元
        reward.setCreatedAt(LocalDateTime.now());
        reward.setUpdatedAt(LocalDateTime.now());
        
        boolean success = this.save(reward);
        if (!success) {
            log.error("创建默认分红奖励配置失败");
            throw new RuntimeException("创建默认分红奖励配置失败");
        }
        
        log.info("创建默认分红奖励配置成功 - ID: {}", reward.getId());
        return reward;
    }
}
