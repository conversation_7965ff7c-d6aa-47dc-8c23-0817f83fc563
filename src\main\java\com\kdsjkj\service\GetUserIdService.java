package com.kdsjkj.service;

import com.kdsjkj.entity.GetUserId;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;

/**
 * 获取用户ID相关配置服务接口
 */
public interface GetUserIdService {
    
    /**
     * 保存配置记录
     */
    void saveConfig(GetUserId getUserId);
    
    /**
     * 根据应用ID获取最新的配置
     */
    GetUserId getLatestByAppId(String appId);
    
    /**
     * 根据授权令牌获取配置
     */
    GetUserId getByAppAuthToken(String appAuthToken);
    
    /**
     * 根据用户ID获取配置
     */
    GetUserId getByUserId(String userId);
    
    /**
     * 处理授权码，获取访问令牌并保存
     */
    GetUserId processAuthCode(String authCode, String appAuthToken);
    
    /**
     * 更新访问令牌信息
     */
    void updateTokenInfo(Long id, AlipaySystemOauthTokenResponse response);
    
    /**
     * 更新错误信息
     */
    void updateErrorInfo(Long id, String errorCode, String errorMsg);
} 