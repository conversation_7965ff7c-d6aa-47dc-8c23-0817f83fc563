package com.kdsjkj.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.response.AlipayTradeRoyaltyRelationBindResponse;
import com.alipay.api.domain.AlipayTradeRoyaltyRelationBindModel;
import com.alipay.api.request.AlipayTradeRoyaltyRelationBindRequest;
import com.alipay.api.domain.RoyaltyEntity;
import com.alipay.api.domain.AlipayTradeRoyaltyRelationBatchqueryModel;
import com.alipay.api.request.AlipayTradeRoyaltyRelationBatchqueryRequest;
import com.alipay.api.response.AlipayTradeRoyaltyRelationBatchqueryResponse;
import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.entity.RoyaltyRelation;
import com.kdsjkj.service.IAlipayConfigFrontService;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.kdsjkj.service.IRoyaltyRelationService;
import com.kdsjkj.service.IFenrunQueryService;
import com.kdsjkj.entity.FenrunQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-20 1:35
 */
@Slf4j
@RestController
@RequestMapping("/api/fenrun")
@CrossOrigin(origins = "*", allowedHeaders = "*")  // 允许跨域访问
public class FenrunController {

    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;

    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;

    @Autowired
    private IRoyaltyRelationService royaltyRelationService;

    @Autowired
    private IFenrunQueryService fenrunQueryService;

    /**
     * 分账关系绑定接口
     * 
     * @param appId 小程序APPID
     * @return 绑定结果
     */
    @PostMapping("/bind")
    public Result bindRoyaltyRelation(
            @RequestParam("appId") String appId) {
        
        try {
            // 生成唯一的外部请求号
            String outRequestNo = UUID.randomUUID().toString().replace("-", "");
            
            // 设置默认的分账接收方类型
            String receiverType = "userId";
            
            // 设置默认的备注信息
            String memo = "分账";

            // 参数校验
            if (appId == null || appId.trim().isEmpty()) {
                return Result.paramError("appId参数不能为空");
            }

            // 检查该appid是否已经绑定过
            FenrunQuery existingQuery = fenrunQueryService.lambdaQuery()
                    .eq(FenrunQuery::getAppid, appId)
                    .one();
            
            if (existingQuery != null) {
                log.info("该appid已存在分账绑定记录 - appId: {}, outRequestNo: {}", appId, existingQuery.getOutRequestNo());
                return Result.success("该appid已存在分账绑定记录，无需重复绑定", existingQuery);
            }

            // 通过royalty_relation表查询分账接收方用户ID
            String receiverAccount = royaltyRelationService.getRoyaltyUserIdByAppid(appId);
            if (receiverAccount == null || receiverAccount.trim().isEmpty()) {
                return Result.error("未找到对应appId的分账接收方用户ID信息，请先配置分账关系");
            }

            log.info("开始绑定分账关系 - appId: {}, outRequestNo: {}, receiverAccount: {}, receiverType: {}", 
                    appId, outRequestNo, receiverAccount, receiverType);

            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数
            AlipayTradeRoyaltyRelationBindRequest request = new AlipayTradeRoyaltyRelationBindRequest();
            AlipayTradeRoyaltyRelationBindModel model = new AlipayTradeRoyaltyRelationBindModel();

            // 设置分账接收方列表
            List<RoyaltyEntity> receiverList = new ArrayList<>();
            RoyaltyEntity receiver = new RoyaltyEntity();
            receiver.setAccount(receiverAccount);
            receiver.setType(receiverType);
            receiver.setMemo(memo);
            
            receiverList.add(receiver);
            model.setReceiverList(receiverList);

            // 设置外部请求号
            model.setOutRequestNo(outRequestNo);

            request.setBizModel(model);

            // 获取app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appId);
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appId的授权令牌信息");
            }

            // 设置第三方代调用模式的app_auth_token
            request.putOtherTextParam("app_auth_token", appAuthToken);

            log.info("发送分账关系绑定请求...");
            AlipayTradeRoyaltyRelationBindResponse response = alipayClient.execute(request);
            
            log.info("分账关系绑定响应: {}", response.getBody());

            if (response.isSuccess()&&response.getResultCode().equals("SUCCESS")) {
                log.info("分账关系绑定成功 - outRequestNo: {}", outRequestNo);
                
                // 绑定成功后，将appid和outRequestNo保存到fenrun_query表
                try {
                    FenrunQuery fenrunQuery = new FenrunQuery();
                    fenrunQuery.setAppid(appId);
                    fenrunQuery.setOutRequestNo(outRequestNo);
                    fenrunQuery.setCreateTime(java.time.LocalDateTime.now());
                    fenrunQuery.setUpdateTime(java.time.LocalDateTime.now());
                    
                    boolean saveSuccess = fenrunQueryService.save(fenrunQuery);
                    if (saveSuccess) {
                        log.info("分账查询记录保存成功 - appId: {}, outRequestNo: {}", appId, outRequestNo);
                    } else {
                        log.warn("分账查询记录保存失败 - appId: {}, outRequestNo: {}", appId, outRequestNo);
                    }
                } catch (Exception e) {
                    log.error("保存分账查询记录异常 - appId: {}, outRequestNo: {}", appId, outRequestNo, e);
                }
                
                return Result.success("分账关系绑定成功", response.getBody());
            } else {
                log.error("分账关系绑定失败 - 错误码: {}, 错误信息: {}", response.getCode(), response.getSubMsg());
                return Result.error("分账关系绑定失败: " + response.getSubMsg());
            }

        } catch (AlipayApiException e) {
            log.error("分账关系绑定异常", e);
            return Result.error("分账关系绑定异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }

    /**
     * 分账关系查询接口
     * 
     * @param appId 小程序APPID
     * @param pageNum 页码（可选，默认1）
     * @param pageSize 页面大小（可选，默认20）
     * @return 查询结果
     */
    @GetMapping("/query")
    public Result queryRoyaltyRelation(
            @RequestParam("appId") String appId,
            @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Long pageSize) {

        try {
            // 参数校验
            if (appId == null || appId.trim().isEmpty()) {
                return Result.paramError("appId参数不能为空");
            }

            log.info("开始查询分账关系 - appId: {}, pageNum: {}, pageSize: {}", appId, pageNum, pageSize);

            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数
            AlipayTradeRoyaltyRelationBatchqueryRequest request = new AlipayTradeRoyaltyRelationBatchqueryRequest();
            AlipayTradeRoyaltyRelationBatchqueryModel model = new AlipayTradeRoyaltyRelationBatchqueryModel();

            // 设置分页参数
            model.setPageNum(pageNum);
            model.setPageSize(pageSize);

            request.setBizModel(model);

            // 获取app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appId);
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appId的授权令牌信息");
            }

            // 设置第三方代调用模式的app_auth_token
            request.putOtherTextParam("app_auth_token", appAuthToken);

            log.info("发送分账关系查询请求...");
            AlipayTradeRoyaltyRelationBatchqueryResponse response = alipayClient.execute(request);

            log.info("分账关系查询响应: {}", response.getBody());

            if (response.isSuccess()) {
                log.info("分账关系查询成功");
                return Result.success("分账关系查询成功", response.getBody());
            } else {
                log.error("分账关系查询失败 - 错误码: {}, 错误信息: {}", response.getCode(), response.getSubMsg());
                return Result.error("分账关系查询失败: " + response.getSubMsg());
            }

        } catch (AlipayApiException e) {
            log.error("分账关系查询异常", e);
            return Result.error("分账关系查询异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            return Result.error("系统异常: " + e.getMessage());
        }
    }



    /**
     * 从数据库获取支付宝配置
     */
    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();

        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }


}
