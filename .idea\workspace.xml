<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="53fd5276-d413-4e6d-8daf-1410fd6662b4" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/entity/MerchantActivation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/mapper/MerchantActivationMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/IMerchantActivationService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/impl/MerchantActivationServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/task/ActivationRewardTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/task/BonusRewardTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cursor/rules/myrule.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/init_alipay_config.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/qr_images/direct_qr_20250709_124952.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/qrcode/将二维码或条形码贴到指定图片上/PosterGenerator.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/qrcode/将二维码或条形码贴到指定图片上/PosterGenerator.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/xiaochengxu/C提审/提审商家小程序/AlipayOpenMiniVersionAuditApply.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/xiaochengxu/C提审/提审商家小程序/AlipayOpenMiniVersionAuditApply.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/分账/分账关系查询/AlipayTradeRoyaltyRelationBatchquery.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/分账/分账关系查询/AlipayTradeRoyaltyRelationBatchquery.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/支付/createPay/AlipayTradeCreateService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/appTest/支付/createPay/AlipayTradeCreateService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/config/AlipayMiniProgramConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/config/AlipayMiniProgramConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/AlipayAuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/AlipayAuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/QrController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/QrController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/AlipayAuthRecordService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/AlipayAuthRecordService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/impl/AlipayAuthRecordServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/impl/AlipayAuthRecordServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/impl/AlipayAuthServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kdsjkj/service/impl/AlipayAuthServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2z31tKlgjaKpZK2n2Pez31FFqae" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP 请求.generated-requests | #85.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.generated-requests | #91.executor&quot;: &quot;Run&quot;,
    &quot;JAR 应用程序.alipay-auth-callback-1.0.0.jar.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript 调试.alipay_promotion_page.html.executor&quot;: &quot;Run&quot;,
    &quot;Maven.alipay-auth-callback [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.alipay-auth-callback [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.alipay-auth-callback [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.alipay-auth-callback [validate].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.AlipayAuthCallbackApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/A帮人开发/支付宝小程序/前后端/backend/src/main/java/com/kdsjkj/代码生成器&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.3839907&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;ml.llm.LLMConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.AlipayOpenMiniBaseinfoQuery.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.AlipayOpenMiniCategoryQuery.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.CodeGenerator.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.DirectQRExtractor.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Getma.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PromotionCodeCrawlerRunner.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\A帮人开发\支付宝小程序\前后端\backend\src\main\java\com\kdsjkj\代码生成器" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.kdsjkj.获取推广码" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.AlipayAuthCallbackApplication">
    <configuration name="AlipayOpenMiniBaseinfoQuery" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.kdsjkj.appTest.xiaochengxu.类目.获取构建的小程序类目.AlipayOpenMiniBaseinfoQuery" />
      <module name="alipay-auth-callback" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kdsjkj.appTest.xiaochengxu.类目.获取构建的小程序类目.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AlipayOpenMiniCategoryQuery" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.kdsjkj.appTest.xiaochengxu.类目.查询小程序类目.AlipayOpenMiniCategoryQuery" />
      <module name="alipay-auth-callback" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kdsjkj.appTest.xiaochengxu.类目.查询小程序类目.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CodeGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.kdsjkj.代码生成器.CodeGenerator" />
      <module name="alipay-auth-callback" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kdsjkj.代码生成器.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="generated-requests | #91" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="91" requestIdentifier="#91" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="alipay-auth-callback-1.0.0.jar" type="JarApplication" temporary="true">
      <option name="JAR_PATH" value="$PROJECT_DIR$/target/alipay-auth-callback-1.0.0.jar" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="shouquanceshi" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="shouquanceshi" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="AlipayAuthCallbackApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="alipay-auth-callback" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kdsjkj.AlipayAuthCallbackApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CodeGenerator" />
        <item itemvalue="JAR 应用程序.alipay-auth-callback-1.0.0.jar" />
        <item itemvalue="应用程序.AlipayOpenMiniCategoryQuery" />
        <item itemvalue="应用程序.AlipayOpenMiniBaseinfoQuery" />
        <item itemvalue="HTTP 请求.generated-requests | #91" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="53fd5276-d413-4e6d-8daf-1410fd6662b4" name="Changes" comment="" />
      <created>1750945155616</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750945155616</updated>
      <workItem from="1750945156705" duration="8109000" />
      <workItem from="1750986786401" duration="1953000" />
      <workItem from="1751014436851" duration="1128000" />
      <workItem from="1751019990516" duration="2849000" />
      <workItem from="1751032862830" duration="1035000" />
      <workItem from="1751036330652" duration="171000" />
      <workItem from="1751902010411" duration="4242000" />
      <workItem from="1751989472998" duration="2252000" />
      <workItem from="1752034813732" duration="2865000" />
      <workItem from="1752048662846" duration="1838000" />
      <workItem from="1752066132540" duration="1228000" />
      <workItem from="1752200505620" duration="8191000" />
      <workItem from="1752217578106" duration="7251000" />
      <workItem from="1752235884866" duration="1994000" />
      <workItem from="1752283713653" duration="7079000" />
      <workItem from="1752327044264" duration="3912000" />
      <workItem from="1752331818383" duration="596000" />
      <workItem from="1752334294680" duration="10353000" />
      <workItem from="1752479967046" duration="3774000" />
      <workItem from="1752496444816" duration="5655000" />
      <workItem from="1752542793522" duration="89000" />
      <workItem from="1752578409582" duration="1263000" />
      <workItem from="1752580054953" duration="74000" />
      <workItem from="1752580357983" duration="276000" />
      <workItem from="1752582237708" duration="1621000" />
      <workItem from="1752661307654" duration="3925000" />
      <workItem from="1752726496159" duration="1518000" />
      <workItem from="1752755488241" duration="7263000" />
      <workItem from="1752816801640" duration="612000" />
      <workItem from="1752820546038" duration="6929000" />
      <workItem from="1752839496815" duration="5200000" />
      <workItem from="1752881881543" duration="38936000" />
      <workItem from="1752976111885" duration="2032000" />
      <workItem from="1752978516920" duration="15094000" />
      <workItem from="1753103485902" duration="3203000" />
      <workItem from="1753175683113" duration="297000" />
      <workItem from="1753321401514" duration="945000" />
      <workItem from="1753330073065" duration="1924000" />
      <workItem from="1753333997523" duration="1353000" />
      <workItem from="1753371247937" duration="1756000" />
      <workItem from="1753402710792" duration="1456000" />
      <workItem from="1753431312342" duration="5000" />
      <workItem from="1753583013272" duration="7542000" />
      <workItem from="1753604673283" duration="2337000" />
      <workItem from="1753615673647" duration="4246000" />
      <workItem from="1753699944900" duration="538000" />
      <workItem from="1753791406124" duration="5321000" />
      <workItem from="1753879918019" duration="4887000" />
      <workItem from="1753886732898" duration="558000" />
      <workItem from="1753891412241" duration="502000" />
      <workItem from="1753931358426" duration="5138000" />
      <workItem from="1753968674538" duration="1475000" />
      <workItem from="1753977783007" duration="940000" />
      <workItem from="1754014566368" duration="370000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/kdsjkj/controller/ReportDataController.java</url>
          <line>78</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>