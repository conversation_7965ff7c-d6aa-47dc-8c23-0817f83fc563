package com.kdsjkj.util;

import com.alipay.api.internal.util.AlipaySignature;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 支付宝签名验证工具类
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
public class AlipaySignUtil {
    
    /**
     * 验证支付宝回调签名
     * 
     * @param params 回调参数
     * @param alipayPublicKey 支付宝公钥
     * @param charset 字符集
     * @param signType 签名类型
     * @return 验证结果
     */
    public static boolean verifySign(Map<String, String> params, 
                                   String alipayPublicKey, 
                                   String charset, 
                                   String signType) {
        try {
            log.debug("开始验证支付宝签名");
            log.debug("签名参数: {}", params);
            log.debug("签名类型: {}", signType);
            
            boolean result = AlipaySignature.rsaCheckV1(params, alipayPublicKey, charset, signType);
            log.debug("签名验证结果: {}", result);
            
            return result;
        } catch (Exception e) {
            log.error("验证支付宝签名异常", e);
            return false;
        }
    }
    
    /**
     * 验证支付宝回调签名（V2版本）
     * 
     * @param params 回调参数
     * @param alipayPublicKey 支付宝公钥
     * @param charset 字符集
     * @param signType 签名类型
     * @return 验证结果
     */
    public static boolean verifySignV2(Map<String, String> params, 
                                     String alipayPublicKey, 
                                     String charset, 
                                     String signType) {
        try {
            log.debug("开始验证支付宝签名V2");
            
            boolean result = AlipaySignature.rsaCheckV2(params, alipayPublicKey, charset, signType);
            log.debug("签名验证结果V2: {}", result);
            
            return result;
        } catch (Exception e) {
            log.error("验证支付宝签名V2异常", e);
            return false;
        }
    }
}
