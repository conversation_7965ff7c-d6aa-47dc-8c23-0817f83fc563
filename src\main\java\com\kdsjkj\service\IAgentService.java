package com.kdsjkj.service;

import com.kdsjkj.dto.AddAgentRequest;
import com.kdsjkj.dto.AddTopAgentRequest;
import com.kdsjkj.dto.UpdateAgentRequest;
import com.kdsjkj.entity.Agent;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 代理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
public interface IAgentService extends IService<Agent> {

    /**
     * 创建代理人
     * 
     * @param request 添加代理人请求
     * @return 创建的代理人
     */
    Agent createAgent(AddAgentRequest request);

    /**
     * 查询指定代理下的所有子代理
     * 
     * @param parentUid 上级代理UID
     * @return 子代理列表
     */
    List<Agent> getSubAgents(String parentUid);

    /**
     * 修改代理人信息
     * 
     * @param uid 代理人UID
     * @param request 修改信息请求
     * @return 更新后的代理人信息
     */
    Agent updateAgent(String uid, UpdateAgentRequest request);

    /**
     * 创建顶级代理人
     * 
     * @param request 添加顶级代理人请求
     * @return 创建的顶级代理人
     */
    Agent createTopAgent(AddTopAgentRequest request);

    /**
     * 根据UID查询代理费率
     * 
     * @param uid 代理UID
     * @return 代理费率，如果代理不存在返回null
     */
    BigDecimal getAgentRateByUid(String uid);

    /**
     * 根据UID查询代理信息
     * 
     * @param uid 代理UID
     * @return 代理信息，如果代理不存在返回null
     */
    Agent getAgentByUid(String uid);
    
    /**
     * 根据代理UID获取所有下级代理UID列表
     * 
     * @param agentUid 代理UID
     * @return 下级代理UID列表
     */
    List<String> getSubAgentUidsByAgentUid(String agentUid);

    /**
     * 根据代理UID获取所有下级代理UID列表（包括子代理的子代理）
     * 
     * @param agentUid 代理UID
     * @return 所有下级代理UID列表
     */
    List<String> getAllSubAgentUidsByAgentUid(String agentUid);
}
