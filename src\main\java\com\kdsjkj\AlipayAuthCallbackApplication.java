package com.kdsjkj;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 支付宝授权回调应用启动类
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@SpringBootApplication
@MapperScan("com.kdsjkj.mapper")
public class AlipayAuthCallbackApplication {

    public static void main(String[] args) {
        SpringApplication.run(AlipayAuthCallbackApplication.class, args);
        System.out.println("=================================");
        System.out.println("支付宝授权回调服务启动成功！");
        System.out.println("回调地址: https://pay.yulinxinxi.com/api/alipay/notifyAuth");
        System.out.println("=================================");
    }
}
