<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.ServiceMarketOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.ServiceMarketOrder">
        <id column="id" property="id" />
        <result column="order_time" property="orderTime" />
        <result column="title" property="title" />
        <result column="name" property="name" />
        <result column="merchant_pid" property="merchantPid" />
        <result column="contactor" property="contactor" />
        <result column="phone" property="phone" />
        <result column="order_item_num" property="orderItemNum" />
        <result column="total_price" property="totalPrice" />
        <result column="biz_type" property="bizType" />
        <result column="item_code" property="itemCode" />
        <result column="specifications" property="specifications" />
        <result column="package_count" property="packageCount" />
        <result column="period_day" property="periodDay" />
        <result column="order_ticket" property="orderTicket" />
        <result column="service_code" property="serviceCode" />
        <result column="isv_ticket" property="isvTicket" />
        <result column="consumer_miniAppId" property="consumerMiniappid" />
        <result column="consumer_appName" property="consumerAppname" />
        <result column="merchant_shop_id" property="merchantShopId" />
        <result column="miniapp_release_bundle" property="miniappReleaseBundle" />
        <result column="promoter_master_name" property="promoterMasterName" />
        <result column="promoter_master_pid" property="promoterMasterPid" />
        <result column="is_fast_open" property="isFastOpen" />
        <result column="agent_uid" property="agentUid" />
        <result column="active_code" property="activeCode" />
        <result column="rate" property="rate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_time, title, name, merchant_pid, contactor, phone, order_item_num, total_price, biz_type, item_code, specifications, package_count, period_day, order_ticket, service_code, isv_ticket, consumer_miniAppId, consumer_appName, merchant_shop_id, miniapp_release_bundle, promoter_master_name, promoter_master_pid, is_fast_open, agent_uid, active_code, rate
    </sql>

</mapper>
