package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

/**
 * 获取用户ID相关配置表实体
 */
@Data
@TableName("getuserid")
public class GetUserId {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键ID
    
    @TableField("app_id")
    private String appId; // 应用APPID
    
    @TableField("app_auth_token")
    private String appAuthToken; // 应用授权令牌
    
    @TableField("private_key")
    private String privateKey; // 应用私钥
    
    @TableField("alipay_public_key")
    private String alipayPublicKey; // 支付宝公钥
    
    @TableField("server_url")
    private String serverUrl; // 服务器地址
    
    @TableField("format")
    private String format; // 数据格式
    
    @TableField("charset")
    private String charset; // 字符编码
    
    @TableField("sign_type")
    private String signType; // 签名类型
    
    @TableField("auth_code")
    private String authCode; // 授权码
    
    @TableField("grant_type")
    private String grantType; // 授权类型
    
    @TableField("access_token")
    private String accessToken; // 访问令牌
    
    @TableField("user_id")
    private String userId; // 支付宝用户ID
    
    @TableField("expires_in")
    private Long expiresIn; // access_token过期时间（秒）
    
    @TableField("refresh_token")
    private String refreshToken; // 刷新令牌
    
    @TableField("re_expires_in")
    private Long reExpiresIn; // refresh_token过期时间（秒）
    
    @TableField("response_body")
    private String responseBody; // 完整响应内容
    
    @TableField("status")
    private String status; // 状态：ACTIVE-有效，EXPIRED-过期，REVOKED-撤销
    
    @TableField("error_code")
    private String errorCode; // 错误代码
    
    @TableField("error_msg")
    private String errorMsg; // 错误信息
    
    @TableField("create_time")
    private Timestamp createTime; // 创建时间
    
    @TableField("update_time")
    private Timestamp updateTime; // 更新时间
    
    /**
     * 构造方法
     */
    public GetUserId() {
        this.createTime = new Timestamp(System.currentTimeMillis());
        this.updateTime = new Timestamp(System.currentTimeMillis());
        this.status = "ACTIVE";
    }
    
    /**
     * 创建配置记录的便捷构造方法
     */
    public GetUserId(String appId, String appAuthToken, String privateKey, String alipayPublicKey) {
        this();
        this.appId = appId;
        this.appAuthToken = appAuthToken;
        this.privateKey = privateKey;
        this.alipayPublicKey = alipayPublicKey;
        this.serverUrl = "https://openapi.alipay.com/gateway.do";
        this.format = "json";
        this.charset = "UTF-8";
        this.signType = "RSA2";
        this.grantType = "authorization_code";
    }
} 