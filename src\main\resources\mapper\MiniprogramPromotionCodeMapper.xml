<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.MiniprogramPromotionCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.MiniprogramPromotionCode">
        <id column="id" property="id" />
        <result column="appid" property="appid" />
        <result column="version" property="version" />
        <result column="type" property="type" />
        <result column="post_url" property="postUrl" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, appid, version, type, post_url, create_time, update_time
    </sql>

</mapper>
