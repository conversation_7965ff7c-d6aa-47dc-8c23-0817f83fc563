package com.kdsjkj.service.impl;

import com.kdsjkj.entity.AgentFund;
import com.kdsjkj.mapper.AgentFundMapper;
import com.kdsjkj.service.IAgentFundService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 代理资金表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class AgentFundServiceImpl extends ServiceImpl<AgentFundMapper, AgentFund> implements IAgentFundService {

    @Override
    public boolean saveAgentFund(String agentUid, BigDecimal amount, Integer obtainType) {
        try {
            AgentFund agentFund = new AgentFund();
            agentFund.setAgentUid(agentUid);
            agentFund.setAmount(amount);
            agentFund.setObtainType(obtainType);
            agentFund.setObtainTime(LocalDateTime.now());
            
            boolean result = this.save(agentFund);
            if (result) {
                log.info("代理资金记录保存成功 - 代理UID: {}, 金额: {}, 类型: {}", agentUid, amount, obtainType);
            } else {
                log.error("代理资金记录保存失败 - 代理UID: {}, 金额: {}, 类型: {}", agentUid, amount, obtainType);
            }
            return result;
        } catch (Exception e) {
            log.error("保存代理资金记录异常 - 代理UID: {}, 金额: {}, 类型: {}", agentUid, amount, obtainType, e);
            return false;
        }
    }

    @Override
    public List<AgentFund> getByAgentUid(String agentUid) {
        return this.lambdaQuery()
                .eq(AgentFund::getAgentUid, agentUid)
                .orderByDesc(AgentFund::getObtainTime)
                .list();
    }

    @Override
    public List<AgentFund> getByObtainType(Integer obtainType) {
        return this.lambdaQuery()
                .eq(AgentFund::getObtainType, obtainType)
                .orderByDesc(AgentFund::getObtainTime)
                .list();
    }

    @Override
    public BigDecimal getTotalAmountByAgentUid(String agentUid) {
        List<AgentFund> funds = this.lambdaQuery()
                .eq(AgentFund::getAgentUid, agentUid)
                .list();
        
        return funds.stream()
                .map(AgentFund::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
