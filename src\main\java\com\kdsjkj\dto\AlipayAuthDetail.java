package com.kdsjkj.dto;

import lombok.Data;

/**
 * 支付宝授权详情DTO
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class AlipayAuthDetail {
    
    /**
     * 被授权方应用ID（第三方应用APPID）
     */
    private String app_id;
    
    /**
     * 授权方的应用ID（商家小程序APPID）
     */
    private String auth_app_id;
    
    /**
     * 授权发生时间
     */
    private Long auth_time;
    
    /**
     * 应用授权码
     */
    private String app_auth_code;
    
    /**
     * 应用授权令牌
     */
    private String app_auth_token;
    
    /**
     * 应用授权令牌失效时间（秒）
     */
    private Long expires_in;
    
    /**
     * 刷新令牌
     */
    private String app_refresh_token;
    
    /**
     * 刷新令牌失效时间（秒）
     */
    private Long re_expires_in;
    
    /**
     * 授权商家的user_id
     */
    private String user_id;
}
