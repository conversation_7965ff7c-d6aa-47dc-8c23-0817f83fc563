package com.kdsjkj.config;

import com.alipay.api.AlipayConfig;

/**
 * 支付宝小程序统一配置类
 * 为xiaochengxu文件夹下的所有文件提供统一的配置参数
 * 
 * <AUTHOR> Generated
 */
public class AlipayMiniProgramConfig {
    
    // 第三方应用授权令牌 - 以AlipayOpenMiniVersionUpload.java的配置为准
    public static final String APP_AUTH_TOKEN = "202507BBfc0ec6be15db444fbfbb29a3fc342F12";

    // 支付宝网关地址
    public static final String SERVER_URL = "https://openapi.alipay.com/gateway.do";
    
    // 应用ID
    public static final String APP_ID = "2021005163695273";
    
    // 数据格式
    public static final String FORMAT = "json";
    
    // 字符编码
    public static final String CHARSET = "UTF-8";
    
    // 签名类型
    public static final String SIGN_TYPE = "RSA2";
    
    // 应用私钥
    public static final String PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC22iPz+K4hYmN7JlrgLz7v9jHgMVNbtpIAcNO2iuFEQU/cw8Uovl/6ZWNT0FRrlBYNfG7RyRSWeIDmjRhq7PRSp4aGhTUNVQXzGrP8B4ZkKIxcv3NCdM4jbTTibUSNZSOxQpCpObH1YkPloVsyf1rwR/iKytO77aSzRunvkbEf8g1qMygdUQr1jmcNYq+FtKvQihxKgb4/6aR+/V2KcItYplUerc2LRPARLjMryUtMQ0sbIiGd9n+TgAyOjDDWeLTDZCuD0ULOL61lyW8Pb8sgb8EsxXyBilxmEqrw2b+0k/P5baETa3lBKwsYS08hGKxfhYafBCMBKfDtwLxVOlYtAgMBAAECggEAJTBOmaMdPzr0gqp1Vx0kzscis7c0ffWu0hogZQLv9+LUrFDAu52khZezUOuRD+QpbAq/uzkLT+MoTDusGKsiseDSkm9rL8/4Cs6Ghp69LYe1rHNfrMd22WuvktGoXJ2SWc3xAAn08LRQnNZpZubSBwaMPSYtVA5sLe4hTBE0lgtbeiiFLquqNS9exwxVnUgEV+oNucfuwrGyM5NVR7iJs6ToHtdIcC0WcdXg9gbpnk2FiCOfmPUMY6Qp9F2Q7ZT2EI2MuxGvRFhRrO4z9/ogE4vzUxx6NdFs9NsZQiO2erh10eh7HLa+LKP8F5pZk9WOMgQ0BtABGg3zb7Rf845PYQKBgQDp+5urn3NQXQTrnzYCuJsnMHtGHbL5ajpolthftkOglJ0CWI4Xbjfs3XBJOqLcWU2pyGJZ84em5mGDCfZx4q8KUiGtnfPKa5abM8PUi50oIRdkQfVOgZf2/tHGHyRbKG8qKZZIN2yQussGbgS/dVTN5WH9RQKnoICYBhbxv3Uk1QKBgQDIDtlC2Z4GLN/831Xl6KEaJasMQ1Lvk2UefzD6ns4HtMF6MKJKDONmXYwxWkSwpkQq7FuRG98yPayOTy58zZudsuUWHVz5WsAi3JNd5YXtCzbP52EL04K3VO3/W41kuy5SzYzE1Ua95h15Ri70CgvG0r+utRmz4ZeNiWIonsD3+QKBgQCXt6U+CHseLeKl4auE4i0AoUgds948KaxL6xvMHAQH2LViMweXm8k5lnBbm2DE3kckgyF60BUynIm4DTq2p9T2LUFXMWdF5rNWWZ9ub0TtI1/mi3pDeHaQGrTs8X6CeVpzjVm74+xKgERrG+WKO8tMYqNCNYA+Ly2S5cf04z9TLQKBgGlGXw/tolmPjcLs56/XbcdroQoP93m475JPzj9kB/lIJjQdmZksFRIOEjL6VIE8zUxlGn5O03IFwGMWydjE/anLtP+hLgbNuaPevPHBUWt+5jYbhRcLE5NT7vXcgJhMY8ERB42gydwuVr41r4meK7pRtqrCBCeDhBPoAq5rdBJJAoGAB2HYFQqZ/AIRmdcCYQ3XboAN0txJ/NHKcmx77Hmf5LNJ50ELX8v7aEUXzHxRhk/5B75G8yRoxhd/uOi3aOs6LhPmRSANBSVZPSimlqJMmaLsyBhiQatJlOHMjAjSK3oHSvoAE0kgmAWVl5QQON3F0NN/qe3faPIx725Em6RZXcs=";
    
    // 支付宝公钥
    public static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwW0Ug2JjoYNUhhfxdPuQlzy3YXgdQs9R3xMXQuhYfuMUMM6D39YbI9YxRDQzl3f6ZjPF44iHNWOSE5pYr/WHNQQslPxifMpX4dqz5lHiasm18o0yY0xHsZPMJr6yoBQN+5MMGDZx3XqIvGRl2vRcAy2ueUiqLCPhaW+R/g4/PlqCvZHPknYekGmuS6zKSdms3cTOdZC+exaVZI83/pOUYF5gpGkebzP4r3GTRmZPWSVhm33B3Z1oLn3nf7Ex/pVoqHRGsQk+P+FxDonWbk0fx3GEE5uOsuY3tfr8wqroVz2V5L6rnL7YqNqu/kbgVrKkkGBiVh/kN0iDZl9BMYHARQIDAQAB";
    
    /**
     * 获取支付宝配置对象
     * 
     * @return AlipayConfig 配置对象
     */
    public static AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(SERVER_URL);
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(PRIVATE_KEY);
        alipayConfig.setFormat(FORMAT);
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayConfig.setCharset(CHARSET);
        alipayConfig.setSignType(SIGN_TYPE);
        return alipayConfig;
    }

    
    /**
     * 获取第三方应用授权令牌
     * 
     * @return app_auth_token
     */
    public static String getAppAuthToken() {
        return APP_AUTH_TOKEN;
    }
} 