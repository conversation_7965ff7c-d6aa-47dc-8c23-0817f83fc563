package com.kdsjkj.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kdsjkj.entity.AlipayServiceOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 支付宝服务市场订单通知Mapper接口
 */
@Mapper
public interface AlipayServiceOrderMapper extends BaseMapper<AlipayServiceOrder> {
    
    /**
     * 根据通知ID查询订单
     */
    @Select("SELECT * FROM alipay_service_order WHERE notify_id = #{notifyId}")
    AlipayServiceOrder selectByNotifyId(String notifyId);
    
    /**
     * 根据商户PID和订单ID查询订单
     */
    @Select("SELECT * FROM alipay_service_order WHERE merchant_pid = #{merchantPid} AND commodity_order_id = #{orderId}")
    AlipayServiceOrder selectByMerchantAndOrderId(String merchantPid, String orderId);
} 