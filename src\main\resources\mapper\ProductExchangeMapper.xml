<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.ProductExchangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.ProductExchange">
        <id column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="product_id" property="productId" />
        <result column="phone" property="phone" />
        <result column="address" property="address" />
        <result column="status" property="status" />
        <result column="exchange_time" property="exchangeTime" />
        <result column="name" property="name" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uid, product_id, phone, address, status, exchange_time, name
    </sql>

</mapper>
