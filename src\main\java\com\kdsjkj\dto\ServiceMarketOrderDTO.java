package com.kdsjkj.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务市场订单DTO
 */
@Data
public class ServiceMarketOrderDTO {
    /**
     * 商户的UID
     */
    private String merchantPid;

    /**
     * 联系人
     */
    private String contactor;

    /**
     * 订购时间
     */
    private LocalDateTime orderTime;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 小程序的名称
     */
    private String consumerAppname;
    
    /**
     * 小程序APPID
     */
    private String appid;

    /**
     * 费率(百分比，如0.0060表示0.6%)
     */
    private BigDecimal rate;
} 