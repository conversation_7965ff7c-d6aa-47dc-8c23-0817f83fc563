package com.kdsjkj.service.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayOpenMiniVersionAuditedCancelModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayOpenMiniVersionAuditedCancelRequest;
import com.alipay.api.response.AlipayOpenMiniVersionAuditedCancelResponse;
import com.kdsjkj.entity.AlipayAuthRecord;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝授权服务实现类
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@Service
public class AlipayAuthServiceImpl implements AlipayAuthService {
    
    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;
    
    @Autowired
    private AlipayServiceOrderService orderService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private IMiniprogramVersionService miniprogramVersionService;

    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;
    
    @Override
    public String processAuthNotify(HttpServletRequest request) {
        try {
            // 获取请求参数
            Map<String, String> params = getRequestParams(request);
            System.out.println("-------------------------------------");
            System.out.println();
            log.info("接收到授权网关参数: {}", JSON.toJSONString(params));
            System.out.println();
            System.out.println("-------------------------------------");
            
            // 验证签名
            if (!verifySign(params)) {
                log.error("签名验证失败");
                return "fail";
            }
            log.info("签名验证成功");
            
            return "success";
            
        } catch (Exception e) {
            log.error("处理授权通知异常", e);
            return "fail";
        }
    }
    
    @Override
    public String processGateWayNotify(HttpServletRequest request) {
        try {
            // 获取请求参数
            Map<String, String> params = getRequestParams(request);
            System.out.println("-------------------------------------");
            System.out.println();
            log.info("接收到应用网关参数: {}", JSON.toJSONString(params));
            System.out.println();
            System.out.println("--------------------------------------");
            
            // 验证签名
            if (!verifySign(params)) {
                log.error("签名验证失败");
                return "fail";
            }
            log.info("签名验证成功");
            
            String notifyType = params.get("notify_type");
            String msgMethod = params.get("msg_method");

            if ("servicemarket_order_notify".equals(notifyType)) {
                // 处理服务市场订单通知
                processServiceMarketOrderRecord(params);
            } else if ("open_app_auth_notify".equals(notifyType)) {
                // 处理应用授权通知
                processAuthNotifyRecord(params);
            } else if ("alipay.open.mini.version.audit.passed".equals(msgMethod)) {
                // 处理小程序版本审核通过通知
                processMiniprogramVersionAuditPassed(params);
            } else if ("alipay.open.mini.version.audit.rejected".equals(msgMethod)) {
                // 处理小程序版本审核被拒绝通知
                processMiniprogramVersionAuditRejected(params);
            } else {
                // 记录未知类型的消息到文件
                String logFileName = "unknown_notify_" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".log";
                String logContent = String.format("[%s] 未知的通知类型: %s, msg_method: %s, 消息内容: %s%n",
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    notifyType,
                    msgMethod,
                    JSON.toJSONString(params, true));

                try {
                    java.nio.file.Files.write(
                        java.nio.file.Paths.get(logFileName),
                        logContent.getBytes("UTF-8"),
                        java.nio.file.StandardOpenOption.CREATE,
                        java.nio.file.StandardOpenOption.APPEND
                    );
                    log.warn("未知的通知类型已记录到文件: {}", logFileName);
                } catch (Exception e) {
                    log.error("写入未知通知类型日志失败", e);
                }
            }

                return "success";
            
        } catch (Exception e) {
            log.error("处理授权通知异常", e);
            return "fail";
        }
    }
    
    @Override
    public boolean verifySign(Map<String, String> params) {
        try {
            AlipayConfig config = getAlipayConfig();
            return AlipaySignature.rsaCheckV1(params,
                config.getAlipayPublicKey(),
                config.getCharset(),
                config.getSignType());
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }
    
    /**
     * 处理服务市场订单记录
     */
    private void processServiceMarketOrderRecord(Map<String, String> params) {
        try {
            // 保存服务市场订单记录

            ServiceMarketOrder order = new ServiceMarketOrder();
            // 按照ServiceMarketOrder实体类字段进行赋值，并修正字段名和类型


            order.setOrderTime(LocalDateTime.now());

            order.setTitle(params.get("title"));
            order.setName(params.get("name"));
            order.setMerchantPid(params.get("merchant_pid"));
            order.setContactor(params.get("contactor"));
            order.setPhone(params.get("phone"));

            // 门店数
            if (params.get("order_item_num") != null) {
                order.setOrderItemNum(Integer.valueOf(params.get("order_item_num")));
            }
            // 订单总价
            if (params.get("total_price") != null) {
                order.setTotalPrice(new BigDecimal(params.get("total_price")));
            }
            order.setBizType(params.get("biz_type"));
            order.setItemCode(params.get("item_code"));
            order.setSpecifications(params.get("specifications"));
            // 套餐数量
            if (params.get("package_count") != null) {
                order.setPackageCount(Integer.valueOf(params.get("package_count")));
            }
            // 订购周期（天）
            if (params.get("period_day") != null) {
                order.setPeriodDay(Integer.valueOf(params.get("period_day")));
            }
            order.setOrderTicket(params.get("order_ticket"));
            order.setServiceCode(params.get("service_code"));
            order.setIsvTicket(params.get("isv_ticket"));
            order.setConsumerMiniappid(params.get("consumer_miniAppId"));
            order.setConsumerAppname(params.get("consumer_appName"));
            order.setMerchantShopId(params.get("merchant_shop_id"));
            order.setMiniappReleaseBundle(params.get("miniapp_release_bundle"));
            order.setPromoterMasterName(params.get("promoter_master_name"));
            order.setPromoterMasterPid(params.get("promoter_master_pid"));
            // 是否极速开通订单
            if (params.get("is_fast_open") != null) {
                order.setIsFastOpen(Boolean.valueOf(params.get("is_fast_open")));
            }
            String[] isvTickets = params.get("isv_ticket").split("_");
            if (isvTickets.length >= 2) {
                String agentUid = isvTickets[0];  // 2088052800156834
                order.setAgentUid(agentUid);
                
                // 通过AgentUid查询代理费率并设置
                BigDecimal agentRate = agentService.getAgentRateByUid(agentUid);
                if (agentRate != null) {
                    order.setRate(agentRate);
                    log.info("为订单设置代理费率成功，AgentUid：{}，费率：{}", agentUid, agentRate);
                } else {
                    log.warn("未找到对应的代理费率，AgentUid：{}，使用默认费率0", agentUid);
                    order.setRate(new BigDecimal("0.3"));
                }
                
                order.setActiveCode(isvTickets[1]); // dcd702fbd4db431ca22a3b1e0c3739ff
            } else {
                log.warn("isv_ticket格式不正确: {}", params.get("isv_ticket"));
                order.setRate(BigDecimal.ZERO); // 设置默认费率
            }
            
            serviceMarketOrderService.save(order);
            log.info("服务市场订单记录已保存，ID: {}", order.getId());

        } catch (Exception e) {
            log.error("处理服务市场订单记录异常", e);
            throw e;
        }
    }

    /**
     * 获取请求参数
     */
    private Map<String, String> getRequestParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();

        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }

        return params;
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) throws ParseException {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            throw new IllegalArgumentException("日期字符串不能为空");
        }
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr);
        } catch (ParseException e) {
            log.error("解析日期异常: {}", dateStr, e);
            throw new ParseException("日期格式不正确，期望格式：yyyy-MM-dd HH:mm:ss", e.getErrorOffset());
        }
    }

    /**
     * 处理应用授权通知记录
     */
    private void processAuthNotifyRecord(Map<String, String> params) {
        try {
            String bizContent = params.get("biz_content");
            if (!StringUtils.hasText(bizContent)) {
                log.error("biz_content为空");
                return;
            }

            // 解析业务内容
            Map<String, Object> bizContentMap = JSON.parseObject(bizContent);
            Map<String, Object> detail = (Map<String, Object>) bizContentMap.get("detail");
            Map<String, Object> notifyContext = (Map<String, Object>) bizContentMap.get("notify_context");

            // 构造记录实体
            AlipayAuthRecord record = new AlipayAuthRecord();
            record.setNotifyId(params.get("notify_id"));
            record.setAppId(params.get("app_id"));
            record.setAuthAppId(params.get("auth_app_id"));
            record.setNotifyStatus(params.get("notify_type"));
            record.setBizStatus(params.get("status"));
            record.setRawNotifyData(JSON.toJSONString(params));
            record.setProcessStatus("SUCCESS");
            record.setCreateTime(new Timestamp(System.currentTimeMillis()));

            if (detail != null) {
                record.setAppAuthToken(String.valueOf(detail.get("app_auth_token")));
                record.setAppRefreshToken(String.valueOf(detail.get("app_refresh_token")));
                record.setAppAuthCode(String.valueOf(detail.get("app_auth_code")));
                record.setUserId(String.valueOf(detail.get("user_id")));
                record.setAuthTime(Long.valueOf(String.valueOf(detail.get("auth_time"))));
                record.setExpiresIn(Long.valueOf(String.valueOf(detail.get("expires_in"))));
                record.setReExpiresIn(Long.valueOf(String.valueOf(detail.get("re_expires_in"))));
            }

            if (notifyContext != null) {
                record.setTriggerType(String.valueOf(notifyContext.get("trigger")));
            }

            alipayAuthRecordService.saveAuthRecord(record);
            log.info("授权通知记录已保存，记录ID: {}", record.getId());

        } catch (Exception e) {
            log.error("处理授权通知记录异常", e);
            throw e;
        }
    }

    /**
     * 处理小程序版本审核通过通知
     */
    private void processMiniprogramVersionAuditPassed(Map<String, String> params) {
        try {
            log.info("开始处理小程序版本审核通过通知");

            // 获取biz_content
            String bizContent = params.get("biz_content");
            if (bizContent == null || bizContent.trim().isEmpty()) {
                log.error("biz_content为空，无法处理小程序版本审核通过通知");
                return;
            }

            // 解析biz_content JSON
            Map<String, Object> bizData = JSON.parseObject(bizContent, Map.class);
            String miniAppId = (String) bizData.get("mini_app_id");
            String miniAppVersion = (String) bizData.get("mini_app_version");

            if (miniAppId == null || miniAppId.trim().isEmpty()) {
                log.error("mini_app_id为空，无法处理小程序版本审核通过通知");
                return;
            }

            if (miniAppVersion == null || miniAppVersion.trim().isEmpty()) {
                log.error("mini_app_version为空，无法处理小程序版本审核通过通知");
                return;
            }

            log.info("解析到小程序信息 - appId: {}, version: {}", miniAppId, miniAppVersion);

            // 提取版本号（只取最后一个小数点后的数字）
            int versionNumber = extractVersionNumber(miniAppVersion);
            log.info("提取的版本号: {}", versionNumber);

            // 更新小程序版本状态为已上架
            boolean updateResult = miniprogramVersionService.lambdaUpdate()
                    .eq(com.kdsjkj.entity.MiniprogramVersion::getAppid, miniAppId)
                    .eq(com.kdsjkj.entity.MiniprogramVersion::getVersion, versionNumber)
                    .set(com.kdsjkj.entity.MiniprogramVersion::getStatus, "已上架")
                    .update();

            if (updateResult) {
                log.info("小程序版本状态更新成功 - appId: {}, version: {}, 状态: 已上架", miniAppId, versionNumber);
            } else {
                log.warn("小程序版本状态更新失败，可能是未找到匹配的记录 - appId: {}, version: {}", miniAppId, versionNumber);
            }

        } catch (Exception e) {
            log.error("处理小程序版本审核通过通知异常", e);
            throw e;
        }
    }

    /**
     * 处理小程序版本审核被拒绝通知
     */
    private void processMiniprogramVersionAuditRejected(Map<String, String> params) {
        try {
            log.info("开始处理小程序版本审核被拒绝通知");

            // 获取biz_content
            String bizContent = params.get("biz_content");
            if (bizContent == null || bizContent.trim().isEmpty()) {
                log.error("biz_content为空，无法处理小程序版本审核被拒绝通知");
                return;
            }

            // 解析biz_content JSON
            Map<String, Object> bizData = JSON.parseObject(bizContent, Map.class);
            String miniAppId = (String) bizData.get("mini_app_id");
            String miniAppVersion = (String) bizData.get("mini_app_version");
            String auditReason = (String) bizData.get("audit_reason");

            if (miniAppId == null || miniAppId.trim().isEmpty()) {
                log.error("mini_app_id为空，无法处理小程序版本审核被拒绝通知");
                return;
            }

            if (miniAppVersion == null || miniAppVersion.trim().isEmpty()) {
                log.error("mini_app_version为空，无法处理小程序版本审核被拒绝通知");
                return;
            }

            log.info("解析到小程序审核被拒绝信息 - appId: {}, version: {}, reason: {}",
                    miniAppId, miniAppVersion, auditReason);

            // 提取版本号（只取最后一个小数点后的数字）
            int versionNumber = extractVersionNumber(miniAppVersion);
            log.info("提取的版本号: {}", versionNumber);

            // 调用支付宝API退回开发状态
            boolean cancelResult = cancelAuditedVersion(miniAppId, miniAppVersion);

            if (cancelResult) {
                // API调用成功，更新小程序版本状态为开发中
                boolean updateResult = miniprogramVersionService.lambdaUpdate()
                        .eq(com.kdsjkj.entity.MiniprogramVersion::getAppid, miniAppId)
                        .eq(com.kdsjkj.entity.MiniprogramVersion::getVersion, versionNumber)
                        .set(com.kdsjkj.entity.MiniprogramVersion::getStatus, "开发中")
                        .update();

                if (updateResult) {
                    log.info("小程序版本已退回开发状态 - appId: {}, version: {}, 状态: 开发中",
                            miniAppId, versionNumber);
                } else {
                    log.warn("小程序版本状态更新失败，可能是未找到匹配的记录 - appId: {}, version: {}",
                            miniAppId, versionNumber);
                }
            } else {
                // API调用失败，仍然更新状态为审核未通过
                boolean updateResult = miniprogramVersionService.lambdaUpdate()
                        .eq(com.kdsjkj.entity.MiniprogramVersion::getAppid, miniAppId)
                        .eq(com.kdsjkj.entity.MiniprogramVersion::getVersion, versionNumber)
                        .set(com.kdsjkj.entity.MiniprogramVersion::getStatus, "审核未通过")
                        .update();

                if (updateResult) {
                    log.info("小程序版本状态更新为审核未通过 - appId: {}, version: {}",
                            miniAppId, versionNumber);
                } else {
                    log.warn("小程序版本状态更新失败，可能是未找到匹配的记录 - appId: {}, version: {}",
                            miniAppId, versionNumber);
                }
            }

        } catch (Exception e) {
            log.error("处理小程序版本审核被拒绝通知异常", e);
            throw e;
        }
    }

    /**
     * 调用支付宝API退回开发状态
     */
    private boolean cancelAuditedVersion(String miniAppId, String miniAppVersion) {
        try {
            log.info("开始调用支付宝API退回开发状态 - appId: {}, version: {}", miniAppId, miniAppVersion);

            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数
            AlipayOpenMiniVersionAuditedCancelRequest request = new AlipayOpenMiniVersionAuditedCancelRequest();
            AlipayOpenMiniVersionAuditedCancelModel model = new AlipayOpenMiniVersionAuditedCancelModel();

            // 设置商家小程序版本号（不去掉小数点）
            model.setAppVersion(miniAppVersion);

            // 设置小程序端
            model.setBundleId("com.alipay.alipaywallet");

            request.setBizModel(model);

            // 获取app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(miniAppId);
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                log.error("未找到对应appid的授权令牌信息 - appId: {}", miniAppId);
                return false;
            }

            // 第三方代调用模式下设置app_auth_token
            request.putOtherTextParam("app_auth_token", appAuthToken);

            AlipayOpenMiniVersionAuditedCancelResponse response = alipayClient.execute(request);
            log.info("退回开发状态API响应: {}", response.getBody());

            if (response.isSuccess()) {
                log.info("退回开发状态API调用成功 - appId: {}, version: {}", miniAppId, miniAppVersion);
                return true;
            } else {
                log.error("退回开发状态API调用失败 - appId: {}, version: {}, error: {}",
                        miniAppId, miniAppVersion, response.getMsg());
                return false;
            }

        } catch (AlipayApiException e) {
            log.error("调用退回开发状态API异常 - appId: {}, version: {}", miniAppId, miniAppVersion, e);
            return false;
        } catch (Exception e) {
            log.error("退回开发状态处理异常 - appId: {}, version: {}", miniAppId, miniAppVersion, e);
            return false;
        }
    }

    /**
     * 获取支付宝配置
     */
    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();

        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }

    /**
     * 从版本号字符串中提取最后一个小数点后的数字
     * 例如：0.0.25 -> 25
     */
    private int extractVersionNumber(String version) {
        if (version == null || version.trim().isEmpty()) {
            return 0;
        }

        // 找到最后一个小数点的位置
        int lastDotIndex = version.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // 没有小数点，尝试直接解析整个字符串
            try {
                return Integer.parseInt(version.trim());
            } catch (NumberFormatException e) {
                log.warn("无法解析版本号: {}", version);
                return 0;
            }
        }

        // 提取最后一个小数点后的部分
        String lastPart = version.substring(lastDotIndex + 1);
        try {
            return Integer.parseInt(lastPart.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析版本号的最后部分: {}", lastPart);
            return 0;
        }
    }
}

