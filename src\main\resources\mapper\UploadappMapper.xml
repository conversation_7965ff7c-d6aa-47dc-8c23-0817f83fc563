<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.UploadappMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.Uploadapp">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_auth_token" property="appAuthToken" />
        <result column="template_version" property="templateVersion" />
        <result column="template_id" property="templateId" />
        <result column="app_version" property="appVersion" />
        <result column="bundle_id" property="bundleId" />
        <result column="ext" property="ext" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, app_auth_token, template_version, template_id, app_version, bundle_id, ext
    </sql>

</mapper>
