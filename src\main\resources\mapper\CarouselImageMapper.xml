<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.CarouselImageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.CarouselImage">
        <id column="id" property="id" />
        <result column="carousel_url" property="carouselUrl" />
        <result column="appid" property="appid" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="link_url" property="linkUrl" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, carousel_url, appid, sort_order, status, title, description, link_url, created_time, updated_time
    </sql>

</mapper>
