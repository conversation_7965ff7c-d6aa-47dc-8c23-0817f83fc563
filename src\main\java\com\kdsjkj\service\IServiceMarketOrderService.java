package com.kdsjkj.service;

import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.dto.ServiceMarketOrderDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;
import java.util.List;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务市场订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IServiceMarketOrderService extends IService<ServiceMarketOrder> {
    
    /**
     * 根据激活码查询订单记录
     * 
     * @param activeCode 激活码
     * @return 订单记录DTO列表
     */
    List<ServiceMarketOrderDTO> getOrdersByActiveCode(String activeCode);

    /**
     * 根据费率范围查询订单记录
     * 
     * @param minRate 最小费率
     * @param maxRate 最大费率
     * @return 订单记录列表
     */
    List<ServiceMarketOrder> getOrdersByRate(BigDecimal minRate, BigDecimal maxRate);

    /**
     * 更新订单费率
     * 
     * @param orderId 订单ID
     * @param rate 新费率
     * @return 更新是否成功
     */
    boolean updateOrderRate(Long orderId, BigDecimal rate);

    /**
     * 根据appid查询对应的代理UID
     * 
     * @param appid 小程序APPID (对应consumer_miniAppId)
     * @return 代理UID，如果未找到返回null
     */
    String getAgentUidByAppid(String appid);
    
    /**
     * 根据appid获取商户费率
     */
    BigDecimal getMerchantRateByAppid(String appid);
    
    /**
     * 根据代理UID和时间范围获取商户appid列表
     * 
     * @param agentUid 代理UID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 商户appid列表
     */
    List<String> getMerchantAppIdsByAgentUidAndTimeRange(String agentUid, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据代理UID获取所有商户appid列表
     * 
     * @param agentUid 代理UID
     * @return 商户appid列表
     */
    List<String> getMerchantAppIdsByAgentUid(String agentUid);
    
    /**
     * 根据代理UID获取开通商户个数
     * 
     * @param agentUid 代理UID
     * @return 开通商户个数
     */
    int getMerchantCountByAgentUid(String agentUid);
}
