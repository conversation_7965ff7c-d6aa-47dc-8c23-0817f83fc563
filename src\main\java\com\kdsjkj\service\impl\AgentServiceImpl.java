package com.kdsjkj.service.impl;

import com.kdsjkj.dto.AddAgentRequest;
import com.kdsjkj.dto.AddTopAgentRequest;
import com.kdsjkj.dto.UpdateAgentRequest;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.mapper.AgentMapper;
import com.kdsjkj.service.IAgentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 代理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
@Slf4j
@Service
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements IAgentService {

    @Override
    @Transactional
    public Agent createAgent(AddAgentRequest request) {
        log.info("开始创建代理人，姓名：{}, 手机号：{}, UID：{}", 
                request.getName(), request.getPhone(), request.getUid());
        

        
        // 检查UID是否已存在
        Agent existingAgentByUid = this.lambdaQuery()
                .eq(Agent::getUid, request.getUid())
                .one();
        
        if (existingAgentByUid != null) {
            log.warn("UID已存在：{}", request.getUid());
            throw new RuntimeException("该UID已被使用");
        }
        
        // 如果指定了上级代理，验证上级代理是否存在
        if (request.getParentUid() != null && !request.getParentUid().trim().isEmpty()) {
            Agent parentAgent = this.lambdaQuery()
                    .eq(Agent::getUid, request.getParentUid())
                    .one();
            
            if (parentAgent == null) {
                log.warn("上级代理不存在：{}", request.getParentUid());
                throw new RuntimeException("指定的上级代理不存在");
            }
        }
        
        // 构建Agent对象
        Agent agent = new Agent();
        agent.setName(request.getName().trim());
        agent.setPhone(request.getPhone().trim());
        agent.setUid(request.getUid().trim());
        agent.setRate(request.getRate());
        agent.setParentUid(request.getParentUid());
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        agent.setCreateTime(now);
        agent.setUpdateTime(now);
        
        // 保存代理人信息
        boolean saved = this.save(agent);
        
        if (!saved) {
            log.error("代理人保存失败");
            throw new RuntimeException("代理人保存失败");
        }
        
        log.info("代理人创建成功，UID：{}", request.getUid());
        return agent;
    }

    @Override
    public List<Agent> getSubAgents(String parentUid) {
        log.info("开始查询子代理，上级代理UID：{}", parentUid);
        
        // 检查父代理是否存在
        Agent parentAgent = this.lambdaQuery()
                .eq(Agent::getUid, parentUid)
                .one();
                
        if (parentAgent == null) {
            log.warn("上级代理不存在：{}", parentUid);
            throw new RuntimeException("指定的上级代理不存在");
        }
        
        // 查询所有子代理
        List<Agent> subAgents = this.lambdaQuery()
                .eq(Agent::getParentUid, parentUid)
                .orderByDesc(Agent::getCreateTime)
                .list();
        
        log.info("查询子代理完成，上级代理UID：{}，子代理数量：{}", parentUid, subAgents.size());
        return subAgents;
    }

    @Override
    @Transactional
    public Agent updateAgent(String uid, UpdateAgentRequest request) {
        log.info("开始修改代理人信息，UID：{}，新姓名：{}，新手机号：{}，新费率：{}", 
                uid, request.getName(), request.getPhone(), request.getRate());
        
        // 查找要修改的代理
        Agent agent = this.lambdaQuery()
                .eq(Agent::getUid, uid)
                .one();
                
        if (agent == null) {
            log.warn("代理不存在：{}", uid);
            throw new RuntimeException("指定的代理不存在");
        }
        
        // 检查手机号是否被其他代理使用
        Agent existingAgentByPhone = this.lambdaQuery()
                .eq(Agent::getPhone, request.getPhone())
                .ne(Agent::getUid, uid)  // 排除自己
                .one();
        
        if (existingAgentByPhone != null) {
            log.warn("手机号已被其他代理使用：{}", request.getPhone());
            throw new RuntimeException("该手机号已被其他代理使用");
        }
        
        // 更新代理信息
        agent.setName(request.getName().trim());
        agent.setPhone(request.getPhone().trim());
        agent.setRate(request.getRate());
        agent.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        boolean updated = this.updateById(agent);
        
        if (!updated) {
            log.error("代理人信息更新失败");
            throw new RuntimeException("代理人信息更新失败");
        }
        
        log.info("代理人信息更新成功，UID：{}", uid);
        return agent;
    }

    @Override
    @Transactional
    public Agent createTopAgent(AddTopAgentRequest request) {
        log.info("开始创建顶级代理人，姓名：{}，手机号：{}，UID：{}", 
                request.getName(), request.getPhone(), request.getUid());
        
        // 检查手机号是否已存在
        Agent existingAgentByPhone = this.lambdaQuery()
                .eq(Agent::getPhone, request.getPhone())
                .one();
        
        if (existingAgentByPhone != null) {
            log.warn("手机号已存在：{}", request.getPhone());
            throw new RuntimeException("该手机号已被注册");
        }
        
        // 检查UID是否已存在
        Agent existingAgentByUid = this.lambdaQuery()
                .eq(Agent::getUid, request.getUid())
                .one();
        
        if (existingAgentByUid != null) {
            log.warn("UID已存在：{}", request.getUid());
            throw new RuntimeException("该UID已被使用");
        }
        
        // 构建顶级代理对象
        Agent agent = new Agent();
        agent.setName(request.getName().trim());
        agent.setPhone(request.getPhone().trim());
        agent.setUid(request.getUid().trim());
        agent.setRate(new BigDecimal("0.3")); // 设置固定费率0.3
        agent.setParentUid("-1"); // 设置为顶级代理
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        agent.setCreateTime(now);
        agent.setUpdateTime(now);
        
        // 保存代理人信息
        boolean saved = this.save(agent);
        
        if (!saved) {
            log.error("顶级代理人保存失败");
            throw new RuntimeException("顶级代理人保存失败");
        }
        
        log.info("顶级代理人创建成功，UID：{}", request.getUid());
        return agent;
    }

    @Override
    public BigDecimal getAgentRateByUid(String uid) {
        log.info("开始查询代理费率，UID：{}", uid);
        
        if (uid == null || uid.trim().isEmpty()) {
            log.warn("UID为空，无法查询费率");
            return null;
        }
        
        Agent agent = this.lambdaQuery()
                .eq(Agent::getUid, uid.trim())
                .select(Agent::getRate)
                .one();
        
        if (agent == null) {
            log.warn("代理不存在，UID：{}", uid);
            return null;
        }
        
        BigDecimal rate = agent.getRate();
        log.info("查询代理费率成功，UID：{}，费率：{}", uid, rate);
        return rate;
    }

    @Override
    public Agent getAgentByUid(String uid) {
        log.info("开始查询代理信息，UID：{}", uid);
        
        if (uid == null || uid.trim().isEmpty()) {
            log.warn("UID为空，无法查询代理信息");
            return null;
        }
        
        Agent agent = this.lambdaQuery()
                .eq(Agent::getUid, uid.trim())
                .one();
        
        if (agent == null) {
            log.warn("代理不存在，UID：{}", uid);
            return null;
        }
        
        log.info("查询代理信息成功，UID：{}，姓名：{}", uid, agent.getName());
        return agent;
    }
    
    @Override
    public List<String> getSubAgentUidsByAgentUid(String agentUid) {
        log.info("开始查询下级代理UID列表，代理UID：{}", agentUid);
        
        if (agentUid == null || agentUid.trim().isEmpty()) {
            log.warn("代理UID为空，无法查询下级代理");
            return new java.util.ArrayList<>();
        }
        
        List<Agent> subAgents = this.lambdaQuery()
                .eq(Agent::getParentUid, agentUid)
                .select(Agent::getUid)
                .list();
        
        List<String> subAgentUids = subAgents.stream()
                .map(Agent::getUid)
                .filter(uid -> uid != null && !uid.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        
        log.info("查询下级代理UID列表成功，代理UID：{}，下级代理数量：{}", agentUid, subAgentUids.size());
        return subAgentUids;
    }

    @Override
    public List<String> getAllSubAgentUidsByAgentUid(String agentUid) {
        log.info("开始查询所有下级代理UID列表（包括子代理的子代理），代理UID：{}", agentUid);
        
        if (agentUid == null || agentUid.trim().isEmpty()) {
            log.warn("代理UID为空，无法查询下级代理");
            return new java.util.ArrayList<>();
        }
        
        // 使用Set来存储所有下级代理UID，避免重复
        java.util.Set<String> allSubAgentUids = new java.util.HashSet<>();
        // 添加当前代理UID
        allSubAgentUids.add(agentUid);
        
        // 递归查找所有下级代理
        findAllSubAgents(agentUid, allSubAgentUids);
        
        // 转换为List并返回
        List<String> result = new java.util.ArrayList<>(allSubAgentUids);
        log.info("查询所有下级代理UID列表成功，代理UID：{}，总代理数量：{}", agentUid, result.size());
        return result;
    }
    
    /**
     * 递归查找所有下级代理
     */
    private void findAllSubAgents(String parentUid, java.util.Set<String> allSubAgentUids) {
        List<Agent> subAgents = this.lambdaQuery()
                .eq(Agent::getParentUid, parentUid)
                .select(Agent::getUid)
                .list();
        
        for (Agent subAgent : subAgents) {
            String subAgentUid = subAgent.getUid();
            if (subAgentUid != null && !subAgentUid.trim().isEmpty() && allSubAgentUids.add(subAgentUid)) {
                // 如果成功添加到Set中（即不是重复的），则继续查找这个代理的下级
                findAllSubAgents(subAgentUid, allSubAgentUids);
            }
        }
    }
}
