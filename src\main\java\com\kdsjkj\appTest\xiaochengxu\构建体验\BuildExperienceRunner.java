//package com.kdsjkj.appTest.xiaochengxu.构建体验;
//
//import java.util.Scanner;
//
///**
// * 构建到体验版本流程的简单运行器
// * 支持自定义参数运行
// */
//public class BuildExperienceRunner {
//
//    public static void main(String[] args) {
//        System.out.println("=== 支付宝小程序构建体验版本 ===");
//
//        Scanner scanner = new Scanner(System.in);
////
//        // 首先检查绑定状态
//        System.out.println("\n🔍 步骤0: 检查小程序关联绑定状态");
//        System.out.print("是否需要检查小程序关联绑定状态? (y/n, 默认: y): ");
//        String checkBinding = scanner.nextLine().trim();
//        if (checkBinding.isEmpty() || checkBinding.equalsIgnoreCase("y")) {
//            System.out.println("正在检查小程序关联绑定状态...");
//            MiniProgramBindingChecker checker = new MiniProgramBindingChecker();
//            checker.checkBindingStatus();
//
//            System.out.print("\n是否继续构建流程? (y/n): ");
//            String continueProcess = scanner.nextLine().trim();
//            if (!continueProcess.equalsIgnoreCase("y")) {
//                System.out.println("构建流程已取消");
//                scanner.close();
//                return;
//            }
//        }
//
//        System.out.print("请输入小程序版本号 (默认: 0.0.1): ");
//        String appVersion = scanner.nextLine().trim();
//        if (appVersion.isEmpty()) {
//            appVersion = "0.0.1";
//        }
//
//        System.out.print("请输入模板版本号 (默认: 0.0.12): ");
//        String templateVersion = scanner.nextLine().trim();
//        if (templateVersion.isEmpty()) {
//            templateVersion = "0.0.12";
//        }
//
//        System.out.println("\n✅ 开始运行:");
//        System.out.println("小程序版本号: " + appVersion);
//        System.out.println("模板版本号: " + templateVersion);
//        System.out.println("==============================");
//
//        scanner.close();
//
//        MiniProgramBuildAndExperience processor = new MiniProgramBuildAndExperience();
//        processor.buildAndCreateExperience(appVersion, templateVersion);
//    }
//}