package com.kdsjkj.controller;


import com.alipay.api.*;
import com.alipay.api.request.AlipayOpenMiniVersionAuditApplyRequest;
import com.alipay.api.response.AlipayOpenMiniVersionAuditApplyResponse;
import com.alipay.api.request.AlipayOpenAppQrcodeCreateRequest;
import com.alipay.api.response.AlipayOpenAppQrcodeCreateResponse;
import com.alipay.api.domain.AlipayOpenAppQrcodeCreateModel;
import com.kdsjkj.config.AlipayMiniProgramConfig;
import com.kdsjkj.constant.Result;
import com.kdsjkj.entity.AlipayConfigFront;
import com.kdsjkj.entity.ServiceMarketOrder;
import com.kdsjkj.service.AlipayAuthRecordService;
import com.kdsjkj.service.IAlipayConfigFrontService;
import com.kdsjkj.service.IMiniprogramVersionService;
import com.kdsjkj.entity.MiniprogramVersion;
import com.kdsjkj.service.IServiceMarketOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Value;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.Base64;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.kdsjkj.appTest.qrcode.将二维码或条形码贴到指定图片上.PosterGenerator;
import java.util.HashMap;
import com.kdsjkj.entity.MiniprogramPromotionCode;
import com.kdsjkj.service.IMiniprogramPromotionCodeService;

/**
 * <p>
 * 小程序提审表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping("/miniprogram-tisheng")
public class MiniprogramTishengController {
    @Autowired
    private IAlipayConfigFrontService alipayConfigFrontService;
    
    @Autowired
    private IMiniprogramVersionService miniprogramVersionService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private AlipayAuthRecordService alipayAuthRecordService;

    @Autowired
    private IMiniprogramPromotionCodeService miniprogramPromotionCodeService;

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.address:localhost}")
    private String serverAddress;

    private String baseUrl;
    private String uploadDir;

    @PostConstruct
    public void init() {
        // 设置上传目录为项目运行目录下的upload/qrcodes
        uploadDir = "upload/qrcodes";
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // 构建基础URL
        baseUrl = String.format("http://%s:%s", serverAddress, serverPort);

        log.info("文件上传目录: {}", dir.getAbsolutePath());
        log.info("访问基础URL: {}", baseUrl);
    }

    @PostMapping("/tisheng")
    public Result tisheng(String appid,
                         String appVersion,
                         @RequestParam("appLogoFile") MultipartFile appLogoFile,
                         String versionDesc,
                         String appSlogan,
                         String miniCategoryIds,
                         String appName,
                         String servicePhone,
                         String appDesc) throws AlipayApiException {
        try {
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniVersionAuditApplyRequest request = new AlipayOpenMiniVersionAuditApplyRequest();

            // 检查文件是否为空
            if (appLogoFile.isEmpty()) {
                return Result.error("请上传应用Logo图片");
            }

            // 从上传的文件创建FileItem
            String originalFilename = appLogoFile.getOriginalFilename();
            String filename = originalFilename != null ? originalFilename : "appLogo.jpg";
            FileItem appLogo = new FileItem(filename, appLogoFile.getBytes());
        request.setAppLogo(appLogo);


        // 设置小程序版本号
        request.setAppVersion(appVersion);

        // 设置小程序版本描述
        request.setVersionDesc(versionDesc);

        // 设置小程序简介 - 更具体的服务描述（10-32个字符限制）
        request.setAppSlogan(appSlogan);

        // 设置区域类型
        request.setRegionType("CHINA");

        // 设置新小程序前台类目
        request.setMiniCategoryIds(miniCategoryIds);


        // 设置小程序名称 - 按照规范：【地域+企业关键字+服务内容】
        request.setAppName(appName);
            request.setAutoOnline("true");


        // 设置小程序客服电话
        request.setServicePhone(servicePhone);

        // 设置小程序描述 - 更详细的功能描述
        request.setAppDesc(appDesc);
        // 根据appid从service_market_order表中查询app_auth_token
        //TODO 待修改
        ServiceMarketOrder serviceOrder = serviceMarketOrderService.lambdaQuery()
                .eq(ServiceMarketOrder::getConsumerMiniappid, appid)
                .orderByDesc(ServiceMarketOrder::getOrderTime)
                .last("LIMIT 1")
                .one();
        // 根据appid从alipay_auth_record表中查询app_auth_token
        String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);

        if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
            return Result.error("未找到对应appid的授权令牌信息");
        }

        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", appAuthToken);

        AlipayOpenMiniVersionAuditApplyResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
                // 提审成功后，更新版本状态为审核中
                boolean updateResult = miniprogramVersionService.lambdaUpdate()
                        .eq(MiniprogramVersion::getAppid, appid)
                        .eq(MiniprogramVersion::getVersion, extractVersionNumber(appVersion))
                        .set(MiniprogramVersion::getStatus, "审核中")
                        .update();

                if (!updateResult) {
                    System.out.println("版本状态更新失败，但提审成功");
                }
            System.out.println("调用成功");
        }
        return Result.success();
        } catch (IOException e) {
            return Result.error("文件处理失败: " + e.getMessage());
        }
    }

    /**
     * 生成小程序二维码并制作推广码
     */
    @PostMapping("/createQrcode")
    public Result createQrcode(
            @RequestParam("appid") String appid,
            @RequestParam("version") String version,
            @RequestParam("type") String type,
            @RequestParam("describe") String describe,
            @RequestParam("urlParam") String urlParam) {
        try {
            // 参数校验
            if (appid == null || appid.trim().isEmpty()) {
                return Result.paramError("appid参数不能为空");
            }
            if (version == null || version.trim().isEmpty()) {
                return Result.paramError("version参数不能为空");
            }
            if (type == null || type.trim().isEmpty()) {
                return Result.paramError("type参数不能为空");
            }
            if (describe == null || describe.trim().isEmpty()) {
                return Result.paramError("describe参数不能为空");
            }
            if (urlParam == null || urlParam.trim().isEmpty()) {
                return Result.paramError("urlParam参数不能为空");
            }

            // 先查询数据库中是否已存在对应的推广码
            MiniprogramPromotionCode existingCode = miniprogramPromotionCodeService.lambdaQuery()
                    .eq(MiniprogramPromotionCode::getAppid, appid)
                    .eq(MiniprogramPromotionCode::getVersion, version)
                    .eq(MiniprogramPromotionCode::getType, type)
                    .one();

            if (existingCode != null && existingCode.getPostUrl() != null && !existingCode.getPostUrl().trim().isEmpty()) {
                log.info("找到已存在的推广码: {}", existingCode.getPostUrl());
                return Result.success("推广码已存在", new HashMap<String, String>() {{
                    put("accessUrl", existingCode.getPostUrl());
                }});
            }

            // 先查询小程序名称
            ServiceMarketOrder serviceOrder = serviceMarketOrderService.lambdaQuery()
                    .eq(ServiceMarketOrder::getConsumerMiniappid, appid)
                    .orderByDesc(ServiceMarketOrder::getOrderTime)
                    .last("LIMIT 1")
                    .one();

            if (serviceOrder == null || serviceOrder.getConsumerAppname() == null) {
                return Result.error("未找到对应的小程序信息");
            }

            String appName = serviceOrder.getConsumerAppname();
            log.info("获取到小程序名称: {} (长度: {})", appName, appName != null ? appName.length() : "null");
            
            // 检查appName的字符编码
            if (appName != null) {
                try {
                    byte[] bytes = appName.getBytes("UTF-8");
                    log.info("小程序名称UTF-8编码字节数: {}", bytes.length);
                    log.info("小程序名称字符详情: {}", java.util.Arrays.toString(appName.toCharArray()));
                } catch (Exception e) {
                    log.error("检查字符编码时出错: {}", e.getMessage());
                }
            }

            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数以调用接口
            AlipayOpenAppQrcodeCreateRequest request = new AlipayOpenAppQrcodeCreateRequest();
            AlipayOpenAppQrcodeCreateModel model = new AlipayOpenAppQrcodeCreateModel();

            // 设置传入的参数
            model.setDescribe(describe);            // 设置码描述
            model.setUrlParam(urlParam);            // 设置跳转小程序的页面路径
            model.setQueryParam("x=1");             // 设置小程序的启动参数（保持默认）

            request.setBizModel(model);

            // 获取app_auth_token
            String appAuthToken = alipayAuthRecordService.getAppAuthTokenByAuthAppId(appid);
            if (appAuthToken == null || appAuthToken.trim().isEmpty()) {
                return Result.error("未找到对应appid的授权令牌信息");
            }

            // 设置第三方应用授权令牌
            request.putOtherTextParam("app_auth_token", appAuthToken);

            AlipayOpenAppQrcodeCreateResponse response = alipayClient.execute(request);
            log.info("二维码生成响应: {}", response.getBody());

            // 检查响应是否成功并且有二维码URL
            if (response.isSuccess()) {
                String qrCodeUrl = response.getQrCodeUrlCircleBlue();
                if (qrCodeUrl != null && !qrCodeUrl.trim().isEmpty()) {
                    log.info("二维码蓝色圆形链接: {}", qrCodeUrl);
                    
                    try {
                        // 下载二维码图片
                        log.info("正在下载二维码图片...");
                        BufferedImage qrCodeImage = downloadImage(qrCodeUrl);
                        
                        if (qrCodeImage != null) {
                            // 生成时间戳文件名
                            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                            String relativePath = uploadDir + "/direct_qr_" + timestamp + ".png";
                            
                            // 确保输出目录存在
                            File outputFile = new File(relativePath);
                            if (!outputFile.getParentFile().exists()) {
                                outputFile.getParentFile().mkdirs();
                            }

                            // 使用ClassPathResource获取背景图片
                            BufferedImage posterImage;
                            try {
                                InputStream posterStream = getClass().getClassLoader().getResourceAsStream("static/images/poster_background.jpg");
                                if (posterStream == null) {
                                    log.error("背景图片资源不存在: static/images/poster_background.jpg");
                                    return Result.error("背景图片资源不存在");
                                }
                                posterImage = ImageIO.read(posterStream);
                                posterStream.close();
                            } catch (IOException e) {
                                log.error("读取背景图片失败: {}", e.getMessage());
                                return Result.error("读取背景图片失败");
                            }
                            
                            log.info("使用背景图片: static/images/poster_background.jpg");
                            
                            // 计算二维码居中位置
                            int posterWidth = posterImage.getWidth();
                            int posterHeight = posterImage.getHeight();
                            int qrWidth = qrCodeImage.getWidth();
                            int qrHeight = qrCodeImage.getHeight();
                            
                            int x = (posterWidth - qrWidth) / 2;
                            int y = (posterHeight - qrHeight) / 2 + 30;
                            
                            log.info("计算的二维码位置: x={}, y={}", x, y);
                            
                            // 生成推广码，传入小程序名称替换"百货店"
                            PosterGenerator.pasteCodeOnPoster(posterImage, relativePath, qrCodeImage, x, y, appName);
                            
                            log.info("推广码生成成功，访问地址: {}", relativePath);
                            
                            // 保存到数据库
                            MiniprogramPromotionCode promotionCode = new MiniprogramPromotionCode();
                            promotionCode.setAppid(appid);
                            promotionCode.setVersion(version);
                            promotionCode.setType(type);
                            promotionCode.setPostUrl("/" + relativePath);
                            promotionCode.setCreateTime(LocalDateTime.now());
                            promotionCode.setUpdateTime(LocalDateTime.now());
                            
                            // 如果已存在记录但URL为空，则更新；否则新增
                            if (existingCode != null) {
                                promotionCode.setId(existingCode.getId());
                                miniprogramPromotionCodeService.updateById(promotionCode);
                                log.info("更新推广码记录到数据库，ID: {}", existingCode.getId());
                            } else {
                                miniprogramPromotionCodeService.save(promotionCode);
                                log.info("保存推广码记录到数据库，ID: {}", promotionCode.getId());
                            }
                            
                            // 返回成功结果
                            return Result.success("推广码生成成功", new HashMap<String, String>() {{
                                put("accessUrl", "/" + relativePath);
                            }});
                        } else {
                            return Result.error("无法下载二维码图片");
                        }
                    } catch (Exception e) {
                        log.error("生成推广码异常", e);
                        return Result.error("生成推广码异常: " + e.getMessage());
                    }
                } else {
                    return Result.error("二维码URL为空");
                }
            } else {
                return Result.error("二维码生成失败: " + response.getSubMsg());
            }
        } catch (Exception e) {
            log.error("生成二维码异常", e);
            return Result.error("生成二维码异常: " + e.getMessage());
        }
    }

    /**
     * 从版本字符串中提取版本号
     * 例如：从 "0.0.1" 提取出 1，从 "0.0.10" 提取出 10
     * 
     * @param appVersion 版本字符串，格式为 "x.x.x"
     * @return 版本号
     */
    private Integer extractVersionNumber(String appVersion) {
        if (appVersion == null || appVersion.trim().isEmpty()) {
            return 1; // 默认版本号
        }
        
        try {
            // 按点分割版本字符串
            String[] versionParts = appVersion.split("\\.");
            if (versionParts.length >= 3) {
                // 取最后一部分作为版本号
                return Integer.parseInt(versionParts[2]);
            } else {
                return 1; // 格式不正确时返回默认版本号
            }
        } catch (NumberFormatException e) {
            System.out.println("版本号格式解析失败: " + appVersion + ", 使用默认版本号1");
            return 1; // 解析失败时返回默认版本号
        }
    }

    /**
     * 下载网络图片
     */
    private BufferedImage downloadImage(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            URLConnection connection = url.openConnection();
            // 设置User-Agent避免被拒绝
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(10000); // 10秒读取超时
            
            try (InputStream inputStream = connection.getInputStream()) {
                return ImageIO.read(inputStream);
            }
        } catch (Exception e) {
            log.error("下载图片失败: {}", e.getMessage());
            return null;
        }
    }

    private AlipayConfig getAlipayConfig() {
        // 从数据库获取最新的配置
        AlipayConfigFront config = alipayConfigFrontService.lambdaQuery()
                .orderByDesc(AlipayConfigFront::getUpdateTime)
                .last("LIMIT 1")
                .one();

        if (config == null) {
            throw new RuntimeException("未找到支付宝配置信息");
        }

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(config.getServerUrl());
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setPrivateKey(config.getPrivateKey());
        alipayConfig.setFormat(config.getFormat());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setCharset(config.getCharset());
        alipayConfig.setSignType(config.getSignType());

        return alipayConfig;
    }

}
