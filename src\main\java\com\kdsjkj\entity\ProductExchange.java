package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商品兑换表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_exchange")
public class ProductExchange implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代理用户唯一标识
     */
    private String uid;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 收货手机号
     */
    private String phone;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 兑换状态：0-待处理，1-已发货，2-已完成，3-已取消
     */
    private Integer status;

    /**
     * 兑换时间
     */
    @TableField("exchange_time")
    private LocalDateTime exchangeTime;

    /**
     * 收获方姓名
     */
    private String name;


}
