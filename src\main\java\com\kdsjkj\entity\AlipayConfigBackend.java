package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("alipay_config_backend")
public class AlipayConfigBackend {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String serverUrl;
    
    private String appId;
    
    private String privateKey;
    
    private String alipayPublicKey;
}
