# 环境配置使用指南

## 可用环境

项目支持多个环境配置，每个环境有不同的配置参数：

### 1. 开发环境 (dev) - 默认环境
- **端口**: 8080
- **SSL**: 关闭
- **日志级别**: DEBUG
- **监控端点**: health, info
- **用途**: 本地开发调试

### 2. 测试环境 (test)
- **端口**: 8081
- **SSL**: 关闭
- **日志级别**: DEBUG
- **监控端点**: health, info, metrics
- **日志文件**: ./logs/alipay-auth-test.log
- **用途**: 功能测试、集成测试

### 3. 生产环境 (prod)
- **端口**: 443
- **SSL**: 启用 (需要证书)
- **日志级别**: INFO/WARN
- **监控端点**: 仅 health
- **日志文件**: /var/log/alipay-auth/alipay-auth.log
- **用途**: 正式生产环境

## 启动方式

### 方式一：使用Maven启动

```bash
# 开发环境（默认）
mvn spring-boot:run

# 指定开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 测试环境
mvn spring-boot:run -Dspring-boot.run.profiles=test

# 生产环境
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

### 方式二：使用jar包启动

```bash
# 先打包
mvn clean package -DskipTests

# 开发环境（默认）
java -jar target/alipay-auth-callback-1.0.0.jar

# 指定开发环境
java -jar target/alipay-auth-callback-1.0.0.jar --spring.profiles.active=dev

# 测试环境
java -jar target/alipay-auth-callback-1.0.0.jar --spring.profiles.active=test

# 生产环境
java -jar target/alipay-auth-callback-1.0.0.jar --spring.profiles.active=prod
```

### 方式三：使用环境变量

```bash
# 设置环境变量
export SPRING_PROFILES_ACTIVE=test

# 启动应用
java -jar target/alipay-auth-callback-1.0.0.jar
```

### 方式四：IDE启动

在IDE中设置启动参数：
```
Program arguments: --spring.profiles.active=dev
```

或设置环境变量：
```
Environment variables: SPRING_PROFILES_ACTIVE=test
```

## 环境特性对比

| 特性 | 开发环境 (dev) | 测试环境 (test) | 生产环境 (prod) |
|------|----------------|-----------------|-----------------|
| 端口 | 8080 | 8081 | 443 |
| SSL | ❌ | ❌ | ✅ |
| 日志级别 | DEBUG | DEBUG | INFO/WARN |
| 控制台日志 | ✅ | ✅ | ✅ |
| 文件日志 | ❌ | ✅ | ✅ |
| 健康检查 | ✅ | ✅ | ✅ |
| 详细监控 | ✅ | ✅ | ❌ |
| 数据存储 | 内存 | 内存 | 内存 |

## 访问地址

### 开发环境
- 应用地址: http://localhost:8080
- 健康检查: http://localhost:8080/actuator/health
- 应用信息: http://localhost:8080/actuator/info

### 测试环境
- 应用地址: http://localhost:8081
- 健康检查: http://localhost:8081/actuator/health
- 应用信息: http://localhost:8081/actuator/info
- 监控指标: http://localhost:8081/actuator/metrics

### 生产环境
- 应用地址: https://pay.yulinxinxi.com
- 健康检查: https://pay.yulinxinxi.com/actuator/health

## 测试接口

每个环境都提供以下测试接口：

```bash
# 查看所有授权数据
GET /api/test/auth-data

# 根据APPID查看授权数据
GET /api/test/auth-data/{authAppId}

# 清空所有授权数据
DELETE /api/test/auth-data

# 模拟授权通知
POST /api/test/simulate-notify

# 健康检查
GET /api/test/health
```

## 环境切换示例

### 开发调试
```bash
# 启动开发环境，详细日志输出
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 功能测试
```bash
# 启动测试环境，保存日志文件
mvn spring-boot:run -Dspring-boot.run.profiles=test
```

### 生产部署
```bash
# 生产环境启动（需要SSL证书）
java -jar alipay-auth-callback-1.0.0.jar --spring.profiles.active=prod
```

## 注意事项

1. **SSL证书**: 生产环境需要配置SSL证书文件
2. **日志目录**: 确保日志目录有写入权限
3. **端口占用**: 确保对应端口未被占用
4. **防火墙**: 生产环境需要开放443端口
5. **内存使用**: 所有环境都使用内存存储，重启后数据丢失

## 配置自定义

如需自定义配置，可以：

1. **修改配置文件**: 直接编辑对应的application-{env}.yml文件
2. **启动参数覆盖**: 使用--server.port=9090等参数覆盖
3. **环境变量**: 使用SERVER_PORT=9090等环境变量覆盖

例如：
```bash
# 自定义端口启动开发环境
java -jar app.jar --spring.profiles.active=dev --server.port=9090
``` 