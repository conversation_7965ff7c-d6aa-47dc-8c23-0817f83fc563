package com.kdsjkj.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 小程序提审表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("miniprogram_tisheng")
public class MiniprogramTisheng implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 小程序版本号
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 小程序名称
     */
    @TableField("app_name")
    private String appName;

    /**
     * 小程序版本描述
     */
    @TableField("version_desc")
    private String versionDesc;

    /**
     * 客服电话
     */
    @TableField("service_phone")
    private String servicePhone;

    /**
     * 小程序类目代码
     */
    @TableField("mini_category_ids")
    private String miniCategoryIds;

    /**
     * 应用简介
     */
    @TableField("app_slogan")
    private String appSlogan;

    /**
     * 应用详细描述
     */
    @TableField("app_desc")
    private String appDesc;

    /**
     * 小程序图标URL地址
     */
    private String logo;


}
