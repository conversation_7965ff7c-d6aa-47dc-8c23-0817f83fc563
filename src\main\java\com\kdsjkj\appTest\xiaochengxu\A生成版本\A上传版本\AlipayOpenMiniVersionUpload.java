package com.kdsjkj.appTest.xiaochengxu.A生成版本.A上传版本;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.request.AlipayOpenMiniVersionUploadRequest;
import com.alipay.api.domain.AlipayOpenMiniVersionUploadModel;
import com.alipay.api.response.AlipayOpenMiniVersionUploadResponse;
import com.kdsjkj.config.AlipayMiniProgramConfig;

public class AlipayOpenMiniVersionUpload {

    public static void main(String[] args) throws AlipayApiException {
        System.out.println("=== 支付宝小程序版本上传构建 ===");
        
        // 显示当前配置信息
        System.out.println("📋 当前配置信息:");
        System.out.println("应用ID: " + AlipayMiniProgramConfig.getAlipayConfig().getAppId());
        System.out.println("授权令牌: " + AlipayMiniProgramConfig.getAppAuthToken());
        System.out.println("网关地址: " + AlipayMiniProgramConfig.getAlipayConfig().getServerUrl());
        System.out.println("==============================");
        
        // 初始化SDK - 使用统一配置类
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayOpenMiniVersionUploadRequest request = new AlipayOpenMiniVersionUploadRequest();
        AlipayOpenMiniVersionUploadModel model = new AlipayOpenMiniVersionUploadModel();
        
        // 设置小程序模板版本号
        model.setTemplateVersion("0.0.21");
        
        // 设置小程序模板 APPID
        model.setTemplateId("2021005162607623");
        
        // 设置商家小程序版本号
        model.setAppVersion("0.0.1");
        
        // 设置小程序投放的端参数
        model.setBundleId("com.alipay.alipaywallet");

        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());

        System.out.println("\n🚀 开始上传构建...");
        System.out.println("模板版本: 0.0.21");
        System.out.println("模板ID: 2021005162607623");
        System.out.println("小程序版本: 0.0.1");
        System.out.println("Bundle ID: com.alipay.alipaywallet");

        AlipayOpenMiniVersionUploadResponse response = alipayClient.execute(request);
        
        System.out.println("\n📥 服务器完整响应:");
        System.out.println("==============================");
        System.out.println(response.getBody());
        System.out.println("==============================");

        if (response.isSuccess()) {
            System.out.println("\n✅ 构建请求提交成功");
            System.out.println("\n⚠️ 重要提示：");
            System.out.println("1. 构建成功并不意味着配置继承成功");
            System.out.println("2. 需要检查第三方应用与小程序的关联绑定状态");
            System.out.println("3. 确保第三方应用已正确配置接口加签方式");
            System.out.println("4. 建议运行 MiniProgramBindingChecker 检查绑定状态");
            
            System.out.println("\n🔍 配置继承检查建议:");
            System.out.println("- 登录支付宝开放平台");
            System.out.println("- 检查第三方应用的接口加签配置");
            System.out.println("- 确认小程序已正确关联到第三方应用");
            System.out.println("- 验证 app_auth_token 的有效性和权限");
            
        } else {
            System.out.println("\n❌ 构建请求失败");
            System.out.println("错误码: " + response.getCode());
            System.out.println("错误信息: " + response.getMsg());
            
            // 分析常见错误原因
            analyzeError(response.getCode(), response.getMsg());
            
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            System.out.println("🔍 诊断链接: " + diagnosisUrl);
        }
    }
    
    /**
     * 分析错误原因并提供解决方案
     */
    private static void analyzeError(String errorCode, String errorMsg) {
        System.out.println("\n🔍 错误分析:");
        System.out.println("==============================");
        
        if ("40004".equals(errorCode)) {
            System.out.println("❌ 业务处理失败 - 可能原因:");
            System.out.println("   1. app_auth_token 无效、过期或权限不足");
            System.out.println("   2. 小程序与第三方应用关联绑定不完整");
            System.out.println("   3. 模板ID或版本号不正确");
            System.out.println("   4. 第三方应用配置不完整");
            
            System.out.println("\n💡 解决方案:");
            System.out.println("   1. 重新获取 app_auth_token");
            System.out.println("   2. 检查支付宝开放平台的关联绑定设置");
            System.out.println("   3. 确保第三方应用已配置接口加签方式");
            System.out.println("   4. 验证模板ID和版本号的正确性");
            
        } else if ("20000".equals(errorCode)) {
            System.out.println("❌ 系统繁忙 - 建议稍后重试");
            
        } else if ("40001".equals(errorCode)) {
            System.out.println("❌ 缺少必选参数");
            System.out.println("   检查所有必需的参数是否已正确设置");
            
        } else if ("40002".equals(errorCode)) {
            System.out.println("❌ 非法的参数");
            System.out.println("   检查参数格式和取值范围");
            
        } else {
            System.out.println("❌ 未知错误码: " + errorCode);
            System.out.println("   详细信息: " + errorMsg);
        }
        
        System.out.println("==============================");
    }
}