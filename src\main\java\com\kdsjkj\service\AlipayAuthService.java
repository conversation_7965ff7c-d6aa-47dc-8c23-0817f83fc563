package com.kdsjkj.service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 支付宝授权服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface AlipayAuthService {
    
    /**
     * 处理授权通知
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    String processAuthNotify(HttpServletRequest request);
    
    /**
     * 处理网关通知
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    String processGateWayNotify(HttpServletRequest request);
    
    /**
     * 验证签名
     * 
     * @param params 请求参数
     * @return 验证结果
     */
    boolean verifySign(Map<String, String> params);
}
