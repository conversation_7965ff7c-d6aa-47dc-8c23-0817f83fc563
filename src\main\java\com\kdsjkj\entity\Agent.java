package com.kdsjkj.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Size;

/**
 * <p>
 * 代理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Agent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代理姓名
     */
    @NotBlank(message = "代理人姓名不能为空")
    @Size(min = 2, max = 50, message = "代理人姓名长度必须在2-50个字符之间")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 用户唯一标识
     */
    private String uid;

    /**
     * 费率（例如：0.0038表示0.38%）
     */
    @NotNull(message = "费率不能为空")
    @DecimalMin(value = "0.0001", message = "费率必须大于0.0001")
    private BigDecimal rate;

    /**
     * 上级代理UID
     */
    @TableField("parent_uid")
    private String parentUid;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
