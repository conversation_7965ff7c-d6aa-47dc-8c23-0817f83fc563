package com.kdsjkj.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商户激活状态记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("merchant_activation")
public class MerchantActivation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户APPID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 代理UID
     */
    @TableField("agent_uid")
    private String agentUid;

    /**
     * 激活金额
     */
    @TableField("activation_amount")
    private BigDecimal activationAmount;

    /**
     * 激活时间
     */
    @TableField("activation_time")
    private LocalDateTime activationTime;

    /**
     * 奖励金额
     */
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
