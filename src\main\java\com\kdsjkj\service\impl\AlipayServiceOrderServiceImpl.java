package com.kdsjkj.service.impl;

import com.kdsjkj.entity.AlipayServiceOrder;
import com.kdsjkj.mapper.AlipayServiceOrderMapper;
import com.kdsjkj.service.AlipayServiceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 支付宝服务市场订单通知服务实现类
 */
@Slf4j
@Service
public class AlipayServiceOrderServiceImpl implements AlipayServiceOrderService {
    
    @Autowired
    private AlipayServiceOrderMapper orderMapper;
    
    @Override
    @Transactional
    public AlipayServiceOrder saveOrder(AlipayServiceOrder order) {
        try {
            // 设置默认值
            if (order.getProcessStatus() == null) {
                order.setProcessStatus("PENDING");
            }
            if (order.getCreateTime() == null) {
                order.setCreateTime(new Date());
            }
            order.setUpdateTime(new Date());
            
            // 保存记录
            orderMapper.insert(order);
            log.info("服务市场订单记录已保存，ID: {}", order.getId());
            
            return order;
        } catch (Exception e) {
            log.error("保存服务市场订单记录异常", e);
            throw new RuntimeException("保存服务市场订单记录失败", e);
        }
    }
    
    @Override
    public AlipayServiceOrder getByNotifyId(String notifyId) {
        try {
            return orderMapper.selectByNotifyId(notifyId);
        } catch (Exception e) {
            log.error("查询订单记录异常", e);
            throw new RuntimeException("查询订单记录失败", e);
        }
    }
    
    @Override
    public AlipayServiceOrder getByMerchantAndOrderId(String merchantPid, String orderId) {
        try {
            return orderMapper.selectByMerchantAndOrderId(merchantPid, orderId);
        } catch (Exception e) {
            log.error("查询订单记录异常", e);
            throw new RuntimeException("查询订单记录失败", e);
        }
    }
    
    @Override
    @Transactional
    public AlipayServiceOrder updateStatus(Long id, String status, String errorMsg) {
        try {
            // 获取当前记录
            AlipayServiceOrder order = orderMapper.selectById(id);
            if (order == null) {
                throw new RuntimeException("订单记录不存在: " + id);
            }
            
            // 更新状态
            order.setProcessStatus(status);
            order.setErrorMsg(errorMsg);
            order.setUpdateTime(new Date());
            orderMapper.updateById(order);
            
            log.info("服务市场订单状态已更新，ID: {}, 新状态: {}", id, status);
            return order;
            
        } catch (Exception e) {
            log.error("更新订单状态异常", e);
            throw new RuntimeException("更新订单状态失败", e);
        }
    }
} 