#!/bin/bash

# 配置参数
SERVER_HOST="your_server_ip"           # 服务器IP地址
SERVER_USER="root"                     # 服务器用户名
SERVER_PORT="22"                       # SSH端口
REMOTE_PATH="/opt/app/"                # 服务器目标路径
JAR_NAME="backend.jar"                 # JAR文件名
SERVICE_NAME="backend"                 # 服务名称

# 本地JAR文件路径
LOCAL_JAR_PATH="./target/${JAR_NAME}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 开始部署 ${JAR_NAME} 到服务器...${NC}"

# 检查本地JAR文件是否存在
if [ ! -f "$LOCAL_JAR_PATH" ]; then
    echo -e "${RED}❌ 错误: 找不到JAR文件 $LOCAL_JAR_PATH${NC}"
    echo -e "${YELLOW}💡 请先执行: mvn clean package${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 找到本地JAR文件: $LOCAL_JAR_PATH${NC}"

# 创建备份
echo -e "${YELLOW}📦 在服务器上创建备份...${NC}"
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "
    if [ -f ${REMOTE_PATH}${JAR_NAME} ]; then
        cp ${REMOTE_PATH}${JAR_NAME} ${REMOTE_PATH}${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S)
        echo '✓ 备份完成'
    else
        echo '! 没有找到旧版本，跳过备份'
    fi
"

# 停止服务
echo -e "${YELLOW}⏹️  停止服务...${NC}"
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "
    if pgrep -f '${JAR_NAME}' > /dev/null; then
        pkill -f '${JAR_NAME}'
        echo '✓ 服务已停止'
        sleep 3
    else
        echo '! 服务未运行'
    fi
"

# 上传新JAR文件
echo -e "${YELLOW}📤 上传JAR文件...${NC}"
scp -P $SERVER_PORT "$LOCAL_JAR_PATH" "$SERVER_USER@$SERVER_HOST:$REMOTE_PATH"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ JAR文件上传成功${NC}"
else
    echo -e "${RED}❌ JAR文件上传失败${NC}"
    exit 1
fi

# 启动服务
echo -e "${YELLOW}▶️  启动服务...${NC}"
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "
    cd $REMOTE_PATH
    nohup java -jar ${JAR_NAME} > app.log 2>&1 &
    echo '✓ 服务启动命令已执行'
"

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "
    if pgrep -f '${JAR_NAME}' > /dev/null; then
        echo '✅ 服务启动成功'
        echo '📄 最新日志:'
        tail -n 20 ${REMOTE_PATH}app.log
    else
        echo '❌ 服务启动失败'
        echo '📄 错误日志:'
        tail -n 20 ${REMOTE_PATH}app.log
        exit 1
    fi
"

echo -e "${GREEN}🎉 部署完成！${NC}" 