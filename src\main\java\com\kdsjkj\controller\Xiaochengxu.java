//package com.kdsjkj.controller;
//
//import com.alipay.api.AlipayApiException;
//import com.alipay.api.AlipayClient;
//import com.alipay.api.AlipayConfig;
//import com.alipay.api.DefaultAlipayClient;
//import com.alipay.api.domain.AlipayOpenMiniVersionUploadModel;
//import com.alipay.api.request.AlipayOpenMiniVersionUploadRequest;
//import com.alipay.api.response.AlipayOpenMiniVersionUploadResponse;
//import com.kdsjkj.config.AlipayMiniProgramConfig;
//import com.kdsjkj.constant.Result;
//import com.kdsjkj.entity.AlipayConfigBackend;
//import com.kdsjkj.service.IAlipayConfigBackendService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.CrossOrigin;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2025-07-11 21:56
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/xiaochengxu")
//@CrossOrigin(origins = "*", allowedHeaders = "*")  // 允许跨域访问
//public class Xiaochengxu {
//    @Autowired
//    private IAlipayConfigBackendService alipayConfigBackendService;
//    private AlipayConfig getAlipayConfig() {
//        // 从数据库获取配置
//        AlipayConfigBackend configFromDb = alipayConfigBackendService.getLatestConfig();
//
//        AlipayConfig alipayConfig = new AlipayConfig();
//        alipayConfig.setFormat("json");
//        alipayConfig.setCharset("UTF-8");
//        alipayConfig.setSignType("RSA2");
//
//        if (configFromDb != null) {
//            // 使用数据库中的配置
//            alipayConfig.setServerUrl(configFromDb.getServerUrl());
//            alipayConfig.setAppId(configFromDb.getAppId());
//            alipayConfig.setPrivateKey(configFromDb.getPrivateKey());
//            alipayConfig.setAlipayPublicKey(configFromDb.getAlipayPublicKey());
//            log.info("✅ 使用数据库配置 - AppId: {}", configFromDb.getAppId());
//        } else {
//            // 兜底配置
//            log.error("⚠️ 数据库中未找到支付宝配置");
//            throw new RuntimeException("支付宝配置未找到,请先在数据库中配置支付宝参数");
//        }
//
//        return alipayConfig;
//    }
//    /**
//     * 空接口
//     */
//
//    @GetMapping("/tisheng")
//    public Result<Object> empty() throws AlipayApiException {
//        log.info("开始提上传版本并提审");
// // 初始化SDK - 使用统一配置类
//        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());
//
//        // 构造请求参数以调用接口
//        AlipayOpenMiniVersionUploadRequest request = new AlipayOpenMiniVersionUploadRequest();
//        AlipayOpenMiniVersionUploadModel model = new AlipayOpenMiniVersionUploadModel();
//
//        // 设置小程序模板版本号
//        model.setTemplateVersion("0.0.21");
//
//        // 设置小程序模板 APPID
//        model.setTemplateId("2021005162607623");
//
//        // 设置商家小程序版本号
//        model.setAppVersion("0.0.1");
//
//        // 设置小程序投放的端参数
//        model.setBundleId("com.alipay.alipaywallet");
//
//        request.setBizModel(model);
//        // 第三方代调用模式下请设置app_auth_token - 使用统一配置类
//        request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
//
//        try {
//            AlipayOpenMiniVersionUploadResponse response = alipayClient.execute(request);
//            if (response.isSuccess()){
//                log.info("提审成功: {}", response.getBody());
//                return Result.success("小程序版本上传成功");
//
//            }else{
//                log.error("提审失败: {}", response.getBody());
//            }
//        } catch (AlipayApiException e) {
//            log.error("提审失败: {}", e.getErrMsg());
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//}
