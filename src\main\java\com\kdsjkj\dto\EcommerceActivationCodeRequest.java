package com.kdsjkj.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 电商激活码请求DTO
 */
@Data
public class EcommerceActivationCodeRequest {
    
    /**
     * 激活码名称
     */
    @NotBlank(message = "激活码不能为空")
    private String activeCode;

    /**
     * 电商激活码链接
     */
    @NotBlank(message = "激活码链接不能为空")
    private String activationLink;

    /**
     * 状态：1-有效 0-无效
     */
    @NotNull(message = "状态不能为空")
    private Boolean status;
} 