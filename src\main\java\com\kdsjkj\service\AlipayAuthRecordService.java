package com.kdsjkj.service;

import com.kdsjkj.entity.AlipayAuthRecord;
 
public interface AlipayAuthRecordService {
    void saveAuthRecord(AlipayAuthRecord record);
    
    /**
     * 根据授权小程序APPID查询授权令牌
     * @param authAppId 授权小程序APPID
     * @return 授权令牌
     */
    String getAppAuthTokenByAuthAppId(String authAppId);
    
    /**
     * 根据授权小程序APPID查询用户ID
     * @param authAppId 授权小程序APPID
     * @return 用户ID
     */
    String getUserIdByAuthAppId(String authAppId);
} 