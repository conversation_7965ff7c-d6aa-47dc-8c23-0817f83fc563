//package com.kdsjkj.appTest.xiaochengxu.构建体验;
//
//import com.alipay.api.AlipayApiException;
//import com.alipay.api.AlipayClient;
//import com.alipay.api.DefaultAlipayClient;
//import com.alipay.api.diagnosis.DiagnosisUtils;
//import com.alipay.api.request.AlipayOpenMiniVersionUploadRequest;
//import com.alipay.api.request.AlipayOpenMiniExperienceCreateRequest;
//import com.alipay.api.request.AlipayOpenMiniExperienceQueryRequest;
//import com.alipay.api.domain.AlipayOpenMiniVersionUploadModel;
//import com.alipay.api.domain.AlipayOpenMiniExperienceCreateModel;
//import com.alipay.api.domain.AlipayOpenMiniExperienceQueryModel;
//import com.alipay.api.response.AlipayOpenMiniVersionUploadResponse;
//import com.alipay.api.response.AlipayOpenMiniExperienceCreateResponse;
//import com.alipay.api.response.AlipayOpenMiniExperienceQueryResponse;
//import com.kdsjkj.config.AlipayMiniProgramConfig;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//
///**
// * 支付宝小程序从构建到体验版本的完整流程
// * 支持传入参数：小程序版本号、模板版本号
// */
//public class MiniProgramBuildAndExperience {
//
//    private static final String DEFAULT_TEMPLATE_ID = "2021005162607623";
//    private static final String DEFAULT_BUNDLE_ID = "com.alipay.alipaywallet";
//
//    public static void main(String[] args) {
//        // 参数处理
//        String appVersion = "0.0.1";
//        String templateVersion = "0.0.12";
//
//        if (args.length >= 1) {
//            appVersion = args[0];
//        }
//        if (args.length >= 2) {
//            templateVersion = args[1];
//        }
//
//        System.out.println("=== 支付宝小程序构建到体验版本流程 ===");
//        System.out.println("小程序版本号: " + appVersion);
//        System.out.println("模板版本号: " + templateVersion);
//        System.out.println("模板ID: " + DEFAULT_TEMPLATE_ID);
//        System.out.println("======================================");
//
//        MiniProgramBuildAndExperience processor = new MiniProgramBuildAndExperience();
//        processor.buildAndCreateExperience(appVersion, templateVersion);
//    }
//
//    /**
//     * 执行完整的构建到体验版本流程
//     * @param appVersion 小程序版本号
//     * @param templateVersion 模板版本号
//     */
//    public void buildAndCreateExperience(String appVersion, String templateVersion) {
//        try {
//            // 步骤1: 上传版本(构建)
//            System.out.println("\n步骤1: 开始上传版本构建...");
//            boolean uploadSuccess = uploadVersion(appVersion, templateVersion);
//
//            if (!uploadSuccess) {
//                System.out.println("❌ 版本上传失败，终止流程");
//                return;
//            }
//
//            // 等待构建完成
//            System.out.println("⏳ 等待构建完成...");
//            waitForBuild();
//
//            // 步骤2: 创建体验版本
//            System.out.println("\n步骤2: 开始创建体验版本...");
//            boolean experienceSuccess = createExperience(appVersion);
//
//            if (experienceSuccess) {
//                System.out.println("✅ 体验版本创建成功");
//
//                // 步骤3: 查询体验版本状态
//                System.out.println("\n步骤3: 查询体验版本状态...");
//                queryExperienceStatus(appVersion);
//
//                System.out.println("\n🎉 完整流程执行成功！");
//                System.out.println("✅ 版本构建完成");
//                System.out.println("✅ 体验版本创建完成");
//                System.out.println("✅ 体验状态查询完成");
//                System.out.println("\n💡 提示：您现在可以使用上面的URL进行体验测试");
//            } else {
//                System.out.println("\n⚠️ 体验版本创建失败，但版本构建已完成");
//            }
//
//        } catch (Exception e) {
//            System.err.println("❌ 流程执行过程中发生异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 上传版本(构建)
//     * @param appVersion 小程序版本号
//     * @param templateVersion 模板版本号
//     * @return 是否成功
//     */
//    private boolean uploadVersion(String appVersion, String templateVersion) {
//        try {
//            // 初始化SDK
//            AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());
//
//            // 构造请求参数
//            AlipayOpenMiniVersionUploadRequest request = new AlipayOpenMiniVersionUploadRequest();
//            AlipayOpenMiniVersionUploadModel model = new AlipayOpenMiniVersionUploadModel();
//
//            // 设置参数
//            model.setTemplateVersion(templateVersion);
//            model.setTemplateId(DEFAULT_TEMPLATE_ID);
//            model.setAppVersion(appVersion);
//            model.setBundleId(DEFAULT_BUNDLE_ID);
//
//            request.setBizModel(model);
//            request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
//
//            // 执行请求
//            System.out.println("📤 发送版本上传请求...");
//            AlipayOpenMiniVersionUploadResponse response = alipayClient.execute(request);
//
//            System.out.println("📥 服务器响应:");
//            System.out.println(response.getBody());
//
//            if (response.isSuccess()) {
//                System.out.println("✅ 版本上传请求成功");
//                return true;
//            } else {
//                System.out.println("❌ 版本上传失败");
//                System.out.println("错误码: " + response.getCode());
//                System.out.println("错误信息: " + response.getMsg());
//
//                // 获取诊断链接
//                try {
//                    String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
//                    System.out.println("🔍 诊断链接: " + diagnosisUrl);
//                } catch (Exception e) {
//                    System.out.println("⚠️ 无法获取诊断链接");
//                }
//                return false;
//            }
//
//        } catch (AlipayApiException e) {
//            System.err.println("❌ 版本上传API调用异常: " + e.getMessage());
//            return false;
//        }
//    }
//
//    /**
//     * 创建体验版本
//     * @param appVersion 小程序版本号
//     * @return 是否成功
//     */
//    private boolean createExperience(String appVersion) {
//        try {
//            // 初始化SDK
//            AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());
//
//            // 构造请求参数
//            AlipayOpenMiniExperienceCreateRequest request = new AlipayOpenMiniExperienceCreateRequest();
//            AlipayOpenMiniExperienceCreateModel model = new AlipayOpenMiniExperienceCreateModel();
//
//            // 设置参数
//            model.setAppVersion(appVersion);
//            model.setBundleId(DEFAULT_BUNDLE_ID);
//
//            request.setBizModel(model);
//            request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
//
//            // 执行请求
//            System.out.println("📤 发送体验版本创建请求...");
//            AlipayOpenMiniExperienceCreateResponse response = alipayClient.execute(request);
//
//            System.out.println("📥 服务器响应:");
//            System.out.println(response.getBody());
//
//            if (response.isSuccess()) {
//                System.out.println("✅ 体验版本创建成功");
//                return true;
//            } else {
//                System.out.println("❌ 体验版本创建失败");
//                System.out.println("错误码: " + response.getCode());
//                System.out.println("错误信息: " + response.getMsg());
//
//                // 获取诊断链接
//                try {
//                    String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
//                    System.out.println("🔍 诊断链接: " + diagnosisUrl);
//                } catch (Exception e) {
//                    System.out.println("⚠️ 无法获取诊断链接");
//                }
//                return false;
//            }
//
//        } catch (AlipayApiException e) {
//            System.err.println("❌ 体验版本创建API调用异常: " + e.getMessage());
//            return false;
//        }
//    }
//
//    /**
//     * 查询体验版本状态
//     * @param appVersion 小程序版本号
//     */
//    private void queryExperienceStatus(String appVersion) {
//        try {
//            // 初始化SDK
//            AlipayClient alipayClient = new DefaultAlipayClient(AlipayMiniProgramConfig.getAlipayConfig());
//
//            // 构造请求参数
//            AlipayOpenMiniExperienceQueryRequest request = new AlipayOpenMiniExperienceQueryRequest();
//            AlipayOpenMiniExperienceQueryModel model = new AlipayOpenMiniExperienceQueryModel();
//
//            // 设置参数
//            model.setAppVersion(appVersion);
//            model.setBundleId(DEFAULT_BUNDLE_ID);
//
//            request.setBizModel(model);
//            request.putOtherTextParam("app_auth_token", AlipayMiniProgramConfig.getAppAuthToken());
//
//            // 执行请求
//            System.out.println("📤 发送体验状态查询请求...");
//            AlipayOpenMiniExperienceQueryResponse response = alipayClient.execute(request);
//
//            System.out.println("📥 服务器响应:");
//            System.out.println();
//            System.out.println(response.getBody());
//            System.out.println();
//            if (response.isSuccess()) {
//                System.out.println("✅ 体验状态查询成功");
//
//                // 解析并提取URL信息
//                extractAndDisplayUrls(response.getBody());
//
//            } else {
//                System.out.println("❌ 体验状态查询失败");
//                System.out.println("错误码: " + response.getCode());
//                System.out.println("错误信息: " + response.getMsg());
//
//                // 获取诊断链接
//                try {
//                    String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
//                    System.out.println("🔍 诊断链接: " + diagnosisUrl);
//                } catch (Exception e) {
//                    System.out.println("⚠️ 无法获取诊断链接");
//                }
//            }
//
//        } catch (AlipayApiException e) {
//            System.err.println("❌ 体验状态查询API调用异常: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 解析响应并提取URL信息
//     * @param responseBody 响应体
//     */
//    private void extractAndDisplayUrls(String responseBody) {
//        try {
//            JSONObject rootNode = JSON.parseObject(responseBody);
//
//            JSONObject responseNode = rootNode.getJSONObject("alipay_open_mini_experience_query_response");
//            if (responseNode != null) {
//                String status = responseNode.getString("status");
//                String expQrCodeUrl = responseNode.getString("exp_qr_code_url");
//                String expSchemaUrl = responseNode.getString("exp_schema_url");
//
//                // 处理空值
//                status = status != null ? status : "未知";
//                expQrCodeUrl = expQrCodeUrl != null ? expQrCodeUrl : "无";
//                expSchemaUrl = expSchemaUrl != null ? expSchemaUrl : "无";
//
//                System.out.println("\n🎯 体验版本信息:");
//                System.out.println("======================================");
//                System.out.println("📊 状态: " + status);
//                System.out.println("🔗 二维码URL: " + expQrCodeUrl);
//                System.out.println("📱 Schema URL: " + expSchemaUrl);
//                System.out.println("======================================");
//
//                // 如果有URL，提供使用说明
//                if (!"无".equals(expQrCodeUrl) || !"无".equals(expSchemaUrl)) {
//                    System.out.println("\n💡 使用说明:");
//                    if (!"无".equals(expQrCodeUrl)) {
//                        System.out.println("• 二维码URL: 可在浏览器中打开查看体验版二维码");
//                    }
//                    if (!"无".equals(expSchemaUrl)) {
//                        System.out.println("• Schema URL: 可在支付宝客户端中直接打开体验版");
//                    }
//                }
//            } else {
//                System.out.println("⚠️ 无法解析响应中的URL信息");
//            }
//
//        } catch (Exception e) {
//            System.err.println("❌ 解析URL信息失败: " + e.getMessage());
//            System.out.println("原始响应: " + responseBody);
//        }
//    }
//
//    /**
//     * 等待构建完成
//     * 实际使用中建议调用构建状态查询接口来确认构建状态
//     */
//    private void waitForBuild() {
//        try {
//            // 简单等待，实际应该调用查询接口确认状态
//            Thread.sleep(3000);
//            System.out.println("⏰ 等待完成，准备创建体验版本");
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            System.out.println("⚠️ 等待被中断");
//        }
//    }
//
//    /**
//     * 便捷方法：使用默认参数执行流程
//     */
//    public static void executeWithDefaults() {
//        System.out.println("使用默认参数执行构建到体验版本流程");
//        MiniProgramBuildAndExperience processor = new MiniProgramBuildAndExperience();
//        processor.buildAndCreateExperience("0.0.1", "0.0.12");
//    }
//
//    /**
//     * 便捷方法：只指定小程序版本号
//     * @param appVersion 小程序版本号
//     */
//    public static void executeWithAppVersion(String appVersion) {
//        System.out.println("指定小程序版本号执行流程: " + appVersion);
//        MiniProgramBuildAndExperience processor = new MiniProgramBuildAndExperience();
//        processor.buildAndCreateExperience(appVersion, "0.0.12");
//    }
//}