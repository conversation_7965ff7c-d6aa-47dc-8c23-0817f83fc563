package com.kdsjkj.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.kdsjkj.config.AlipayMiniProgramConfig;
import com.kdsjkj.entity.AlipayConfigBackend;
import com.kdsjkj.service.IAlipayConfigBackendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;

/**
 * 后台获取代理uid
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*", allowedHeaders = "*")  // 允许跨域访问
public class UserController {
    
    @Autowired
    private IAlipayConfigBackendService alipayConfigBackendService;

    
    /**
     * 后台通过授权码获取用户ID
     * 
     * @param authCode 授权码
     * @return 用户ID和访问令牌
     */
    @PostMapping("/getUserId")
    @ResponseBody
    public Map<String, Object> getUserId(@RequestParam(required = true) String authCode) {
        Map<String, Object> response = new HashMap<>();
        try {
            log.info("收到获取用户ID请求，授权码: {}", authCode);
            
            // 通过授权码获取访问令牌和用户ID
            AlipaySystemOauthTokenResponse tokenResponse = getAccessToken(authCode);
            log.info("tokenResponse: {}", tokenResponse);
            
            if (tokenResponse.isSuccess()) {
                String userId = tokenResponse.getUserId();
                String accessToken = tokenResponse.getAccessToken();
                log.info("获取到用户ID: {}, AccessToken: {}", userId, accessToken);
                
                response.put("success", true);
                response.put("message", "获取用户ID成功");
                response.put("userId", userId);
                response.put("accessToken", accessToken);
            } else {
                // 授权失败
                response.put("success", false);
                response.put("message", "授权失败: " + tokenResponse.getSubMsg());
                log.error("授权失败: {}", tokenResponse.getSubMsg());
            }
        } catch (AlipayApiException e) {
            log.error("处理授权码异常", e);
            response.put("success", false);
            response.put("message", "处理授权码异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            response.put("success", false);
            response.put("message", "系统异常: " + e.getMessage());
        }
        
        return response;
    }


    public AlipaySystemOauthTokenResponse getAccessToken(String authCode) throws AlipayApiException {
        log.info("=== 开始获取访问令牌 ===");
        log.info("授权码: {}", authCode);

        // 使用统一配置类，确保签名配置正确
        AlipayConfig alipayConfig = getAlipayConfig();


        // 构造请求参数
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);
        request.setGrantType("authorization_code");



        log.info("请求参数:");
        log.info("- 授权码: {}", authCode);
        log.info("- 授权类型: authorization_code");


        try {
            // 使用统一配置初始化客户端
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);

            log.info("发送请求到支付宝...");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);

            log.info("支付宝响应:");
            log.info("- 完整响应: {}", response.getBody());
            log.info("- 是否成功: {}", response.isSuccess());
            log.info("- 错误码: {}", response.getCode());
            log.info("- 错误信息: {}", response.getMsg());
            log.info("- 子错误码: {}", response.getSubCode());
            log.info("- 子错误信息: {}", response.getSubMsg());

            if (response.isSuccess()) {
                log.info("✅ 获取访问令牌成功");
                log.info("- 用户ID: {}", response.getUserId());
                log.info("- 访问令牌: {}", response.getAccessToken());
                log.info("- 刷新令牌: {}", response.getRefreshToken());
                return response;
            } else {
                log.error("❌ 获取访问令牌失败");

                // 分析具体错误
                if ("40004".equals(response.getCode())) {
                    log.error("业务处理失败，可能原因:");
                    log.error("1. app_auth_token 无效或过期");
                    log.error("2. 授权码已过期或已使用");
                    log.error("3. 小程序与第三方应用关联不完整");
                } else if ("40001".equals(response.getCode())) {
                    log.error("缺少必选参数");
                } else if ("40002".equals(response.getCode())) {
                    log.error("非法的参数");
                }

                throw new AlipayApiException("获取访问令牌失败: " + response.getSubMsg());
            }

        } catch (AlipayApiException e) {
            log.error("❌ 调用支付宝API异常", e);

            // 检查是否是签名错误
            if (e.getMessage().contains("sign check fail")) {
                log.error("🔧 签名验证失败，可能原因:");
                log.error("1. 应用私钥配置错误");
                log.error("2. 支付宝公钥配置错误");
                log.error("3. 签名类型不匹配");
                log.error("4. 字符编码不正确");
                log.error("5. 小程序未正确继承第三方应用的签名配置");

                log.error("💡 建议解决方案:");
                log.error("1. 检查支付宝开放平台的应用配置");
                log.error("2. 确认小程序已正确关联到第三方应用");
                log.error("3. 验证 app_auth_token 的有效性");
                log.error("4. 重新构建小程序以继承配置");
            }

            throw e;
        }
    }

    private AlipayConfig getAlipayConfig() {
        // 从数据库获取配置
        AlipayConfigBackend configFromDb = alipayConfigBackendService.getLatestConfig();
        
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setFormat("json");
        alipayConfig.setCharset("UTF-8"); 
        alipayConfig.setSignType("RSA2");


        if (configFromDb != null) {
            // 使用数据库中的配置
            alipayConfig.setServerUrl(configFromDb.getServerUrl());
            alipayConfig.setAppId(configFromDb.getAppId());
            alipayConfig.setPrivateKey(configFromDb.getPrivateKey());
            alipayConfig.setAlipayPublicKey(configFromDb.getAlipayPublicKey());
            log.info("✅ 使用数据库配置 - AppId: {}", configFromDb.getAppId());
        } else {
            // 兜底配置
            log.error("⚠️ 数据库中未找到支付宝配置");
            throw new RuntimeException("支付宝配置未找到,请先在数据库中配置支付宝参数");
        }
        
        return alipayConfig;
    }


}


