<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kdsjkj.mapper.AlipayConfigFrontMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kdsjkj.entity.AlipayConfigFront">
        <id column="id" property="id" />
        <result column="server_url" property="serverUrl" />
        <result column="app_id" property="appId" />
        <result column="format" property="format" />
        <result column="charset" property="charset" />
        <result column="sign_type" property="signType" />
        <result column="private_key" property="privateKey" />
        <result column="alipay_public_key" property="alipayPublicKey" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, server_url, app_id, format, charset, sign_type, private_key, alipay_public_key, create_time, update_time
    </sql>

</mapper>
